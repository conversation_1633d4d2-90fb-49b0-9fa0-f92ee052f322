<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الترانزستور كمفتاح</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .simulation-area {
            border: 1px solid #ccc;
            padding: 20px;
            margin-bottom: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .circuit-diagram img {
            max-width: 300px;
            height: auto;
            margin-bottom: 15px;
        }
        .controls label, .status p {
            margin: 8px 0;
        }
        .controls button {
            padding: 10px 15px;
            font-size: 1em;
            cursor: pointer;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            margin-top: 10px;
        }
        .controls button:hover {
            background-color: #0056b3;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            border: 1px dashed #eee;
            width: 80%;
            text-align: center;
        }
        .status .led {
            width: 50px;
            height: 50px;
            background-color: #555; /* LED off */
            border-radius: 50%;
            margin: 10px auto;
            border: 2px solid #333;
            transition: background-color 0.3s ease;
        }
        .status .led.on {
            background-color: #ff4136; /* LED on - red */
            box-shadow: 0 0 15px #ff4136;
        }
        #explanationTable {
            width: 80%;
            margin-top: 20px;
            border-collapse: collapse;
        }
        #explanationTable th, #explanationTable td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        #explanationTable th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <header>
        <h1>تجربة: الترانزستور كمفتاح (NPN)</h1>
    </header>

    <nav>
        <ul>
            <li><a href="index.html">الرئيسية</a></li>
            <li><a href="simulation.html">بدء المحاكاة</a></li>
            <li><a href="NPN Transistor Behavior Explorer.html">سلوك NPN</a></li>
            <li><a href="NPN BJT Operating Modes Explorer.html">أوضاع تشغيل NPN</a></li>
            <li><a href="NPN Transistor Interactive Explorer.html">المستكشف التفاعلي</a></li>
        </ul>
    </nav>

    <main>
        <section id="experiment-intro">
            <h2>مقدمة التجربة</h2>
            <p>في هذه التجربة، سنستكشف كيف يمكن استخدام الترانزستور NPN كمفتاح إلكتروني للتحكم في دائرة أخرى (مثل إضاءة LED). عندما يكون الترانزستور في وضع القطع، يعمل كمفتاح مفتوح، وعندما يكون في وضع التشبع، يعمل كمفتاح مغلق.</p>
        </section>

        <section id="circuit-diagram-workbench-switch" style="padding: 20px; margin-bottom: 20px; border: 1px solid #007bff; background-color: #f0f8ff; border-radius: 8px;">
            <h2>رسم دائرة الترانزستور كمفتاح ومنطقة العمل</h2>
            <div class="circuit-diagram-container" style="text-align: center; margin-bottom: 15px;">
                <p><strong>رسم الدائرة:</strong></p>
                <!-- سيتم استبدال هذا برسم SVG أو صورة للدائرة لاحقًا -->
                <img src="images/placeholder_circuit_transistor_switch.png" alt="رسم دائرة الترانزستور كمفتاح" style="max-width: 400px; border: 1px solid #ccc; padding: 10px; margin-bottom: 10px;">
                 <p style="font-size:0.8em; text-align:center;"><em>(ملاحظة: الرسم التوضيحي أعلاه هو مثال عام. قيم المقاومات والجهد قد تختلف في محاكاتنا الفعلية)</em></p>
            </div>
            <div class="workbench-container" style="border: 1px solid #0056b3; padding: 15px; margin-top: 15px; background-color: #e7f3fe;">
                <p><strong>منطقة العمل (Workbench):</strong></p>
                <p><em>(هنا يمكنك بناء وتعديل دائرة الترانزستور كمفتاح)</em></p>
                <div>
                    <label for="component-select-switch">اختر مكونًا:</label>
                    <select id="component-select-switch">
                        <option value="resistor_base">مقاومة القاعدة (RB)</option>
                        <option value="resistor_collector">مقاومة المجمع (RC) / الحمل</option>
                        <option value="led">LED</option>
                        <option value="transistor_npn">ترانزستور NPN</option>
                        <option value="voltage_source_vcc">مصدر جهد (VCC)</option>
                        <option value="voltage_source_vin">مصدر دخل (Vin)</option>
                        <option value="ground">أرضي</option>
                    </select>
                    <button id="addComponentButtonSwitch">إضافة المكون</button>
                </div>
                <div id="interactive-circuit-drawing-area" style="width: 100%; height: 250px; background-color: #e9ecef; margin-top:10px; border:1px dashed #adb5bd; display:flex; flex-direction: column; align-items:center; justify-content:center;">
                    <p>مساحة رسم دائرة المفتاح (قيد التطوير)</p>
                </div>
            </div>
        </section>

        <section class="simulation-area">
            <h2>محاكاة دائرة المفتاح</h2>
            <div class="circuit-diagram">
                <!-- يمكن استبدال هذا برسم SVG تفاعلي لاحقًا -->
                <img src="https://www.electronicshub.org/wp-content/uploads/2015/01/Transistor-as-a-Switch-Circuit.jpg" alt="دائرة الترانزستور كمفتاح">
                <p style="font-size:0.8em; text-align:center;"><em>(ملاحظة: الرسم التوضيحي أعلاه هو مثال عام. قيم المقاومات والجهد قد تختلف في محاكاتنا)</em></p>
            </div>
            
            <div class="controls">
                <h3>التحكم في المفتاح:</h3>
                <label for="baseVoltage">جهد دخل القاعدة (V<sub>in</sub>): <span id="baseVoltageValue">0</span> V</label>
                <input type="range" id="baseVoltage" min="0" max="5" step="0.1" value="0">
                <button id="toggleSwitchBtn">تبديل حالة المفتاح (تطبيق V<sub>in</sub>)</button>
            </div>

            <div class="status">
                <h3>حالة الدائرة:</h3>
                <p>حالة الترانزستور: <strong id="transistorState">--</strong></p>
                <p>حالة الـ LED:</p>
                <div class="led" id="ledIndicator"></div>
                <p id="ledStatusText">مطفأ</p>
            </div>
        </section>

        <section id="theory">
            <h2>الأساس النظري</h2>
            <p>يعمل الترانزستور كمفتاح بالانتقال بين منطقتي القطع والتشبع:</p>
            <ul>
                <li><strong>في منطقة القطع (Cut-off):</strong> عندما يكون جهد القاعدة (V<sub>B</sub>) أقل من جهد التشغيل (عادة حوالي 0.7 فولت لترانزستور السيليكون)، لا يمر تيار يذكر عبر القاعدة (I<sub>B</sub> ≈ 0). وبالتالي، لا يمر تيار كبير عبر المجمع (I<sub>C</sub> ≈ 0). يعمل الترانزستور كـ<strong>مفتاح مفتوح</strong>، والـ LED (أو الحمل) لا يعمل.</li>
                <li><strong>في منطقة التشبع (Saturation):</strong> عندما يتم تطبيق جهد كافٍ على القاعدة لجعل تيار القاعدة (I<sub>B</sub>) كبيرًا بما فيه الكفاية، يدخل الترانزستور في منطقة التشبع. في هذه الحالة، يصبح جهد المجمع-الباعث (V<sub>CE</sub>) صغيرًا جدًا (V<sub>CE,sat</sub> ≈ 0.2 فولت). يمر تيار كبير عبر المجمع (I<sub>C</sub>)، ويعمل الترانزستور كـ<strong>مفتاح مغلق</strong>، مما يسمح للـ LED (أو الحمل) بالعمل.</li>
            </ul>
            <p>مقاومة القاعدة (R<sub>B</sub>) تحد من تيار القاعدة، ومقاومة المجمع (R<sub>C</sub>) (أو مقاومة الحمل مثل LED مع مقاومته التسلسلية) تحد من تيار المجمع.</p>
            
            <h3>جدول ملخص الحالات:</h3>
            <table id="explanationTable">
                <thead>
                    <tr>
                        <th>جهد الدخل (V<sub>in</sub>) للقاعدة</th>
                        <th>تيار القاعدة (I<sub>B</sub>)</th>
                        <th>منطقة عمل الترانزستور</th>
                        <th>تيار المجمع (I<sub>C</sub>)</th>
                        <th>جهد المجمع-الباعث (V<sub>CE</sub>)</th>
                        <th>حالة المفتاح</th>
                        <th>حالة الـ LED</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>منخفض (مثلاً 0 فولت)</td>
                        <td>قريب من الصفر</td>
                        <td>القطع</td>
                        <td>قريب من الصفر</td>
                        <td>مرتفع (قريب من V<sub>CC</sub>)</td>
                        <td>مفتوح</td>
                        <td>مطفأ</td>
                    </tr>
                    <tr>
                        <td>مرتفع (مثلاً 5 فولت)</td>
                        <td>كافٍ للتشبع</td>
                        <td>التشبع</td>
                        <td>مرتفع (محدود بـ R<sub>C</sub> و V<sub>CE,sat</sub>)</td>
                        <td>منخفض (V<sub>CE,sat</sub>)</td>
                        <td>مغلق</td>
                        <td>مضاء</td>
                    </tr>
                </tbody>
            </table>
        </section>

    </main>

    <footer>
        <p>&copy; 2024 معمل الترانزستور الافتراضي. جميع الحقوق محفوظة.</p>
    </footer>

<script src="js/main.js" defer></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const addButtonSwitch = document.getElementById('addComponentButtonSwitch');
            const componentSelectSwitch = document.getElementById('component-select-switch');

            if (addButtonSwitch && componentSelectSwitch) {
                addButtonSwitch.addEventListener('click', function() {
                    const selectedComponent = componentSelectSwitch.value;
                    if (typeof addComponent === 'function') {
                        addComponent(selectedComponent);
                    } else {
                        console.error('addComponent function is not defined. Make sure js/main.js is loaded.');
                    }
                });
            }

            // Existing script for switch simulation
            const baseVoltageSlider = document.getElementById('baseVoltage');
            const baseVoltageValueSpan = document.getElementById('baseVoltageValue');
            const toggleSwitchBtn = document.getElementById('toggleSwitchBtn');
            const transistorStateSpan = document.getElementById('transistorState');
            const ledIndicatorDiv = document.getElementById('ledIndicator');
            const ledStatusTextSpan = document.getElementById('ledStatusText');

            // قيم الدائرة النموذجية (يمكن تعديلها)
            const VCC = 5;      // جهد المصدر للـ LED (فولت)
            const VBE_ON = 0.7; // جهد تشغيل القاعدة-الباعث (فولت)
            const VCE_SAT = 0.2;// جهد تشبع المجمع-الباعث (فولت)
            // RB و RC غير مستخدمين مباشرة في هذا المنطق المبسط، لكنهما مهمان في التصميم الحقيقي

            function updateSwitchState(vin) {
                baseVoltageValueSpan.textContent = vin.toFixed(1);
                let state = '';
                let ledOn = false;

                if (vin < VBE_ON) {
                    state = 'القطع (مفتوح)';
                    ledOn = false;
                    ledIndicatorDiv.classList.remove('on');
                    ledStatusTextSpan.textContent = 'مطفأ';
                } else {
                    // نفترض أن vin كافٍ لإدخال الترانزستور في التشبع إذا تجاوز VBE_ON
                    // في الواقع، يعتمد على RB و BETA و RC
                    state = 'التشبع (مغلق)';
                    ledOn = true;
                    ledIndicatorDiv.classList.add('on');
                    ledStatusTextSpan.textContent = 'مضاء';
                }
                transistorStateSpan.textContent = state;
            }

            baseVoltageSlider.addEventListener('input', function() {
                updateSwitchState(parseFloat(this.value));
            });
            
            toggleSwitchBtn.addEventListener('click', function() {
                // هذا الزر يمكن أن يضبط قيمة Slider إلى قيمة محددة تمثل "تشغيل"
                // أو يمكن أن يبدل بين قيمتين محددتين (0 و 5 فولت مثلاً)
                let currentVin = parseFloat(baseVoltageSlider.value);
                if (currentVin < VCC/2) { // إذا كان الجهد منخفضًا، اجعله مرتفعًا
                    baseVoltageSlider.value = VCC;
                } else { // إذا كان الجهد مرتفعًا، اجعله منخفضًا
                    baseVoltageSlider.value = 0;
                }
                // تحديث الواجهة بناءً على القيمة الجديدة للمن ползунок
                updateSwitchState(parseFloat(baseVoltageSlider.value));
            });

            // تحديث الحالة الأولية عند تحميل الصفحة
            updateSwitchState(parseFloat(baseVoltageSlider.value));
        });
    </script>
</body>
</html>