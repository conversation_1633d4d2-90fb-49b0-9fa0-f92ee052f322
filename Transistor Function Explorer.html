<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transistor Basics Explorer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f0f2f5; /* Softer background */
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        h1 {
            color: #004080; /* Dark blue for main title */
            margin-bottom: 30px;
        }
        .app-container {
            display: flex;
            flex-wrap: wrap; /* Allow wrapping on smaller screens */
            justify-content: center;
            gap: 30px; /* Spacing between NPN and PNP sections */
            width: 100%;
            max-width: 720px; /* Adjusted Max width for the two sections side-by-side */
        }
        .transistor-section {
            background-color: #fff;
            padding: 20px;
            border-radius: 10px; /* Slightly more rounded corners */
            box-shadow: 0 4px 12px rgba(0,0,0,0.1); /* Softer shadow */
            text-align: center;
            width: calc(50% - 45px); /* (720 - 30gap)/2 - 2*20padding = 305. section width = 300 + 2*20 padding = 340. (720-30)/2 = 345.  */
            min-width: 300px; /* Minimum width before wrapping */
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .transistor-section h2 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #0056b3; /* Consistent with title theme */
        }
        canvas {
            display: block;
            margin: 10px auto 15px auto; /* Adjusted margin */
            border: 1px solid #d1d9e6; /* Lighter border */
            border-radius: 6px;
            /* The canvas element itself is made responsive, browser scales the fixed-res drawing */
            width: 100%; /* Make canvas element fill its container part */
            max-width: 200px; /* Intrinsic drawing width */
            height: auto; /* Maintain aspect ratio based on width attribute */
        }
        .controls {
            margin-top: 10px;
            width: 100%;
            max-width: 280px; /* Limit width of controls area */
        }
        .controls label {
            display: block;
            margin-bottom: 8px; /* Increased margin */
            font-weight: bold;
            color: #495057; /* Darker gray for label text */
        }
        .controls input[type="range"] {
            width: 90%; /* Wider slider */
            max-width: 250px;
            margin-bottom: 15px; /* Increased margin */
            cursor: pointer;
        }
        .controls p {
            margin: 10px 0; /* Increased margin */
            font-size: 0.98em; /* Slightly larger text */
            color: #343a40; /* Darker gray for paragraph text */
        }
        .controls p span {
            font-weight: bold;
            padding: 2px 6px; /* Padding for state badge */
            border-radius: 4px; /* Rounded corners for badge */
            color: #fff; /* White text for badges */
        }
        .controls p span.cutoff { background-color: #6c757d; } /* Bootstrap Gray */
        .controls p span.active { background-color: #28a745; } /* Bootstrap Success Green */
        .controls p span.saturation { background-color: #ffc107; color: #212529; } /* Bootstrap Warning Yellow, dark text */
        
        /* Responsive adjustments */
        @media (max-width: 690px) { /* Approx 300px min-width * 2 + gap */
            .app-container {
                flex-direction: column;
                align-items: center;
            }
            .transistor-section {
                width: 100%;
                max-width: 380px; /* Allow sections to be wider when stacked */
                margin-bottom: 25px;
            }
            .transistor-section:last-child {
                margin-bottom: 0;
            }
        }
    </style>
</head>
<body>

    <h1>Transistor Function Explorer</h1>

    <div class="app-container">
        <div class="transistor-section" id="npn-section">
            <h2>NPN Transistor</h2>
            <canvas id="npnCanvas" width="200" height="250"></canvas>
            <div class="controls">
                <label for="npnIbSlider">Base Current (I<sub>b</sub>): <span id="npnIbValue">0</span>%</label>
                <input type="range" id="npnIbSlider" min="0" max="100" value="0" step="1">
                <p>State: <span id="npnState" class="cutoff">Cut-off</span></p>
                <p>Collector Current (I<sub>c</sub>): <span id="npnIcValue">0.0</span> units</p>
            </div>
        </div>

        <div class="transistor-section" id="pnp-section">
            <h2>PNP Transistor</h2>
            <canvas id="pnpCanvas" width="200" height="250"></canvas>
            <div class="controls">
                <label for="pnpIbSlider">Base Current (I<sub>b</sub>): <span id="pnpIbValue">0</span>%</label>
                <input type="range" id="pnpIbSlider" min="0" max="100" value="0" step="1">
                <p>State: <span id="pnpState" class="cutoff">Cut-off</span></p>
                <p>Collector Current (I<sub>c</sub>): <span id="pnpIcValue">0.0</span> units</p>
            </div>
        </div>
    </div>

    <script>
        const MAX_IC_DISPLAY = 100; // Arbitrary units for Ic display
        const NPN_FLOW_COLOR = 'rgba(0, 100, 255, 0.9)'; 
        const PNP_FLOW_COLOR = 'rgba(255, 50, 50, 0.9)';  
        const SYMBOL_COLOR = '#333';
        const LABEL_COLOR = '#444'; // Slightly darker label color
        const CIRCLE_BORDER_COLOR = '#b0bec5'; // Softer circle border
        const BASE_CURRENT_VIS_COLOR_NPN = NPN_FLOW_COLOR;
        const BASE_CURRENT_VIS_COLOR_PNP = PNP_FLOW_COLOR;

        const npnCanvas = document.getElementById('npnCanvas');
        const npnCtx = npnCanvas.getContext('2d');
        const pnpCanvas = document.getElementById('pnpCanvas');
        const pnpCtx = pnpCanvas.getContext('2d');

        const npnIbSlider = document.getElementById('npnIbSlider');
        const npnIbValueDisplay = document.getElementById('npnIbValue');
        const npnStateDisplay = document.getElementById('npnState');
        const npnIcValueDisplay = document.getElementById('npnIcValue');

        const pnpIbSlider = document.getElementById('pnpIbSlider');
        const pnpIbValueDisplay = document.getElementById('pnpIbValue');
        const pnpStateDisplay = document.getElementById('pnpState');
        const pnpIcValueDisplay = document.getElementById('pnpIcValue');

        const canvasWidth = 200; // Canvas drawing resolution width
        const canvasHeight = 250; // Canvas drawing resolution height
        const centerX = canvasWidth / 2;
        const circleCenterY = 105; // Adjusted for better vertical centering with labels
        const radius = 60; 
        const symbolLineWidth = 2;
        const labelFont = 'bold 13px Arial'; // Slightly bolder and smaller labels
        const terminalLabelOffset = 10; // Offset for C, B, E labels from leads

        function drawArrowhead(ctx, fromX, fromY, toX, toY, headLength, color) {
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.save(); // Save context state
            ctx.strokeStyle = color;
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headLength * Math.cos(angle - Math.PI / 6), toY - headLength * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(toX - headLength * Math.cos(angle + Math.PI / 6), toY - headLength * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fill();
            ctx.restore(); // Restore context state
        }
        
        function drawTransistor(ctx, type, ibPercent, icValue) {
            ctx.clearRect(0, 0, canvasWidth, canvasHeight);
            
            // Symbol drawing parameters
            const baseExtX = centerX - radius + 5; 
            const baseExtY = circleCenterY;
            const baseIntX = centerX - radius * 0.25; 
            const baseIntY = circleCenterY;

            const junctionTopY = circleCenterY - radius * 0.3;
            const junctionBottomY = circleCenterY + radius * 0.3;
            
            let collectorExtX, collectorExtY, emitterExtX, emitterExtY;
            let collectorLabelX, collectorLabelY, emitterLabelX, emitterLabelY;
            let baseLabelX, baseLabelY;

            ctx.lineWidth = symbolLineWidth;
            ctx.strokeStyle = SYMBOL_COLOR;
            ctx.fillStyle = SYMBOL_COLOR;

            if (type === 'NPN') { // C top, E bottom
                collectorExtX = centerX;
                collectorExtY = circleCenterY - radius + 5;
                emitterExtX = centerX;
                emitterExtY = circleCenterY + radius - 5;

                collectorLabelX = collectorExtX;
                collectorLabelY = collectorExtY - terminalLabelOffset;
                emitterLabelX = emitterExtX;
                emitterLabelY = emitterExtY + terminalLabelOffset + 3; // +3 for better spacing for 'E'
                baseLabelX = baseExtX - terminalLabelOffset;
                baseLabelY = baseExtY + 4; // Vertically center 'B'

                // Draw Collector lead
                ctx.beginPath();
                ctx.moveTo(collectorExtX, collectorExtY);
                ctx.lineTo(baseIntX, junctionTopY);
                ctx.stroke();

                // Draw Emitter lead
                ctx.beginPath();
                ctx.moveTo(emitterExtX, emitterExtY);
                ctx.lineTo(baseIntX, junctionBottomY);
                ctx.stroke();
                // NPN Arrow (on Emitter lead, pointing out)
                const arrowEmitterFromX = baseIntX;
                const arrowEmitterFromY = junctionBottomY;
                const arrowEmitterToX = emitterExtX;
                const arrowEmitterToY = emitterExtY;
                const arrowMidX_NPN = arrowEmitterFromX + (arrowEmitterToX - arrowEmitterFromX) * 0.55; // Position arrow
                const arrowMidY_NPN = arrowEmitterFromY + (arrowEmitterToY - arrowEmitterFromY) * 0.55;
                drawArrowhead(ctx, arrowEmitterFromX, arrowEmitterFromY, arrowMidX_NPN, arrowMidY_NPN, 8, SYMBOL_COLOR);

            } else { // PNP: E top, C bottom
                emitterExtX = centerX;
                emitterExtY = circleCenterY - radius + 5;
                collectorExtX = centerX;
                collectorExtY = circleCenterY + radius - 5;

                emitterLabelX = emitterExtX;
                emitterLabelY = emitterExtY - terminalLabelOffset;
                collectorLabelX = collectorExtX;
                collectorLabelY = collectorExtY + terminalLabelOffset + 3;
                baseLabelX = baseExtX - terminalLabelOffset;
                baseLabelY = baseExtY + 4;

                // Draw Emitter lead
                ctx.beginPath();
                ctx.moveTo(emitterExtX, emitterExtY);
                ctx.lineTo(baseIntX, junctionTopY);
                ctx.stroke();
                // PNP Arrow (on Emitter lead, pointing in)
                const arrowEmitterToX = baseIntX;
                const arrowEmitterToY = junctionTopY;
                const arrowEmitterFromX = emitterExtX;
                const arrowEmitterFromY = emitterExtY;
                const arrowMidX_PNP = arrowEmitterFromX + (arrowEmitterToX - arrowEmitterFromX) * 0.45; // Position arrow
                const arrowMidY_PNP = arrowEmitterFromY + (arrowEmitterToY - arrowEmitterFromY) * 0.45;
                drawArrowhead(ctx, arrowEmitterFromX, arrowEmitterFromY, arrowMidX_PNP, arrowMidY_PNP, 8, SYMBOL_COLOR);
                
                // Draw Collector lead
                ctx.beginPath();
                ctx.moveTo(collectorExtX, collectorExtY);
                ctx.lineTo(baseIntX, junctionBottomY);
                ctx.stroke();
            }

            // Draw Base lead
            ctx.beginPath();
            ctx.moveTo(baseExtX, baseExtY);
            ctx.lineTo(baseIntX, baseIntY);
            ctx.stroke();

            // Draw internal vertical bar (Base region representation)
            ctx.beginPath();
            ctx.moveTo(baseIntX, junctionTopY);
            ctx.lineTo(baseIntX, junctionBottomY);
            ctx.stroke();
            
            // Draw Circle
            ctx.beginPath();
            ctx.arc(centerX, circleCenterY, radius, 0, 2 * Math.PI);
            ctx.strokeStyle = CIRCLE_BORDER_COLOR; 
            ctx.stroke();
            
            // Draw Labels
            ctx.font = labelFont;
            ctx.fillStyle = LABEL_COLOR;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle'; // Align text vertically for labels
            ctx.fillText(type === 'NPN' ? 'C' : 'E', collectorLabelX, collectorLabelY);
            ctx.fillText(type === 'NPN' ? 'E' : 'C', emitterLabelX, emitterLabelY);
            ctx.textAlign = 'right';
            ctx.fillText('B', baseLabelX, baseLabelY);
            ctx.textAlign = 'left'; // Reset for any subsequent text

            // --- Current Flow Visualization ---
            const icNormalized = icValue / MAX_IC_DISPLAY;
            const minFlowWidth = 1; // Min visible width when active
            const maxFlowWidth = 14; 
            let currentFlowWidth = 0;
            if (icValue > 0) {
                 currentFlowWidth = minFlowWidth + icNormalized * (maxFlowWidth - minFlowWidth);
            }


            if (currentFlowWidth > 0) {
                const flowColor = type === 'NPN' ? NPN_FLOW_COLOR : PNP_FLOW_COLOR;
                
                // Main current path (C-E for NPN, E-C for PNP)
                const flowPathStartY = circleCenterY - radius * 0.55; // Adjusted to be within circle more
                const flowPathEndY = circleCenterY + radius * 0.55;
                
                ctx.beginPath();
                ctx.moveTo(centerX, flowPathStartY);
                ctx.lineTo(centerX, flowPathEndY);
                ctx.lineWidth = currentFlowWidth;
                ctx.strokeStyle = flowColor;
                ctx.lineCap = 'round'; // Smoother ends for flow line
                ctx.stroke();
                drawArrowhead(ctx, centerX, flowPathStartY, centerX, flowPathEndY, 10, flowColor);

                // Base current visualization (small arrow)
                const baseFlowLineWidth = Math.max(1.5, currentFlowWidth * 0.25);
                ctx.lineWidth = baseFlowLineWidth;
                ctx.strokeStyle = type === 'NPN' ? BASE_CURRENT_VIS_COLOR_NPN : BASE_CURRENT_VIS_COLOR_PNP;
                const baseFlowArrowLength = 7;
                const baseVisOffset = 18; // How far from the base terminal lead
                const baseVisLength = 15; // Length of the base current indicator line

                if (type === 'NPN') { // Ib into Base
                    ctx.beginPath();
                    ctx.moveTo(baseExtX - baseVisOffset, baseExtY);
                    ctx.lineTo(baseExtX - baseVisOffset + baseVisLength, baseExtY);
                    ctx.stroke();
                    drawArrowhead(ctx, baseExtX - baseVisOffset, baseExtY, baseExtX - baseVisOffset + baseVisLength, baseExtY, baseFlowArrowLength, BASE_CURRENT_VIS_COLOR_NPN);
                } else { // Ib out of Base
                     ctx.beginPath();
                    ctx.moveTo(baseExtX - baseVisOffset + baseVisLength, baseExtY);
                    ctx.lineTo(baseExtX - baseVisOffset, baseExtY);
                    ctx.stroke();
                    drawArrowhead(ctx, baseExtX - baseVisOffset + baseVisLength, baseExtY, baseExtX - baseVisOffset, baseExtY, baseFlowArrowLength, BASE_CURRENT_VIS_COLOR_PNP);
                }
                ctx.lineCap = 'butt'; // Reset lineCap
            }
        }

        function updateNPN() {
            const ibPercent = parseInt(npnIbSlider.value);
            npnIbValueDisplay.textContent = ibPercent;

            let icNPN = 0;
            let stateNPN = "Cut-off";
            let stateClassNPN = "cutoff";

            if (ibPercent === 0) {
                icNPN = 0;
            } else if (ibPercent === 100) {
                icNPN = MAX_IC_DISPLAY;
                stateNPN = "Saturation";
                stateClassNPN = "saturation";
            } else { 
                icNPN = (ibPercent / 100) * MAX_IC_DISPLAY;
                stateNPN = "Active";
                stateClassNPN = "active";
            }
            
            npnIcValueDisplay.textContent = icNPN.toFixed(1);
            npnStateDisplay.textContent = stateNPN;
            npnStateDisplay.className = stateClassNPN;

            drawTransistor(npnCtx, 'NPN', ibPercent, icNPN);
        }

        function updatePNP() {
            const ibPercent = parseInt(pnpIbSlider.value);
            pnpIbValueDisplay.textContent = ibPercent;

            let icPNP = 0;
            let statePNP = "Cut-off";
            let stateClassPNP = "cutoff";

            if (ibPercent === 0) {
                icPNP = 0;
            } else if (ibPercent === 100) {
                icPNP = MAX_IC_DISPLAY;
                statePNP = "Saturation";
                stateClassPNP = "saturation";
            } else { 
                icPNP = (ibPercent / 100) * MAX_IC_DISPLAY;
                statePNP = "Active";
                stateClassPNP = "active";
            }

            pnpIcValueDisplay.textContent = icPNP.toFixed(1);
            pnpStateDisplay.textContent = statePNP;
            pnpStateDisplay.className = stateClassPNP;
            
            drawTransistor(pnpCtx, 'PNP', ibPercent, icPNP);
        }

        npnIbSlider.addEventListener('input', updateNPN);
        pnpIbSlider.addEventListener('input', updatePNP);

        window.addEventListener('load', () => {
            updateNPN();
            updatePNP();
        });
    </script>

</body>
</html>
