<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Transistor Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f9; /* Light grayish blue */
            color: #333;
            display: flex;
            justify-content: center;
        }
        .app-container {
            max-width: 900px;
            width:100%;
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #005A9C; /* Professional blue */
            margin-bottom: 10px;
        }
        .app-description {
            text-align: center;
            margin-bottom: 25px;
            font-size: 0.95em;
            color: #555;
        }
        .main-content {
            display: flex;
            flex-wrap: wrap;
            gap: 25px; /* Increased gap */
            margin-top: 20px;
        }
        .controls-section, .diagram-section {
            padding: 20px; /* Increased padding */
            border: 1px solid #dde3e8; /* Softer border */
            border-radius: 6px; /* Softer radius */
            background-color: #fdfdfd; /* Slightly off-white */
        }
        .controls-section {
            flex: 1;
            min-width: 280px;
        }
        .diagram-section {
            flex: 1.5; /* Give diagram a bit more space if available */
            min-width: 320px; /* Slightly larger min-width */
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .controls-section h2, .diagram-section h2 {
            margin-top:0;
            margin-bottom:20px; /* More space below heading */
            font-size: 1.3em;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .slider-group {
            margin-bottom: 20px; /* Increased margin */
        }
        .slider-group label {
            display: block;
            margin-bottom: 8px; /* More space for label */
            font-weight: 500; /* Medium weight */
            font-size: 0.9em;
            color: #444;
        }
        .slider-group input[type="range"] {
            width: 100%;
            cursor: pointer;
            background: transparent; /* Needed for some browsers to style track */
        }
        /* Custom slider track and thumb styles */
        input[type=range]::-webkit-slider-runnable-track {
            width: 100%;
            height: 8px;
            cursor: pointer;
            background: #dde3e8;
            border-radius: 4px;
        }
        input[type=range]::-moz-range-track {
            width: 100%;
            height: 8px;
            cursor: pointer;
            background: #dde3e8;
            border-radius: 4px;
        }
        input[type=range]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            background: #007bff; /* Bootstrap primary blue */
            cursor: pointer;
            border-radius: 50%;
            margin-top: -5px; /* Center thumb on track */
        }
        input[type=range]::-moz-range-thumb {
            width: 18px;
            height: 18px;
            background: #007bff;
            cursor: pointer;
            border-radius: 50%;
            border: none;
        }
        .slider-group span { /* Value display */
            font-weight: bold;
            color: #007bff;
        }
        .results-section {
            margin-top: 25px; /* Increased gap */
            padding: 20px; /* Increased padding */
            border: 1px solid #dde3e8; /* Softer border */
            border-radius: 6px;
            background-color: #e9f1f6; /* Light blueish background */
            text-align: center;
        }
        .results-section h3 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.3em;
            color: #004085; /* Darker blue for results heading */
        }
        .results-section p {
            font-size: 1.1em; /* Slightly larger font */
            margin-bottom: 8px;
        }
        .results-section p span {
            font-weight: bold;
            color: #005A9C;
        }
        #status-message {
            margin-top: 10px;
            font-weight: 500; /* Medium weight */
            font-size: 1.05em;
            min-height: 1.2em; /* Prevent layout shift */
        }
        #circuit-svg {
            width: 100%;
            max-width: 380px; /* Max width for SVG */
            height: auto;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #fff; /* White background for SVG */
        }
        .current-path {
            stroke-linecap: round;
            stroke-linejoin: round;
            transition: stroke-width 0.15s ease-out, opacity 0.15s ease-out;
        }
        .current-arrow-text {
            font-family: monospace;
            font-weight: bold;
            transition: opacity 0.15s ease-out;
        }

        @media (max-width: 768px) {
            body { padding: 10px; }
            .app-container { padding: 15px; }
            .main-content { flex-direction: column; }
            .controls-section, .diagram-section {
                min-width: calc(100% - 40px); /* Adjust for padding */
                flex-basis: auto; /* Allow natural sizing in column */
            }
            h1 { font-size: 1.6em; }
            .controls-section h2, .diagram-section h2, .results-section h3 { font-size: 1.2em; }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <h1>Transistor Current Controller</h1>
        <p class="app-description">This app demonstrates how an NPN Bipolar Junction Transistor (BJT) can act as a current-controlled current source. Adjust the sliders to see how base current (I<sub>B</sub>), collector supply voltage (V<sub>CC</sub>), and current gain (β) affect the collector current (I<sub>C</sub>).</p>

        <div class="main-content">
            <div class="controls-section">
                <h2>Controls</h2>
                <div class="slider-group">
                    <label for="baseCurrent">Base Current (I<sub>B</sub>): <span id="baseCurrentValue">10</span> µA</label>
                    <input type="range" id="baseCurrent" min="0" max="100" value="10" step="1">
                </div>
                <div class="slider-group">
                    <label for="collectorVoltage">Collector Supply Voltage (V<sub>CC</sub>): <span id="collectorVoltageValue">5.0</span> V</label>
                    <input type="range" id="collectorVoltage" min="0" max="12" value="5" step="0.1">
                </div>
                <div class="slider-group">
                    <label for="currentGain">Current Gain (β): <span id="currentGainValue">100</span></label>
                    <input type="range" id="currentGain" min="10" max="300" value="100" step="1">
                </div>
            </div>

            <div class="diagram-section">
                <h2>Simplified Circuit Diagram (NPN)</h2>
                <svg id="circuit-svg" viewBox="0 0 300 200">
                    <!-- Static parts -->
                    <text x="150" y="12" text-anchor="middle" font-size="9">VCC</text>
                    <line x1="150" y1="15" x2="150" y2="25" stroke="black" stroke-width="1"/>
                    <rect x="142" y="25" width="16" height="20" fill="none" stroke="black" stroke-width="1"/> <!-- RL -->
                    <text x="162" y="37" text-anchor="start" font-size="8">RL (1kΩ)</text>
                    <line x1="150" y1="45" x2="150" y2="55" stroke="black" stroke-width="1"/> <!-- RL to C-Path -->

                    <!-- Transistor Group -->
                    <g transform="translate(150, 80)">
                        <line x1="-25" y1="0" x2="0" y2="0" stroke="black" stroke-width="1.5"/> <!-- Base wire -->
                        <text x="-32" y="3" font-size="9">B</text>
                        <line x1="0" y1="-15" x2="0" y2="15" stroke="black" stroke-width="1.5"/> <!-- Base plate -->
                        
                        <line x1="0" y1="-15" x2="15" y2="-25" stroke="black" stroke-width="1.5"/> <!-- Collector arm -->
                        <line x1="15" y1="-25" x2="15" y2="-30" stroke="black" stroke-width="1.5"/> <!-- C lead -->
                        <text x="15" y="-35" text-anchor="middle" font-size="9">C</text>
                        
                        <line x1="0" y1="15" x2="15" y2="25" stroke="black" stroke-width="1.5"/> <!-- Emitter arm -->
                        <line x1="15" y1="25" x2="15" y2="30" stroke="black" stroke-width="1.5"/> <!-- E lead -->
                        <text x="15" y="40" text-anchor="middle" font-size="9">E</text>
                        <polygon points="0,0 -6,3 -6,-3" fill="black" transform="translate(15,25) rotate(33.69)" /> <!-- Emitter Arrow -->
                    </g>
                    
                    <line x1="150" y1="55" x2="165" y2="50" stroke="black" stroke-width="1"/> <!-- C-Path to C lead -->
                    <line x1="165" y1="110" x2="165" y2="130" stroke="black" stroke-width="1"/> <!-- E lead to GND line -->

                    <!-- Ground -->
                    <line x1="165" y1="130" x2="165" y2="140" stroke="black" stroke-width="1"/>
                    <line x1="150" y1="140" x2="180" y2="140" stroke="black" stroke-width="1"/>
                    <line x1="155" y1="145" x2="175" y2="145" stroke="black" stroke-width="1"/>
                    <line x1="160" y1="150" x2="170" y2="150" stroke="black" stroke-width="1"/>
                    <text x="165" y="162" text-anchor="middle" font-size="9">GND</text>

                    <!-- Base Circuit -->
                    <rect x="72" y="72.5" width="25" height="15" fill="none" stroke="black" stroke-width="1"/> <!-- RB -->
                    <text x="65" y="82" text-anchor="end" font-size="8">RB</text>
                    <line x1="97" y1="80" x2="125" y2="80" stroke="black" stroke-width="1"/> <!-- RB to Base -->
                    <line x1="50" y1="80" x2="72" y2="80" stroke="black" stroke-width="1"/> <!-- VB to RB -->
                    <text x="40" y="83" text-anchor="middle" font-size="8">VB</text>
                    
                    <!-- Current Flow Paths -->
                    <path id="ib-flow" class="current-path" d="M 50 80 L 125 80" fill="none" stroke="#FF0000" stroke-width="1"/>
                    <path id="ic-flow" class="current-path" d="M 150 15 L 150 25 M 150 45 L 150 55 L 165 50" fill="none" stroke="#0000FF" stroke-width="1"/>
                    <path id="ie-flow" class="current-path" d="M 165 110 L 165 130" fill="none" stroke="#008000" stroke-width="1"/>
                    
                    <!-- Current Arrows Text -->
                    <text id="ib-arrow-text" x="120" y="83" class="current-arrow-text" font-size="11" fill="#FF0000" dominant-baseline="middle" text-anchor="end">→</text>
                    <text id="ic-arrow-text" x="153" y="63" class="current-arrow-text" font-size="11" fill="#0000FF" dominant-baseline="middle" text-anchor="start">↓</text>
                    <text id="ie-arrow-text" x="153" y="125" class="current-arrow-text" font-size="11" fill="#008000" dominant-baseline="middle" text-anchor="start">↓</text>
                </svg>
            </div>
        </div>

        <div class="results-section">
            <h3>Calculated Values & Transistor State</h3>
            <p>Collector Current (I<sub>C</sub>): <span id="collectorCurrentDisplay">0.000</span> mA</p>
            <p id="status-message">Device state will be shown here.</p>
        </div>
    </div>

    <script>
        const baseCurrentSlider = document.getElementById('baseCurrent');
        const collectorVoltageSlider = document.getElementById('collectorVoltage');
        const currentGainSlider = document.getElementById('currentGain');

        const baseCurrentValueDisplay = document.getElementById('baseCurrentValue');
        const collectorVoltageValueDisplay = document.getElementById('collectorVoltageValue');
        const currentGainValueDisplay = document.getElementById('currentGainValue');

        const collectorCurrentDisplay = document.getElementById('collectorCurrentDisplay');
        const statusMessageDisplay = document.getElementById('status-message');

        const ibFlowPath = document.getElementById('ib-flow');
        const icFlowPath = document.getElementById('ic-flow');
        const ieFlowPath = document.getElementById('ie-flow');
        
        const ibArrowText = document.getElementById('ib-arrow-text');
        const icArrowText = document.getElementById('ic-arrow-text');
        const ieArrowText = document.getElementById('ie-arrow-text');

        const LOAD_RESISTANCE_KOHM = 1; // 1 kOhm

        function updateApp() {
            const baseCurrent_uA = parseFloat(baseCurrentSlider.value);
            const collectorVoltage_V = parseFloat(collectorVoltageSlider.value);
            const beta = parseFloat(currentGainSlider.value);

            baseCurrentValueDisplay.textContent = baseCurrent_uA;
            collectorVoltageValueDisplay.textContent = collectorVoltage_V.toFixed(1);
            currentGainValueDisplay.textContent = beta;

            const potential_Ic_mA = (beta * baseCurrent_uA) / 1000;
            // max_Ic_mA can be 0 if collectorVoltage_V is 0.
            const max_Ic_mA = (LOAD_RESISTANCE_KOHM > 0) ? (collectorVoltage_V / LOAD_RESISTANCE_KOHM) : Infinity; 
            
            let actual_Ic_mA;
             // If VCC is effectively zero or negative, no collector current can flow from the supply.
            if (collectorVoltage_V < 0.01) {
                 actual_Ic_mA = 0;
            } else {
                 actual_Ic_mA = Math.min(potential_Ic_mA, max_Ic_mA);
            }
            actual_Ic_mA = Math.max(0, actual_Ic_mA); // Ensure non-negative

            collectorCurrentDisplay.textContent = actual_Ic_mA.toFixed(3);

            if (baseCurrent_uA === 0) {
                statusMessageDisplay.textContent = "Cut-off: No base current (Iʙ = 0), so no collector current flows (Iᴄ = 0).";
                statusMessageDisplay.style.color = "#555555"; // Dark Gray
            } else { // baseCurrent_uA > 0
                if (collectorVoltage_V < 0.01) { 
                    statusMessageDisplay.textContent = "Saturation: Vcc is effectively zero. No collector current can flow, even with base current.";
                    statusMessageDisplay.style.color = "#D32F2F"; // Material Red 700
                } else if (potential_Ic_mA >= max_Ic_mA) { 
                    if (collectorVoltage_V < 0.5) { // Threshold for "very low VCC" message variant
                         statusMessageDisplay.textContent = "Saturation: Vcc is very low. Iᴄ is limited by Vcc and Rʟ (Iᴄ ≈ Vcc/Rʟ).";
                    } else {
                         statusMessageDisplay.textContent = "Saturation: Transistor is fully 'on'. Iᴄ is limited by Vcc and Rʟ (Iᴄ ≈ Vcc/Rʟ), not by Iʙ.";
                    }
                    statusMessageDisplay.style.color = "#D32F2F"; // Material Red 700
                } else { 
                    statusMessageDisplay.textContent = "Active Region: Collector current is controlled by base current (Iᴄ = β × Iʙ).";
                    statusMessageDisplay.style.color = "#388E3C"; // Material Green 700
                }
            }
            
            const MAX_VISUAL_STROKE = 6; 
            const MIN_VISUAL_STROKE = 0.8;

            function mapCurrentToStrokeWidth(current_val, max_current_for_scale, min_current_for_display = 0.001) {
                if (current_val < min_current_for_display) return 0; // No visible line for zero/negligible current
                if (max_current_for_scale <= min_current_for_display) return MIN_VISUAL_STROKE; // Avoid division by zero or weird scaling
                
                const normalized_current = Math.max(0, (current_val - min_current_for_display) / (max_current_for_scale - min_current_for_display));
                let stroke = MIN_VISUAL_STROKE + normalized_current * (MAX_VISUAL_STROKE - MIN_VISUAL_STROKE);
                return Math.min(MAX_VISUAL_STROKE, Math.max(MIN_VISUAL_STROKE, stroke));
            }
            
            const ib_max_uA = parseFloat(baseCurrentSlider.max);
            // Max possible Ic is when VCC is max and transistor is saturated.
            const ic_visualization_max_mA = parseFloat(collectorVoltageSlider.max) / LOAD_RESISTANCE_KOHM; 
            const ie_visualization_max_mA = ic_visualization_max_mA + (ib_max_uA / 1000);

            // For Ib, min_current_for_display is 1uA as slider step is 1.
            const ib_stroke = mapCurrentToStrokeWidth(baseCurrent_uA, ib_max_uA, 1); 
            ibFlowPath.style.strokeWidth = ib_stroke;
            ibArrowText.style.opacity = baseCurrent_uA >= 1 ? '1' : '0.1';

            const ic_stroke = mapCurrentToStrokeWidth(actual_Ic_mA, ic_visualization_max_mA);
            icFlowPath.style.strokeWidth = ic_stroke;
            icArrowText.style.opacity = actual_Ic_mA > 0.001 ? '1' : '0.1';

            const Ie_mA = actual_Ic_mA + (baseCurrent_uA / 1000);
            const ie_stroke = mapCurrentToStrokeWidth(Ie_mA, ie_visualization_max_mA);
            ieFlowPath.style.strokeWidth = ie_stroke;
            ieArrowText.style.opacity = Ie_mA > 0.001 ? '1' : '0.1';
        }

        baseCurrentSlider.addEventListener('input', updateApp);
        collectorVoltageSlider.addEventListener('input', updateApp);
        currentGainSlider.addEventListener('input', updateApp);

        // Initial call to set values and draw diagram correctly on page load
        document.addEventListener('DOMContentLoaded', updateApp);
    </script>
</body>
</html>
