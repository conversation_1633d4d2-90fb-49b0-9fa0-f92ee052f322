 c<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معمل الترانزستور الافتراضي - مساحة عمل الدوائر</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/workbench_styles.css">
</head>
<body>
    <header>
        <h1>مساحة عمل رسم الدوائر</h1>
        <nav>
            <ul>
                <li><a href="index.html">الرئيسية</a></li>
                <li><a href="simulation.html">المحاكاة العامة</a></li>
                <li><a href="#">التجارب</a></li>
                <li><a href="#">عن المشروع</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section id="workbench-area">
            <h2>لوحة الرسم</h2>

            <div class="workbench-container">
                <!-- Component Library Panel -->
                <div id="component-library">
                    <div class="library-header">
                        <h3>مكتبة العناصر</h3>
                        <div class="search-container">
                            <input type="text" id="component-search" placeholder="بحث عن عنصر...">
                        </div>
                        <div class="library-filters">
                            <select id="category-filter">
                                <option value="all">جميع الفئات</option>
                                <option value="basic">العناصر الأساسية</option>
                                <option value="semiconductor">أشباه الموصلات</option>
                                <option value="logic">البوابات المنطقية</option>
                                <option value="sources">المصادر والقياس</option>
                            </select>
                        </div>
                    </div>
                    <div class="library-content">
                        <!-- Component categories will be populated here -->
                    </div>
                </div>

                <!-- Main Workbench Area -->
                <div id="workbench-main">
                    <!-- Toolbar -->
                    <div id="toolbar">
                        <div class="toolbar-section">
                            <h4>العناصر الأساسية</h4>
                            <button id="tool-resistor">مقاومة</button>
                            <button id="tool-capacitor">مكثف</button>
                            <button id="tool-inductor">ملف</button>
                            <button id="tool-diode">ثنائي</button>
                            <button id="tool-led">LED</button>
                            <button id="tool-switch">مفتاح</button>
                        </div>
                        <div class="toolbar-section">
                            <h4>أشباه الموصلات</h4>
                            <button id="tool-transistor-npn">ترانزستور NPN</button>
                            <button id="tool-transistor-pnp">ترانزستور PNP</button>
                            <button id="tool-op-amp">مضخم عمليات</button>
                        </div>
                        <div class="toolbar-section">
                            <h4>البوابات المنطقية</h4>
                            <button id="tool-logic-and">AND</button>
                            <button id="tool-logic-or">OR</button>
                            <button id="tool-logic-not">NOT</button>
                        </div>
                        <div class="toolbar-section">
                            <h4>المصادر والقياس</h4>
                            <button id="tool-voltage-source">مصدر جهد</button>
                            <button id="tool-current-source">مصدر تيار</button>
                            <button id="tool-ground">أرضي</button>
                            <button id="tool-voltmeter">فولتميتر</button>
                            <button id="tool-ammeter">أميتر</button>
                        </div>
                        <div class="toolbar-section">
                            <h4>أدوات</h4>
                            <button id="tool-wire">سلك</button>
                            <button id="tool-select">تحديد</button>
                            <button id="tool-delete">حذف</button>
                            <button id="tool-rotate">تدوير</button>
                        </div>
                    </div>

                    <!-- Canvas with Grid Toggle -->
                    <div class="canvas-container">
                        <div class="canvas-controls">
                            <label class="toggle-container">
                                <input type="checkbox" id="grid-toggle" checked>
                                <span class="toggle-label">إظهار الشبكة</span>
                            </label>
                            <label class="toggle-container">
                                <input type="checkbox" id="snap-toggle" checked>
                                <span class="toggle-label">تفعيل التثبيت</span>
                            </label>
                        </div>
                        <canvas id="circuit-canvas" width="800" height="500"></canvas>
                    </div>

                    <!-- Properties Panel -->
                    <div id="properties-panel">
                        <h3>خصائص العنصر</h3>
                        <!-- Properties of the selected component will be displayed here -->
                    </div>
                </div>

                <!-- Oscilloscope Panel -->
                <div id="oscilloscope-panel">
                    <h3>راسم الذبذبات</h3>
                    <canvas id="oscilloscope-canvas" width="300" height="200"></canvas>
                    <div class="oscilloscope-controls">
                        <button id="oscilloscope-start">بدء</button>
                        <button id="oscilloscope-stop">إيقاف</button>
                        <select id="oscilloscope-timebase">
                            <option value="1">1 ms/div</option>
                            <option value="5">5 ms/div</option>
                            <option value="10" selected>10 ms/div</option>
                            <option value="50">50 ms/div</option>
                            <option value="100">100 ms/div</option>
                        </select>
                        <select id="oscilloscope-voltage">
                            <option value="0.1">0.1 V/div</option>
                            <option value="0.5">0.5 V/div</option>
                            <option value="1" selected>1 V/div</option>
                            <option value="5">5 V/div</option>
                            <option value="10">10 V/div</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Simulation Controls -->
            <div id="simulation-controls">
                <button id="simulate-button">بدء المحاكاة</button>
                <button id="reset-button">إعادة تعيين</button>
                <button id="save-button">حفظ الدائرة</button>
                <button id="load-button">تحميل دائرة</button>
                <button id="export-button">تصدير كصورة</button>
            </div>

            <!-- Templates Section -->
            <div id="templates-section">
                <h3>قوالب الدوائر</h3>
                <div class="templates-container">
                    <div class="template-card" data-template="voltage-divider">
                        <h4>مقسم الجهد</h4>
                        <div class="template-preview">
                            <img src="images/templates/voltage-divider.png" alt="مقسم الجهد">
                        </div>
                        <button class="load-template-btn">تحميل</button>
                    </div>
                    <div class="template-card" data-template="common-emitter">
                        <h4>مكبر الباعث المشترك</h4>
                        <div class="template-preview">
                            <img src="images/templates/common-emitter.png" alt="مكبر الباعث المشترك">
                        </div>
                        <button class="load-template-btn">تحميل</button>
                    </div>
                    <div class="template-card" data-template="astable-multivibrator">
                        <h4>مذبذب متعدد غير مستقر</h4>
                        <div class="template-preview">
                            <img src="images/templates/astable-multivibrator.png" alt="مذبذب متعدد غير مستقر">
                        </div>
                        <button class="load-template-btn">تحميل</button>
                    </div>
                </div>
            </div>

            <input type="file" id="load-circuit-file" accept=".json" style="display: none;">
        </section>
    </main>

    <footer>
        <p>&copy; 2024 معمل الترانزستور الافتراضي. جميع الحقوق محفوظة.</p>
    </footer>

    <script src="js/workbench.js"></script>
</body>
</html>