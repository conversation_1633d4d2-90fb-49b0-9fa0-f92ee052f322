<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Transistor Circuit Explorer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: flex-start; /* Align to top for potentially long content */
            min-height: 100vh;
        }
        .app-container {
            max-width: 900px;
            width: 100%;
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
            text-align: center;
        }
        h1 { 
            margin-top: 0;
            margin-bottom: 25px; 
            font-size: 1.8em;
        }
        h2 { 
            margin-top: 0; 
            margin-bottom: 15px; 
            font-size: 1.3em;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }

        .main-content {
            display: flex;
            flex-wrap: wrap;
            gap: 25px;
        }
        .circuit-diagram-container, .controls-output-container {
            flex: 1;
            min-width: 300px; /* Minimum width before wrapping */
            padding: 20px;
            background-color: #fdfdfd;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        #circuitSvg {
            max-width: 100%; 
            height: auto; 
            display: block; 
            margin: 0 auto; 
            border: 1px solid #ccc;
            background-color: #fff; /* White background for the diagram itself */
        }

        .controls label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        .controls input[type="range"] {
            width: 100%;
            margin-bottom: 15px;
        }
        .output p, .status p, .fixed-params p {
            margin: 8px 0;
            font-size: 1em;
        }
        .output span, .status span, .fixed-params span, #vinValue {
            font-weight: bold;
            color: #0056b3; /* A slightly distinct color for values */
        }
        #transistorState {
            display: inline-block; 
            padding: 6px 12px;
            border-radius: 4px;
            font-weight: bold;
            color: white; 
            min-width: 90px; 
            text-align: center;
            transition: background-color 0.3s ease;
        }

        .fixed-params {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px dashed #ccc;
        }
        .fixed-params p span {
            color: #333; /* Standard color for fixed param values */
        }

        /* SVG text styling */
        #circuitSvg text {
            font-family: Arial, sans-serif;
            fill: #333;
        }
         #circuitSvg .svg-label {
            font-size: 12px;
        }
        #circuitSvg .svg-value-label {
            font-size: 10px;
        }
        #circuitSvg .svg-node-label { /* For C, B, E */
            font-size: 12px;
            font-weight: bold;
        }


        @media (max-width: 720px) {
            .main-content {
                flex-direction: column;
            }
            .circuit-diagram-container, .controls-output-container {
                min-width: 100%; /* Stack them fully */
            }
            h1 { font-size: 1.5em; }
            h2 { font-size: 1.2em; }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <h1>Transistor Behavior Explorer</h1>

        <div class="main-content">
            <div class="circuit-diagram-container">
                <h2>Simplified Circuit Diagram</h2>
                <svg id="circuitSvg" viewBox="0 0 300 250">
                    <!-- VCC -->
                    <text x="145" y="18" class="svg-label">VCC (<tspan id="svgVccVal" class="svg-value-label">12</tspan>V)</text>
                    <line x1="150" y1="25" x2="150" y2="50" stroke="black" stroke-width="2"/>

                    <!-- RC -->
                    <rect x="140" y="50" width="20" height="30" stroke="black" stroke-width="2" fill="white"/>
                    <text x="168" y="60" class="svg-label">RC</text>
                    <text x="168" y="75" class="svg-value-label">(<tspan id="svgRcVal">1</tspan>kΩ)</text>
                    <line x1="150" y1="80" x2="150" y2="100" stroke="black" stroke-width="2"/> <!-- Wire to Collector -->

                    <!-- Transistor (NPN BJT) - Simplified Symbol -->
                    <circle cx="150" cy="120" r="20" stroke="black" stroke-width="2" fill="#e0f2fe"/> <!-- Light blue fill for transistor body -->
                    <line x1="150" y1="100" x2="150" y2="110" stroke="black" stroke-width="2"/> <!-- Collector line to body center -->
                    <line x1="110" y1="120" x2="130" y2="120" stroke="black" stroke-width="2"/> <!-- Base line to body edge -->
                    <line x1="150" y1="130" x2="150" y2="150" stroke="black" stroke-width="2"/> <!-- Emitter line from body center -->
                    
                    <!-- Emitter Arrow (NPN: Pointing Out from emitter line) -->
                    <line x1="150" y1="140" x2="143" y2="133" stroke="black" stroke-width="2"/>
                    <line x1="150" y1="140" x2="157" y2="133" stroke="black" stroke-width="2"/>
                    
                    <text x="158" y="105" class="svg-node-label">C</text>
                    <text x="100" y="125" class="svg-node-label">B</text>
                    <text x="158" y="150" class="svg-node-label">E</text>

                    <!-- Ground for Emitter -->
                    <line x1="150" y1="150" x2="150" y2="170" stroke="black" stroke-width="2"/>
                    <line x1="135" y1="170" x2="165" y2="170" stroke="black" stroke-width="2"/>
                    <line x1="140" y1="175" x2="160" y2="175" stroke="black" stroke-width="2"/>
                    <line x1="145" y1="180" x2="155" y2="180" stroke="black" stroke-width="2"/>

                    <!-- RB -->
                    <line x1="110" y1="120" x2="70" y2="120" stroke="black" stroke-width="2"/> <!-- Wire from Base -->
                    <rect x="50" y="110" width="20" height="30" stroke="black" stroke-width="2" fill="white"/>
                    <text x="15" y="115" class="svg-label">RB</text>
                    <text x="15" y="130" class="svg-value-label">(<tspan id="svgRbVal">10</tspan>kΩ)</text>
                    <line x1="50" y1="120" x2="30" y2="120" stroke="black" stroke-width="2"/> <!-- Wire to Vin -->

                    <!-- Vin -->
                    <circle cx="20" cy="120" r="10" stroke="black" stroke-width="2" fill="white"/>
                    <text x="16" y="124" font-size="10" font-family="sans-serif">+</text>
                    <line x1="20" y1="125" x2="20" y2="125" stroke="black" stroke-width="1.5" style="transform: translateY(2.5px);"/> <!-- Minus sign -->
                    <text x="5" y="105" class="svg-label">Vin</text>
                    <line x1="20" y1="130" x2="20" y2="170" stroke="black" stroke-width="2"/> <!-- Vin to ground -->
                    <line x1="5" y1="170" x2="35" y2="170" stroke="black" stroke-width="2"/> <!-- Vin ground symbol -->
                </svg>
            </div>

            <div class="controls-output-container">
                <div class="controls">
                    <h2>Controls</h2>
                    <label for="vinSlider">Input Voltage (Vin): <span id="vinValue">0.0</span> V</label>
                    <input type="range" id="vinSlider" min="0" max="5" step="0.05" value="0">
                </div>

                <div class="output">
                    <h2>Calculated Values</h2>
                    <p>Base Current (I<sub>B</sub>): <span id="ibValue">0.00</span> µA</p>
                    <p>Collector Current (I<sub>C</sub>): <span id="icValue">0.00</span> mA</p>
                    <p>Collector-Emitter Voltage (V<sub>CE</sub>): <span id="vceValue">0.00</span> V</p>
                </div>

                <div class="status">
                    <h2>Operating Region</h2>
                    <p><span id="transistorState">Cut-off</span></p>
                </div>

                <div class="fixed-params">
                    <h2>Fixed Parameters</h2>
                    <p>V<sub>CC</sub>: <span id="vccParam">12</span> V</p>
                    <p>R<sub>C</sub>: <span id="rcParam">1</span> kΩ</p>
                    <p>R<sub>B</sub>: <span id="rbParam">10</span> kΩ</p>
                    <p>Beta (β): <span id="betaParam">100</span></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- Constants for the Circuit ---
        const VCC = 12;       // Supply Voltage (Volts)
        const RC = 1000;      // Collector Resistor (Ohms)
        const RB = 10000;     // Base Resistor (Ohms)
        const BETA = 100;     // Current Gain
        const VBE_THRESHOLD = 0.7; // Base-Emitter forward voltage drop (Volts)
        const VCE_SATURATION = 0.2; // Collector-Emitter saturation voltage (Volts)

        // --- DOM Element References ---
        const vinSlider = document.getElementById('vinSlider');
        const vinValueDisplay = document.getElementById('vinValue');
        const ibValueDisplay = document.getElementById('ibValue');
        const icValueDisplay = document.getElementById('icValue');
        const vceValueDisplay = document.getElementById('vceValue');
        const transistorStateDisplay = document.getElementById('transistorState');

        // --- Update Fixed Parameter Displays ---
        document.getElementById('vccParam').textContent = VCC;
        document.getElementById('rcParam').textContent = RC / 1000; // Display in kOhms
        document.getElementById('rbParam').textContent = RB / 1000; // Display in kOhms
        document.getElementById('betaParam').textContent = BETA;

        // Update SVG fixed values
        document.getElementById('svgVccVal').textContent = VCC;
        document.getElementById('svgRcVal').textContent = RC / 1000;
        document.getElementById('svgRbVal').textContent = RB / 1000;

        // --- Calculation and Display Function ---
        function calculateAndDisplay() {
            const Vin = parseFloat(vinSlider.value);
            vinValueDisplay.textContent = Vin.toFixed(2); // Show Vin with 2 decimal places for 0.05 step

            let IB = 0;
            if (Vin > VBE_THRESHOLD) {
                IB = (Vin - VBE_THRESHOLD) / RB;
            }
            // Ensure IB is not negative due to floating point inaccuracies or Vin <= VBE_THRESHOLD
            if (IB < 0) {
                IB = 0;
            }

            let IC_potential = BETA * IB;
            const IC_limit_by_VCC_RC = VCC / RC; // Max possible current if VCE drops to 0

            let IC = Math.min(IC_potential, IC_limit_by_VCC_RC);
            if (IC < 0) { // Should not happen if IB is non-negative
                IC = 0;
            }
            
            let VCE = VCC - (IC * RC);
            // VCE cannot be negative. If calculation results in negative, it means deep saturation, VCE is near 0 or VCE_SAT.
            // The IC limit (VCC/RC) ensures VCE won't go below 0 if that limit is strictly applied.
            if (VCE < 0) {
                VCE = 0; 
            }

            // --- Determine Operating Region ---
            let region = "";
            let regionColor = "";

            if (IB <= 0) { // Or more strictly, Vin <= VBE_THRESHOLD
                region = "Cut-off";
                regionColor = "#808080"; // Grey
                IC = 0;  // Explicitly set IC to 0 in cut-off
                VCE = VCC; // In cut-off, VCE is VCC
            } else if (VCE <= VCE_SATURATION) {
                region = "Saturation";
                regionColor = "#5dade2"; // Brighter Blue for Saturation
                // In saturation, VCE is low. The calculated IC might drive VCE even lower than VCE_SAT.
                // For display, VCE should be capped at VCE_SAT if one wants to be pedantic,
                // but the problem asks to calculate VCE = VCC - IC*RC.
                // If IC is high enough, this calculated VCE will be <= VCE_SAT.
            } else {
                region = "Active";
                regionColor = "#58d68d"; // Brighter Green for Active
            }

            // --- Update Value Displays ---
            ibValueDisplay.textContent = (IB * 1e6).toFixed(2);   // Display IB in microamperes (µA)
            icValueDisplay.textContent = (IC * 1e3).toFixed(2);   // Display IC in milliamperes (mA)
            vceValueDisplay.textContent = VCE.toFixed(2);         // Display VCE in Volts (V)
            
            transistorStateDisplay.textContent = region;
            transistorStateDisplay.style.backgroundColor = regionColor;
        }

        // --- Event Listener for Slider ---
        vinSlider.addEventListener('input', calculateAndDisplay);

        // --- Initial Calculation on Page Load ---
        calculateAndDisplay();
    </script>
</body>
</html>
