<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مستكشف أوضاع تشغيل الترانزستور NPN BJT</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        /* أنماط إضافية لهذه الصفحة إذا لزم الأمر */
        .mode-explanation {
            padding: 15px;
            margin-top: 10px;
            border-radius: 4px;
        }
        .cutoff-mode { background-color: #ffebee; border-left: 5px solid #c62828; }
        .active-mode { background-color: #e8f5e9; border-left: 5px solid #2e7d32; }
        .saturation-mode { background-color: #e3f2fd; border-left: 5px solid #1565c0; }
    </style>
</head>
<body>
    <header>
        <h1>مستكشف أوضاع تشغيل الترانزستور NPN BJT</h1>
    </header>

    <nav>
        <ul>
            <li><a href="index.html">الرئيسية</a></li>
            <li><a href="simulation.html">بدء المحاكاة</a></li>
            <li><a href="NPN Transistor Behavior Explorer.html">مستكشف سلوك NPN</a></li>
        </ul>
    </nav>

    <main>
        <section id="modes-intro">
            <h2>فهم أوضاع تشغيل الترانزستور NPN</h2>
            <p>يعمل الترانزستور NPN BJT في ثلاثة أوضاع رئيسية: القطع (Cutoff)، الفعال (Active)، والتشبع (Saturation). فهم هذه الأوضاع ضروري لتصميم الدوائر الإلكترونية المختلفة مثل المفاتيح والمضخمات.</p>
        </section>

        <section id="modes-simulation-area">
            <h2>محاكاة تفاعلية لأوضاع التشغيل</h2>
            <p>تحكم في جهد القاعدة (V<sub>BE</sub>) وجهد المجمع-الباعث (V<sub>CE</sub>) ولاحظ كيف ينتقل الترانزستور بين الأوضاع المختلفة.</p>
            <div class="simulation-area">
                <p>مساحة محاكاة أوضاع التشغيل (قيد الإنشاء)</p>
                <div>
                    <label for="vbe-slider">جهد القاعدة-الباعث (V<sub>BE</sub>):</label>
                    <input type="range" id="vbe-slider" min="0" max="1.2" step="0.05" value="0.7">
                    <span id="vbe-value">0.70 V</span>
                </div>
                <div>
                    <label for="vce-slider">جهد المجمع-الباعث (V<sub>CE</sub>):</label>
                    <input type="range" id="vce-slider" min="0" max="10" step="0.1" value="5">
                    <span id="vce-value">5.0 V</span>
                </div>
                <div id="mode-results" style="margin-top: 20px;">
                    <h4>النتائج:</h4>
                    <p>تيار القاعدة (I<sub>B</sub>): <span id="ib-result">--</span> µA</p>
                    <p>تيار المجمع (I<sub>C</sub>): <span id="ic-result">--</span> mA</p>
                    <p><strong>وضع التشغيل الحالي: <span id="current-mode" style="font-weight:bold;">--</span></strong></p>
                </div>
            </div>
        </section>

        <section id="circuit-diagram-workbench">
            <h2>رسم الدائرة ومنطقة العمل</h2>
            <div class="circuit-diagram-container">
                <p><strong>رسم الدائرة:</strong></p>
                <!-- سيتم استبدال هذا برسم SVG أو صورة للدائرة لاحقًا -->
                <img src="images/placeholder_circuit_npn_modes.png" alt="رسم دائرة مستكشف أوضاع NPN" style="max-width: 400px; border: 1px solid #ccc; padding: 10px; margin-bottom: 15px;">
            </div>
            <div class="workbench-container" style="border: 1px solid #007bff; padding: 15px; margin-top: 20px; background-color: #f0f8ff;">
                <p><strong>منطقة العمل (Workbench):</strong></p>
                <p><em>(سيتم هنا إضافة عناصر تحكم ومكونات تفاعلية لرسم الدائرة وتعديلها)</em></p>
                <!-- مثال لعناصر يمكن إضافتها -->
                <div>
                    <label for="component-select">اختر مكونًا:</label>
                    <select id="component-select">
                        <option value="resistor">مقاومة</option>
                        <option value="capacitor">مكثف</option>
                        <option value="transistor_npn">ترانزستور NPN</option>
                        <option value="voltage_source">مصدر جهد</option>
                    </select>
                    <button id="addComponentButtonNpnModes">إضافة المكون</button>
                </div>
                <div id="interactive-circuit-drawing-area" style="width: 100%; height: 200px; background-color: #e9ecef; margin-top:10px; border:1px dashed #adb5bd; display:flex; flex-direction: column; align-items:center; justify-content:center;">
                    <p>مساحة رسم الدائرة (قيد التطوير)</p>
                </div>
            </div>
        </section>

        <section id="modes-explanation-details">
            <h2>شرح تفصيلي لأوضاع التشغيل</h2>
            
            <div id="cutoff-explanation" class="mode-explanation cutoff-mode">
                <h3>1. وضع القطع (Cutoff Mode)</h3>
                <p>يحدث عندما يكون جهد القاعدة-الباعث (V<sub>BE</sub>) أقل من جهد العتبة (حوالي 0.7 فولت للسيليكون). في هذا الوضع:</p>
                <ul>
                    <li>وصلة القاعدة-الباعث (BEJ) تكون في حالة انحياز عكسي أو انحياز أمامي ضعيف جداً.</li>
                    <li>وصلة المجمع-القاعدة (CBJ) تكون في حالة انحياز عكسي.</li>
                    <li>الترانزستور يكون "مطفأ" (OFF)، ولا يمر تيار يذكر من المجمع إلى الباعث (I<sub>C</sub> ≈ 0).</li>
                    <li>يُستخدم الترانزستور كمفتاح مفتوح في هذا الوضع.</li>
                </ul>
            </div>

            <div id="active-explanation" class="mode-explanation active-mode">
                <h3>2. الوضع الفعال (Active Mode)</h3>
                <p>يحدث عندما يكون جهد القاعدة-الباعث (V<sub>BE</sub>) كافياً (حوالي 0.7 فولت) لجعل وصلة القاعدة-الباعث في انحياز أمامي، ووصلة المجمع-القاعدة في انحياز عكسي (V<sub>CB</sub> > 0، أو V<sub>CE</sub> > V<sub>BE</sub>).</p>
                <ul>
                    <li>وصلة القاعدة-الباعث (BEJ) تكون في حالة انحياز أمامي.</li>
                    <li>وصلة المجمع-القاعدة (CBJ) تكون في حالة انحياز عكسي.</li>
                    <li>تيار المجمع (I<sub>C</sub>) يتناسب طردياً مع تيار القاعدة (I<sub>B</sub>) من خلال العلاقة: I<sub>C</sub> = β * I<sub>B</sub> (حيث β هو كسب التيار).</li>
                    <li>يُستخدم الترانزستور كمضخم للإشارة في هذا الوضع.</li>
                </ul>
            </div>

            <div id="saturation-explanation" class="mode-explanation saturation-mode">
                <h3>3. وضع التشبع (Saturation Mode)</h3>
                <p>يحدث عندما تكون كل من وصلة القاعدة-الباعث ووصلة المجمع-القاعدة في حالة انحياز أمامي. هذا يعني أن V<sub>BE</sub> ≥ 0.7V و V<sub>BC</sub> ≥ 0.7V (أو V<sub>CE</sub> يصبح صغيراً جداً، عادةً V<sub>CE(sat)</sub> ≈ 0.2V).</p>
                <ul>
                    <li>وصلة القاعدة-الباعث (BEJ) تكون في حالة انحياز أمامي.</li>
                    <li>وصلة المجمع-القاعدة (CBJ) تكون أيضاً في حالة انحياز أمامي.</li>
                    <li>الترانزستور يكون "مفتوح بالكامل" (ON)، ويمر أقصى تيار ممكن عبر المجمع، ويتم تحديده بشكل أساسي بواسطة دائرة المجمع الخارجية وليس فقط بتيار القاعدة.</li>
                    <li>العلاقة I<sub>C</sub> = β * I<sub>B</sub> لم تعد صالحة تماماً؛ I<sub>C</sub> < β * I<sub>B</sub>.</li>
                    <li>يُستخدم الترانزستور كمفتاح مغلق في هذا الوضع.</li>
                </ul>
            </div>
        </section>

        <section id="experiment-procedure">
            <h2>الخطوات الأساسية لإجراء التجربة</h2>
            <ol>
                <li><strong>فهم الهدف من التجربة:</strong> استيعاب مفهوم أوضاع تشغيل الترانزستور NPN (القطع، الفعال، التشبع) وكيفية التحكم فيها.</li>
                <li><strong>التعرف على واجهة المحاكاة:</strong> فهم دور كل من منزلقات التحكم (V<sub>BE</sub> و V<sub>CE</sub>) وكيف تؤثر على قراءات تيار القاعدة (I<sub>B</sub>)، تيار المجمع (I<sub>C</sub>)، ووضع التشغيل.</li>
                <li><strong>استكشاف وضع القطع:</strong>
                    <ul>
                        <li>اضبط V<sub>BE</sub> على قيمة أقل من 0.7 فولت (مثلاً 0.2 فولت).
                        <li>غيّر قيمة V<sub>CE</sub> ولاحظ أن I<sub>B</sub> و I<sub>C</sub> يظلان قريبين من الصفر.
                        <li>سجل ملاحظاتك حول سلوك الترانزستور في هذا الوضع.</li>
                    </ul>
                </li>
                <li><strong>استكشاف الوضع الفعال:</strong>
                    <ul>
                        <li>اضبط V<sub>BE</sub> على قيمة أكبر من 0.7 فولت (مثلاً 0.7 فولت أو 0.8 فولت).
                        <li>حافظ على V<sub>CE</sub> بحيث يكون أكبر من V<sub>BE</sub> وأكبر من V<sub>CE(sat)</sub> (مثلاً بين 2 فولت و 8 فولت).
                        <li>غيّر قيمة V<sub>BE</sub> قليلاً ولاحظ التغير في I<sub>B</sub> و I<sub>C</sub>. هل العلاقة I<sub>C</sub> ≈ β * I<sub>B</sub> متحققة؟
                        <li>غيّر قيمة V<sub>CE</sub> ضمن النطاق الفعال ولاحظ تأثيره (الطفيف) على I<sub>C</sub>.
                        <li>سجل ملاحظاتك.</li>
                    </ul>
                </li>
                <li><strong>استكشاف وضع التشبع:</strong>
                    <ul>
                        <li>اضبط V<sub>BE</sub> على قيمة أكبر من 0.7 فولت (مثلاً 0.8 فولت أو أعلى).
                        <li>قلل قيمة V<sub>CE</sub> تدريجياً حتى تصبح قريبة من V<sub>CE(sat)</sub> (مثلاً 0.2 فولت أو أقل).
                        <li>لاحظ كيف يزداد I<sub>C</sub> حتى يصل إلى قيمة معينة ثم يتوقف عن الزيادة بشكل كبير مع زيادة I<sub>B</sub> (عن طريق زيادة V<sub>BE</sub>).
                        <li>سجل ملاحظاتك حول سلوك الترانزستور عندما يكون V<sub>CE</sub> منخفضاً جداً.</li>
                    </ul>
                </li>
                <li><strong>تسجيل البيانات:</strong> قم بتعبئة الجداول المقترحة أدناه بقيم مختلفة لـ V<sub>BE</sub> و V<sub>CE</sub> والقراءات المقابلة.</li>
                <li><strong>تحليل النتائج:</strong> بناءً على البيانات والملاحظات، قم بتحليل سلوك الترانزستور في كل وضع.</li>
            </ol>
        </section>

        <section id="report-writing">
            <h2>كتابة تقرير التجربة</h2>
            <p>يهدف التقرير إلى تلخيص فهمك لأوضاع تشغيل الترانزستور NPN بناءً على المحاكاة التفاعلية. يجب أن يتضمن التقرير العناصر التالية:</p>
            <ul>
                <li><strong>مقدمة:</strong> وصف موجز للترانزستور NPN وأهمية فهم أوضاع تشغيله.</li>
                <li><strong>الأدوات المستخدمة:</strong> ذكر أن التجربة تمت باستخدام محاكي "مستكشف أوضاع تشغيل الترانزستور NPN BJT".</li>
                <li><strong>الخطوات المتبعة:</strong> تلخيص للخطوات التي قمت بها خلال استكشاف الأوضاع المختلفة.</li>
                <li><strong>النتائج والملاحظات:</strong> عرض البيانات التي جمعتها في جداول، مع ملاحظاتك حول كل وضع.</li>
                <li><strong>تحليل النتائج:</strong> شرح كيف تتوافق النتائج مع النظرية الخاصة بكل وضع تشغيل. ناقش العلاقة بين V<sub>BE</sub>، V<sub>CE</sub>، I<sub>B</sub>، I<sub>C</sub>، ووضع التشغيل.</li>
                <li><strong>الاستنتاج:</strong> تلخيص لما تعلمته من التجربة.</li>
            </ul>

            <h3>جداول مقترحة لتسجيل النتائج:</h3>
            <h4>جدول 1: استكشاف وضع القطع</h4>
            <table>
                <thead>
                    <tr>
                        <th>V<sub>BE</sub> (V)</th>
                        <th>V<sub>CE</sub> (V)</th>
                        <th>I<sub>B</sub> (µA)</th>
                        <th>I<sub>C</sub> (mA)</th>
                        <th>وضع التشغيل الملاحظ</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>0.2</td>
                        <td>2.0</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>0.4</td>
                        <td>5.0</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>0.6</td>
                        <td>8.0</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <!-- أضف المزيد من الصفوف حسب الحاجة -->
                </tbody>
            </table>

            <h4>جدول 2: استكشاف الوضع الفعال</h4>
            <table>
                <thead>
                    <tr>
                        <th>V<sub>BE</sub> (V)</th>
                        <th>V<sub>CE</sub> (V)</th>
                        <th>I<sub>B</sub> (µA)</th>
                        <th>I<sub>C</sub> (mA)</th>
                        <th>β (I<sub>C</sub>/I<sub>B</sub>)</th>
                        <th>وضع التشغيل الملاحظ</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>0.7</td>
                        <td>2.0</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>0.7</td>
                        <td>5.0</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>0.75</td>
                        <td>5.0</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>0.8</td>
                        <td>8.0</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <!-- أضف المزيد من الصفوف حسب الحاجة -->
                </tbody>
            </table>

            <h4>جدول 3: استكشاف وضع التشبع</h4>
            <table>
                <thead>
                    <tr>
                        <th>V<sub>BE</sub> (V)</th>
                        <th>V<sub>CE</sub> (V)</th>
                        <th>I<sub>B</sub> (µA)</th>
                        <th>I<sub>C</sub> (mA)</th>
                        <th>وضع التشغيل الملاحظ</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>0.8</td>
                        <td>0.5</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>0.8</td>
                        <td>0.2</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>0.9</td>
                        <td>0.1</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                     <tr>
                        <td>1.0</td>
                        <td>0.2</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <!-- أضف المزيد من الصفوف حسب الحاجة -->
                </tbody>
            </table>
        </section>

    </main>

    <footer>
        <p>&copy; 2024 معمل الترانزستور الافتراضي. جميع الحقوق محفوظة.</p>
    </footer>

    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const vbeSlider = document.getElementById('vbe-slider');
            const vceSlider = document.getElementById('vce-slider');
            const vbeValueSpan = document.getElementById('vbe-value');
            const vceValueSpan = document.getElementById('vce-value');
            const ibResultSpan = document.getElementById('ib-result');
            const icResultSpan = document.getElementById('ic-result');
            const currentModeSpan = document.getElementById('current-mode');

            // قيم افتراضية للترانزستور (يمكن تعديلها)
            const BETA = 100; // كسب التيار
            const VBE_THRESHOLD = 0.7; // جهد عتبة القاعدة-باعث
            const VCE_SAT = 0.2; // جهد التشبع مجمع-باعث
            // لتبسيط حساب تيار القاعدة، نفترض مقاومة قاعدة Rb
            const RB_KOHM = 10; // مقاومة القاعدة بالكيلو أوم
            // لتبسيط حساب تيار المجمع في التشبع، نفترض مقاومة مجمع Rc
            const RC_KOHM = 1; // مقاومة المجمع بالكيلو أوم

            function updateOperatingMode() {
                let vbe = parseFloat(vbeSlider.value);
                let vce = parseFloat(vceSlider.value);

                vbeValueSpan.textContent = vbe.toFixed(2) + ' V';
                vceValueSpan.textContent = vce.toFixed(1) + ' V';

                let ib_uA = 0;
                let ic_mA = 0;
                let mode = '';

                if (vbe < VBE_THRESHOLD) {
                    mode = 'القطع (Cutoff)';
                    ib_uA = 0;
                    ic_mA = 0;
                } else {
                    // حساب تيار القاعدة التقريبي
                    // نفترض أن جهد الدخل متصل مباشرة بـ VBE خلال مقاومة القاعدة RB
                    // هذا تبسيط، في الواقع V_input - VBE = IB * RB
                    // لكن هنا VBE هو المدخل المباشر للوصلة
                    ib_uA = ((vbe - VBE_THRESHOLD) / RB_KOHM) * 1000; // تحويل من mA إلى µA
                    ib_uA = Math.max(0, ib_uA); // لا يمكن أن يكون التيار سالبًا

                    let ic_active_mA = (BETA * ib_uA) / 1000; // تحويل من µA إلى mA

                    if (vce <= VCE_SAT) {
                        mode = 'التشبع (Saturation)';
                        // في التشبع، تيار المجمع يحدده VCC و RC (نفترض VCC هو VCE المعطى كحد أقصى)
                        // هذا تبسيط كبير، VCE هو نتيجة وليس مدخل مباشر دائماً
                        // ic_mA = (vceSlider.max - VCE_SAT) / RC_KOHM; // هذا ليس دقيقاً تماماً للطريقة التي يتم بها التحكم
                        // طريقة أفضل: إذا كان VCE أقل من VCE_SAT، فهو في التشبع
                        // وتيار المجمع هو ما تسمح به الدائرة الخارجية، حتى لو كان أقل من beta*IB
                        // هنا، بما أننا نتحكم بـ VCE مباشرة، إذا كان VCE <= VCE_SAT، فهو تشبع.
                        // تيار المجمع في التشبع سيكون محدودًا بالدائرة الخارجية.
                        // لغرض هذه المحاكاة، إذا كان VCE <= VCE_SAT، نعتبره تشبع ونحسب Ic بناءً على VCE.
                        // هذا ليس نموذجًا فيزيائيًا دقيقًا لكيفية عمل الدائرة، بل هو استكشاف للأوضاع.
                        ic_mA = (parseFloat(vceSlider.max) - vce) / RC_KOHM; // مثال لتيار التشبع
                        ic_mA = Math.max(0, ic_mA);
                        // يجب أن يكون تيار التشبع الفعلي أقل من أو يساوي beta * ib
                        // إذا كان beta * ib أقل، فهذا يعني أنه لم يصل للتشبع الكامل بعد أو أن النموذج مبسط
                        ic_mA = Math.min(ic_mA, ic_active_mA); 

                    } else if (vce > VBE_THRESHOLD && (vce - vbe) > VCE_SAT ) { // VCB > 0 (تقريبي)
                        // VCB = VCE - VBE. للانحياز العكسي لـ CBJ، VCB > 0 (أو قيمة سالبة صغيرة جداً)
                        // بشكل أدق، طالما VCE > VCE_SAT، فهو في المنطقة الفعالة أو القطع.
                        // وبما أننا تجاوزنا شرط القطع (VBE >= VBE_THRESHOLD)
                        mode = 'الفعال (Active)';
                        ic_mA = ic_active_mA;
                    } else {
                        // هذه الحالة قد تمثل انتقالاً أو منطقة حدودية، أو أن VCE قريب جداً من VBE
                        // إذا كان VCE قريب من VBE ولكن أكبر من VCE_SAT، لا يزال يعتبر فعالاً
                        // إذا كان VCE أقل من VBE (يعني VCB موجب، أي CBJ انحياز أمامي)، فهذا تشبع
                        // الشرط vce <= VCE_SAT يغطي التشبع بشكل جيد
                        // إذا لم يكن قطعاً ولم يكن تشبعاً (VCE > VCE_SAT)، فهو فعال
                        mode = 'الفعال (Active)'; // افتراضي إذا لم يكن قطع أو تشبع
                        ic_mA = ic_active_mA;
                         if (vce <= vbe) { // إذا كان VCE أقل من VBE (VBC موجب) فهذا يقود للتشبع
                             mode = 'التشبع (Saturation)';
                             // إعادة حساب Ic للتشبع إذا لزم الأمر
                             let ic_sat_approx = (parseFloat(vceSlider.max) - VCE_SAT) / RC_KOHM;
                             ic_mA = Math.min(ic_active_mA, ic_sat_approx);
                         }
                    }
                }
                
                // التأكد من أن ic لا يتجاوز ما يمكن أن يوفره المصدر (تبسيط)
                let max_ic_possible_by_vce = (parseFloat(vceSlider.max) - vce) / RC_KOHM;
                if (mode === 'الفعال (Active)') {
                     ic_mA = Math.min(ic_mA, max_ic_possible_by_vce);
                }

                ibResultSpan.textContent = ib_uA.toFixed(2) + ' µA';
                icResultSpan.textContent = ic_mA.toFixed(2) + ' mA';
                currentModeSpan.textContent = mode;

                // تحديث ألوان الخلفية للشرح بناءً على الوضع الحالي
                document.querySelectorAll('.mode-explanation').forEach(el => el.style.opacity = '0.6');
                if (mode.includes('القطع')) document.getElementById('cutoff-explanation').style.opacity = '1';
                if (mode.includes('الفعال')) document.getElementById('active-explanation').style.opacity = '1';
                if (mode.includes('التشبع')) document.getElementById('saturation-explanation').style.opacity = '1';
            }

            vbeSlider.addEventListener('input', updateOperatingMode);
            vceSlider.addEventListener('input', updateOperatingMode);

            updateOperatingMode(); // Call on load
        });
    </script>
<script src="js/main.js" defer></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const addButton = document.getElementById('addComponentButtonNpnModes');
        const componentSelect = document.getElementById('component-select');
        
        if (addButton && componentSelect) {
            addButton.addEventListener('click', function() {
                const selectedComponent = componentSelect.value;
                if (typeof addComponent === 'function') {
                    addComponent(selectedComponent);
                } else {
                    console.error('addComponent function is not defined. Make sure js/main.js is loaded.');
                }
            });
        }
    });
</script>
</body>
</html>
