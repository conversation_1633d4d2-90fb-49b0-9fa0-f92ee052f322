<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NPN Transistor Explorer</title>
    <style>
        body {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
        }

        .container {
            max-width: 1100px;
            width: 100%;
            background: #fff;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        h1, h2, h3 {
            color: #1a237e; /* Dark blue */
            text-align: center;
        }
        h1 { margin-bottom: 25px; font-size: 1.8em; }
        h2 { margin-top: 30px; margin-bottom: 15px; font-size: 1.4em; border-bottom: 1px solid #e0e0e0; padding-bottom: 5px;}
        h3 { text-align: left; margin-top: 0; margin-bottom: 8px; font-size: 1.15em; color: #3f51b5; /* Indigo */}


        .main-content {
            display: flex;
            flex-wrap: wrap;
            gap: 25px;
        }

        .circuit-area {
            flex: 2 1 500px; 
            min-width: 320px;
            border: 1px solid #bdbdbd;
            border-radius: 8px;
            padding: 15px;
            background-color: #e8eaf6; /* Light indigo */
            display: flex;
            justify-content: center;
            align-items: center;
        }

        #circuit-diagram {
            width: 100%;
            max-width: 450px; /* Max width for the SVG itself */
            height: auto;
            display: block;
        }

        .controls-data-area {
            flex: 1 1 300px;
            min-width: 280px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .controls, .data-display {
            background-color: #f9f9f9;
            padding: 18px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .controls h3, .data-display h2 {
            text-align: left;
            margin-top:0;
            padding-bottom: 0;
            border-bottom: none;
            font-size: 1.2em;
        }


        .controls label {
            display: block;
            margin-bottom: 10px;
            font-weight: 500;
            color: #424242;
        }

        .controls input[type="range"] {
            width: 100%;
            cursor: pointer;
            margin-top: 5px;
        }

        .data-display p {
            margin: 10px 0;
            font-size: 0.95em;
            display: flex;
            justify-content: space-between;
        }
        .data-display p span:first-child {
            color: #555;
        }
        .data-display p .value-display {
            font-weight: bold;
            color: #1e88e5; /* Blue */
        }
        .data-display strong#region-status {
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-size: 0.9em;
            min-width: 80px;
            text-align: center;
        }

        .explanations {
            margin-top: 30px;
            padding: 0px; /* No padding here, div children will have */
        }

        .explanations > div { /* Direct children of explanations */
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            transition: border-color 0.3s ease, background-color 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .explanations p {
            font-size: 0.9em;
            margin-bottom: 0;
            color: #424242;
        }

        /* Region-specific styling for explanations and status */
        .cutoff-style { background-color: #e3f2fd; border-left: 5px solid #2196f3; } /* Light Blue */
        .active-style { background-color: #e8f5e9; border-left: 5px solid #4caf50; } /* Light Green */
        .saturation-style { background-color: #ffebee; border-left: 5px solid #f44336; } /* Light Red */

        #region-status.cutoff { background-color: #2196f3; } /* Blue */
        #region-status.active { background-color: #4caf50; } /* Green */
        #region-status.saturation { background-color: #f44336; } /* Red */

        /* SVG text styling */
        .svg-label { font-size: 11px; fill: #333; }
        .svg-value { font-size: 11px; font-weight: bold; fill: #0d47a1; } /* Darker Blue for values */
        .svg-meter-symbol { font-size: 9px; text-anchor: middle; dominant-baseline: central; }
        .svg-component-label { font-size: 10px; text-anchor: middle; fill: #555; }

        @media (max-width: 860px) {
            .main-content {
                flex-direction: column;
            }
            .circuit-area, .controls-data-area {
                flex-basis: auto; 
            }
        }
        @media (max-width: 480px) {
            body { padding: 10px; }
            .container { padding: 15px; }
            h1 { font-size: 1.5em; }
            h2 { font-size: 1.2em; }
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>NPN Transistor Interactive Explorer</h1>

        <div class="main-content">
            <div class="circuit-area">
                <svg id="circuit-diagram" viewBox="0 0 400 350"> <!-- Adjusted viewBox for better fit -->
                    <!-- VCC -->
                    <circle cx="200" cy="30" r="12" stroke="#333" stroke-width="1" fill="white"/>
                    <text x="200" y="30" class="svg-meter-symbol" fill="#333">+</text>
                    <text x="200" y="12" class="svg-component-label">VCC (+5V)</text>
                    <line x1="200" y1="42" x2="200" y2="60" stroke="#333" stroke-width="1"/>

                    <!-- RC -->
                    <rect x="170" y="60" width="60" height="18" stroke="#333" fill="#fff" stroke-width="1"/>
                    <text x="200" y="52" class="svg-component-label">RC (1kΩ)</text>
                    <line x1="200" y1="78" x2="200" y2="90" stroke="#333" stroke-width="1"/>

                    <!-- IC Ammeter -->
                    <line x1="200" y1="90" x2="200" y2="92" stroke="#333" stroke-width="1"/> 
                    <circle cx="200" cy="102" r="10" stroke="#c62828" fill="white" stroke-width="1"/>
                    <text x="200" y="102" class="svg-meter-symbol" fill="#c62828">A</text>
                    <text id="svg-ic-text" x="235" y="105" class="svg-value">0.00mA</text>
                    <line x1="200" y1="112" x2="200" y2="130" stroke="#333" stroke-width="1"/> 
                    
                    <!-- Vin_source (Conceptual input to RB) -->
                    <text x="10" y="170" class="svg-label">Vin_src</text>
                    <line x1="50" y1="165" x2="80" y2="165" stroke="#333" stroke-width="1"/>

                    <!-- RB -->
                    <rect x="80" y="156" width="60" height="18" stroke="#333" fill="#fff" stroke-width="1"/>
                    <text x="110" y="150" class="svg-component-label">RB (10kΩ)</text>
                    <line x1="140" y1="165" x2="150" y2="165" stroke="#333" stroke-width="1"/>

                    <!-- IB Ammeter -->
                    <line x1="150" y1="165" x2="152" y2="165" stroke="#333" stroke-width="1"/>
                    <circle cx="162" cy="165" r="10" stroke="#c62828" fill="white" stroke-width="1"/>
                    <text x="162" y="165" class="svg-meter-symbol" fill="#c62828">A</text>
                    <text id="svg-ib-text" x="162" y="190" text-anchor="middle" class="svg-value">0.00µA</text>
                    <line x1="172" y1="165" x2="180" y2="165" stroke="#333" stroke-width="1"/>

                    <!-- Transistor NPN -->
                    <!-- Base connection at (180,165) -->
                    <!-- Collector connection at (200,130) -->
                    <!-- Emitter connection at (200,200) -->
                    <line x1="180" y1="165" x2="190" y2="165" stroke="#333" stroke-width="1.5"/> <!-- Base lead -->
                    <line x1="190" y1="145" x2="190" y2="185" stroke="#333" stroke-width="1.5"/> <!-- Vertical bar -->
                    <line x1="190" y1="145" x2="200" y2="130" stroke="#333" stroke-width="1.5"/> <!-- Collector arm -->
                    <line x1="190" y1="185" x2="200" y2="200" stroke="#333" stroke-width="1.5"/> <!-- Emitter arm -->
                    <polygon points="200,200 192,193 199,192" fill="#333" transform="rotate(10, 200, 200)"/> <!-- Arrow -->
                    <text x="215" y="168" class="svg-label">NPN</text>
                    
                    <!-- Emitter to Ground -->
                    <line x1="200" y1="200" x2="200" y2="220" stroke="#333" stroke-width="1"/>
                    <line x1="180" y1="220" x2="220" y2="220" stroke="#333" stroke-width="1.5"/>
                    <line x1="185" y1="225" x2="215" y2="225" stroke="#333" stroke-width="1.5"/>
                    <line x1="190" y1="230" x2="210" y2="230" stroke="#333" stroke-width="1.5"/>

                    <!-- VBE Voltmeter (Base:180,165 to Emitter:200,200) -->
                    <circle cx="145" cy="205" r="10" stroke="#1565c0" fill="white" stroke-width="1"/>
                    <text x="145" y="205" class="svg-meter-symbol" fill="#1565c0">V</text>
                    <line x1="180" y1="165" x2="153" y2="200" stroke="#1565c0" stroke-dasharray="2,2" stroke-width="1"/>
                    <line x1="200" y1="200" x2="155" y2="208" stroke="#1565c0" stroke-dasharray="2,2" stroke-width="1"/>
                    <text id="svg-vbe-text" x="145" y="230" text-anchor="middle" class="svg-value">0.00V</text>

                    <!-- VCE Voltmeter (Collector:200,130 to Emitter:200,200) -->
                    <circle cx="255" cy="165" r="10" stroke="#1565c0" fill="white" stroke-width="1"/>
                    <text x="255" y="165" class="svg-meter-symbol" fill="#1565c0">V</text>
                    <line x1="200" y1="130" x2="247" y2="160" stroke="#1565c0" stroke-dasharray="2,2" stroke-width="1"/>
                    <line x1="200" y1="200" x2="247" y2="170" stroke="#1565c0" stroke-dasharray="2,2" stroke-width="1"/>
                    <text id="svg-vce-text" x="255" y="140" text-anchor="middle" class="svg-value">0.00V</text>
                </svg>
            </div>

            <div class="controls-data-area">
                <div class="controls">
                  <h3>Controls</h3>
                  <label for="vin-slider">Input Voltage (Vin_source): <span id="vin-value" style="font-weight:bold; color:#1e88e5;">0.00</span> V</label>
                  <input type="range" id="vin-slider" min="0" max="5" step="0.01" value="0">
                </div>

                <div class="data-display">
                  <h2>Live Parameters</h2>
                  <p><span>Base Voltage (VBE):</span> <span id="vbe-value" class="value-display">- V</span></p>
                  <p><span>Collector-Emitter Voltage (VCE):</span> <span id="vce-value" class="value-display">- V</span></p>
                  <p><span>Base Current (IB):</span> <span id="ib-value" class="value-display">- µA</span></p>
                  <p><span>Collector Current (IC):</span> <span id="ic-value" class="value-display">- mA</span></p>
                  <p><span>Operating Region:</span> <strong id="region-status">CUTOFF</strong></p>
                </div>
            </div>
        </div>

        <div class="explanations">
            <h2>Operating Regions Explained</h2>
            <div id="cutoff-explanation">
                <h3>Cutoff Region</h3>
                <p>When the voltage applied to the base circuit (Vin_source) is too low (typically causing VBE < 0.7V), the transistor is "OFF". No significant current flows from collector to emitter (IC ≈ 0) and no significant current flows into the base (IB ≈ 0). The transistor acts like an open switch. VCE is approximately equal to VCC.</p>
            </div>
            <div id="active-explanation">
                <h3>Active Region</h3>
                <p>When VBE is around 0.7V and the transistor is not saturated, it operates in the active region. Here, the collector current (IC) is proportional to the base current (IB), defined by IC = β * IB (where β or hFE is the current gain). The transistor acts like a current amplifier. VCE is greater than VCE_sat (around 0.2V) and changes as IB (and thus Vin_source) changes.</p>
            </div>
            <div id="saturation-explanation">
                <h3>Saturation Region</h3>
                <p>If the base current (IB) is increased further by raising Vin_source, the transistor becomes "fully ON" or saturated. In this state, the collector-emitter voltage (VCE) drops to a minimum value (VCE_sat, typically around 0.2V). Increasing IB further does not significantly decrease VCE. IC is limited by the external circuit (IC_sat ≈ (VCC - VCE_sat) / RC), not by β*IB. The transistor acts like a closed switch.</p>
            </div>
        </div>
    </div>

    <script>
        // --- Transistor Parameters ---
        const VCC = 5.0;        // Volts
        const RB_val = 10000;     // Ohms (10kΩ) - renamed to avoid conflict with SVG element id if any
        const RC_val = 1000;      // Ohms (1kΩ) - renamed
        const BETA = 100;       // Current Gain (hFE)
        const VBE_ON = 0.7;     // Volts (Base-Emitter turn-on voltage)
        const VCE_SAT = 0.2;    // Volts (Collector-Emitter saturation voltage)

        // --- DOM Elements ---
        const vinSlider = document.getElementById('vin-slider');
        const vinValueDisplay = document.getElementById('vin-value');

        const vbeValueDisplay = document.getElementById('vbe-value');
        const vceValueDisplay = document.getElementById('vce-value');
        const ibValueDisplay = document.getElementById('ib-value');
        const icValueDisplay = document.getElementById('ic-value');
        const regionStatusDisplay = document.getElementById('region-status');

        const svgVbeText = document.getElementById('svg-vbe-text');
        const svgVceText = document.getElementById('svg-vce-text');
        const svgIbText = document.getElementById('svg-ib-text');
        const svgIcText = document.getElementById('svg-ic-text');
        
        const cutoffExpDiv = document.getElementById('cutoff-explanation');
        const activeExpDiv = document.getElementById('active-explanation');
        const saturationExpDiv = document.getElementById('saturation-explanation');

        function updateCircuit() {
            const Vin_source = parseFloat(vinSlider.value);
            vinValueDisplay.textContent = Vin_source.toFixed(2);

            let VBE = 0, VCE = 0, IB = 0, IC = 0;
            let region = "CUTOFF";

            if (Vin_source < VBE_ON) {
                // --- CUTOFF Region ---
                VBE = Vin_source; 
                IB = 0;
                IC = 0;
                VCE = VCC;
                region = "CUTOFF";
            } else {
                // Potentially Active or Saturation
                VBE = VBE_ON;
                IB = (Vin_source - VBE_ON) / RB_val;
                if (IB < 0) IB = 0; 

                if (IB <= 1e-9) { // Consider IB effectively zero (e.g. 1nA threshold for cutoff)
                    VBE = Vin_source; // If IB is virtually zero, VBE tracks Vin_source
                    IB = 0; // Ensure IB is strictly zero for display
                    IC = 0;
                    VCE = VCC;
                    region = "CUTOFF";
                } else {
                    let IC_active_candidate = BETA * IB;
                    let VCE_candidate_if_active = VCC - (IC_active_candidate * RC_val);

                    if (VCE_candidate_if_active <= VCE_SAT) {
                        // --- SATURATION Region ---
                        region = "SATURATION";
                        VCE = VCE_SAT;
                        IC = (VCC - VCE_SAT) / RC_val;
                        if (IC < 0) IC = 0; // Should not happen with VCC > VCE_SAT
                    } else {
                        // --- ACTIVE Region ---
                        region = "ACTIVE";
                        IC = IC_active_candidate;
                        VCE = VCE_candidate_if_active;
                    }
                }
            }
            
            if (VCE < 0 && region !== "SATURATION") VCE = 0; // Prevent VCE from going negative if calculations are off
            if (VCE < VCE_SAT && region === "ACTIVE") VCE = VCE_SAT; // If active calculation dips below VCE_SAT
            if (VCE > VCC) VCE = VCC;


            // Update HTML Data Display
            vbeValueDisplay.textContent = VBE.toFixed(2) + " V";
            vceValueDisplay.textContent = VCE.toFixed(2) + " V";
            ibValueDisplay.textContent = (IB * 1e6).toFixed(2) + " µA";
            icValueDisplay.textContent = (IC * 1e3).toFixed(2) + " mA";
            
            regionStatusDisplay.textContent = region;
            regionStatusDisplay.className = region.toLowerCase();

            // Update SVG Text
            svgVbeText.textContent = VBE.toFixed(2) + "V";
            svgVceText.textContent = VCE.toFixed(2) + "V";
            svgIbText.textContent = (IB * 1e6).toFixed(1) + "µA"; // Slightly less precision for space
            svgIcText.textContent = (IC * 1e3).toFixed(1) + "mA"; // Slightly less precision for space

            // Update explanation highlighting
            cutoffExpDiv.classList.remove('cutoff-style', 'active-style', 'saturation-style');
            activeExpDiv.classList.remove('cutoff-style', 'active-style', 'saturation-style');
            saturationExpDiv.classList.remove('cutoff-style', 'active-style', 'saturation-style');

            if (region === "CUTOFF") {
                cutoffExpDiv.classList.add('cutoff-style');
            } else if (region === "ACTIVE") {
                activeExpDiv.classList.add('active-style');
            } else if (region === "SATURATION") {
                saturationExpDiv.classList.add('saturation-style');
            }
        }

        vinSlider.addEventListener('input', updateCircuit);

        // Initial call to set values
        updateCircuit();
    </script>
</body>
</html>
