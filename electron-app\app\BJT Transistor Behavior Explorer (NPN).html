<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BJT Transistor Explorer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
        }
        .container {
            width: 100%;
            max-width: 960px;
            background: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1877f2; /* Facebook blue, for a modern tech feel */
            text-align: center;
            margin-bottom: 25px;
            font-size: 1.8em;
        }
        h3 {
            color: #333;
            margin-top: 0;
            margin-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 8px;
            font-size: 1.2em;
        }
        .main-content {
            display: flex;
            flex-wrap: wrap;
            gap: 25px;
        }
        .circuit-column, .controls-column {
            flex: 1;
            min-width: 320px; /* Min width before stacking */
        }
        .circuit-diagram-wrapper { /* Wrapper for consistent padding and border */
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background-color: #f9fafb;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden; /* To contain the SVG properly */
        }
        .circuit-diagram-wrapper svg {
            max-width: 100%;
            height: auto; /* Maintain aspect ratio */
            display: block; /* Remove any extra space below SVG */
        }
        .controls label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }
        .controls input[type="range"] {
            width: 100%;
            margin-bottom: 15px;
            cursor: pointer;
        }
        .parameters p, .region-info p, .fixed-parameters p {
            margin: 10px 0;
            font-size: 0.95em;
            color: #444;
        }
        .parameters span, #vin-value {
            font-weight: bold;
            color: #1877f2;
        }
        .region-box {
            padding: 12px 15px;
            margin-top: 8px;
            margin-bottom: 12px;
            font-weight: bold;
            text-align: center;
            border-radius: 6px;
            border: 1px solid;
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
            font-size: 1.1em;
        }
        .region-box.cutoff { background-color: #e9ecef; color: #495057; border-color: #ced4da; }
        .region-box.active { background-color: #d1e7dd; color: #0f5132; border-color: #badbcc; }
        .region-box.saturation { background-color: #f8d7da; color: #842029; border-color: #f5c2c7; }

        #region-explanation {
            padding: 12px;
            background-color: #e7f3ff; /* Light blue background */
            border-left: 5px solid #1877f2;
            font-style: italic;
            color: #004085; /* Darker blue text for contrast */
            border-radius: 0 4px 4px 0;
        }
        .fixed-parameters {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        .fixed-parameters h3 {
            border-bottom: none; /* No border for this h3 */
            color: #1877f2;
        }

        /* SVG specific styles for BJT symbol color change */
        #bjt-representation-circle.cutoff { fill: #d0d0d0; } /* Neutral Gray */
        #bjt-representation-circle.active { fill: #a7f3d0; } /* Light Mint Green */
        #bjt-representation-circle.saturation { fill: #fecaca; } /* Light Coral/Red */

        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            h1 { font-size: 1.6em; }
            .circuit-column, .controls-column {
                min-width: 100%; /* Full width on smaller screens */
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>BJT Transistor Behavior Explorer (NPN)</h1>

        <div class="main-content">
            <div class="circuit-column">
                <h3>Circuit Diagram</h3>
                <div class="circuit-diagram-wrapper">
                    <svg width="350" height="230" viewBox="0 0 350 230" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMid meet">
                        <!-- VCC -->
                        <line x1="175" y1="10" x2="175" y2="30" stroke="black" stroke-width="2"/>
                        <text x="165" y="25" font-size="14" font-family="inherit" dominant-baseline="middle" text-anchor="end">VCC</text>
                        <text x="185" y="25" font-size="14" font-family="inherit" dominant-baseline="middle" text-anchor="start">(+12V)</text>

                        <!-- RC -->
                        <rect x="150" y="30" width="50" height="20" stroke="black" stroke-width="1.5" fill="#f0f0f0"/>
                        <text x="175" y="40" font-size="14" font-family="inherit" dominant-baseline="middle" text-anchor="middle">RC</text>
                        <text x="205" y="40" font-size="12" font-family="inherit" dominant-baseline="middle" text-anchor="start">(1kΩ)</text>
                        <line x1="175" y1="50" x2="175" y2="70" stroke="black" stroke-width="2"/>
                        <text x="180" y="65" font-size="14" font-family="inherit" fill="#007bff" dominant-baseline="middle" text-anchor="start">IC ↓</text>

                        <!-- NPN Transistor -->
                        <circle cx="175" cy="95" r="25" stroke="black" stroke-width="1.5" id="bjt-representation-circle" class="cutoff"/>
                        <line x1="175" y1="70" x2="175" y2="80" stroke="black" stroke-width="2"/> <!-- Collector to circle top -->
                        <line x1="125" y1="95" x2="150" y2="95" stroke="black" stroke-width="2"/> <!-- Base to circle left -->
                        <line x1="175" y1="110" x2="175" y2="140" stroke="black" stroke-width="2"/> <!-- Emitter to ground path -->
                        <polygon points="170,125 180,125 175,135" fill="black" stroke="black" stroke-width="1"/> <!-- Emitter arrow -->
                        <text x="180" y="88" font-size="14" font-family="inherit" dominant-baseline="middle" text-anchor="start">NPN</text>

                        <!-- RB -->
                        <rect x="50" y="85" width="50" height="20" stroke="black" stroke-width="1.5" fill="#f0f0f0"/>
                        <text x="75" y="95" font-size="14" font-family="inherit" dominant-baseline="middle" text-anchor="middle">RB</text>
                        <text x="45" y="95" font-size="12" font-family="inherit" dominant-baseline="middle" text-anchor="end">(10kΩ)</text>
                        <line x1="100" y1="95" x2="125" y2="95" stroke="black" stroke-width="2"/>
                        <text x="100" y="85" font-size="14" font-family="inherit" fill="#dc3545" dominant-baseline="middle" text-anchor="middle">IB →</text>

                        <!-- Vin -->
                        <line x1="10" y1="95" x2="50" y2="95" stroke="black" stroke-width="2"/>
                        <text x="25" y="85" font-size="14" font-family="inherit" dominant-baseline="middle" text-anchor="middle">Vin</text>
                        <polygon points="10,90 10,100 0,95" fill="black" stroke="black" stroke-width="1"/> <!-- Arrow for Vin input -->

                        <!-- Ground -->
                        <line x1="175" y1="140" x2="175" y2="150" stroke="black" stroke-width="2"/>
                        <line x1="155" y1="150" x2="195" y2="150" stroke="black" stroke-width="2"/>
                        <line x1="160" y1="155" x2="190" y2="155" stroke="black" stroke-width="2"/>
                        <line x1="165" y1="160" x2="185" y2="160" stroke="black" stroke-width="2"/>
                        <text x="175" y="175" font-size="14" font-family="inherit" dominant-baseline="middle" text-anchor="middle">GND</text>

                        <!-- VCE -->
                        <line x1="225" y1="70" x2="235" y2="70" stroke="#28a745" stroke-width="1.5"/> <!-- VCE top line marker -->
                        <line x1="225" y1="140" x2="235" y2="140" stroke="#28a745" stroke-width="1.5"/> <!-- VCE bottom line marker -->
                        <line x1="230" y1="70" x2="230" y2="140" stroke="#28a745" stroke-width="1.5" marker-start="url(#arrowhead-vce)" marker-end="url(#arrowhead-vce)"/>
                        <text x="240" y="105" font-size="14" font-family="inherit" fill="#28a745" dominant-baseline="middle" text-anchor="start">VCE</text>
                        <defs>
                            <marker id="arrowhead-vce" markerWidth="8" markerHeight="5.6" refX="0" refY="2.8" orient="auto-start-reverse">
                                <polygon points="0 0, 8 2.8, 0 5.6" fill="#28a745"/>
                            </marker>
                        </defs>
                    </svg>
                </div>
            </div>

            <div class="controls-column">
                <div class="controls">
                    <label for="vin-slider">Input Voltage (Vin): <span id="vin-value">0.00</span> V</label>
                    <input type="range" id="vin-slider" min="0" max="5" step="0.01" value="0">
                </div>

                <div class="parameters">
                    <h3>Calculated Values:</h3>
                    <p>Base Current (IB): <span id="ib-value">0.00</span> µA</p>
                    <p>Collector Current (IC): <span id="ic-value">0.00</span> mA</p>
                    <p>Collector-Emitter Voltage (VCE): <span id="vce-value">0.00</span> V</p>
                </div>

                <div class="region-info">
                    <h3>Region of Operation:</h3>
                    <div id="region-display" class="region-box cutoff">CUTOFF</div>
                    <p id="region-explanation"></p>
                </div>
            </div>
        </div>

        <div class="fixed-parameters">
            <h3>Circuit Parameters (Fixed):</h3>
            <p>VCC = 12V</p>
            <p>RB = 10 kΩ (Base Resistor)</p>
            <p>RC = 1 kΩ (Collector Resistor)</p>
            <p>Transistor Beta (β) = 100 (Current Gain)</p>
            <p>VBE(on) = 0.7V (Base-Emitter turn-on voltage)</p>
            <p>VCE(sat) ≈ 0.2V (Collector-Emitter saturation voltage, used for calculation boundary)</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const vinSlider = document.getElementById('vin-slider');
            const vinValueDisplay = document.getElementById('vin-value');
            const ibValueDisplay = document.getElementById('ib-value');
            const icValueDisplay = document.getElementById('ic-value');
            const vceValueDisplay = document.getElementById('vce-value');
            const regionDisplay = document.getElementById('region-display');
            const regionExplanation = document.getElementById('region-explanation');
            const bjtSymbolCircle = document.getElementById('bjt-representation-circle');

            // Circuit Parameters (constants)
            const VCC = 12.0;       // Volts
            const RB = 10000.0;     // Ohms (10 kΩ)
            const RC = 1000.0;      // Ohms (1 kΩ)
            const BETA = 100.0;
            const VBE_ON = 0.7;     // Volts
            const VCE_SAT = 0.2;    // Volts (Collector-Emitter saturation voltage)

            const explanations = {
                cutoff: 'The transistor is OFF. IB is approximately zero, and IC is approximately zero. VCE is approximately equal to VCC.',
                active: 'The transistor is acting as an amplifier. IC is approximately equal to Beta * IB, and VCE is between 0V (approx. VCEsat) and VCC.',
                saturation: 'The transistor is fully ON. VCE is approximately zero (at VCEsat ≈ 0.2V), and IC is limited by RC and VCC (IC ≈ (VCC - VCEsat) / RC).'
            };

            function updateCircuit() {
                const Vin = parseFloat(vinSlider.value);
                vinValueDisplay.textContent = Vin.toFixed(2);

                let IB = 0, IC = 0, VCE = 0;
                let region = '';
                let bjtSvgClass = ''; // Class for SVG element styling

                // Calculate Base Current (IB)
                if (Vin <= VBE_ON) {
                    IB = 0;
                } else {
                    IB = (Vin - VBE_ON) / RB;
                }
                if (IB < 0) IB = 0; // Ensure IB is not negative


                // Determine Region of Operation
                if (IB <= 0) { // Effectively if Vin <= VBE_ON
                    region = 'Cutoff';
                    bjtSvgClass = 'cutoff';
                    IC = 0;
                    VCE = VCC;
                } else {
                    let IC_active_check = BETA * IB;
                    let VCE_active_check = VCC - (IC_active_check * RC);

                    if (VCE_active_check <= VCE_SAT) {
                        region = 'Saturation';
                        bjtSvgClass = 'saturation';
                        VCE = VCE_SAT;
                        IC = (VCC - VCE_SAT) / RC;
                        // In saturation, IC may be less than BETA * IB.
                        // If calculated IC here is negative (VCC < VCE_SAT), clamp to 0, though not expected with these params.
                        if (IC < 0) IC = 0;
                    } else {
                        region = 'Active';
                        bjtSvgClass = 'active';
                        IC = IC_active_check;
                        VCE = VCE_active_check;
                    }
                }
                
                // Final checks/clamping for VCE
                if (VCE > VCC) VCE = VCC; // Should not happen if IC is positive.
                // If not in cutoff and VCE somehow drops below VCE_SAT (e.g. floating point issues for values very close to saturation boundary)
                // this is mostly covered by the saturation logic, but as a hard clamp:
                if (region !== 'Cutoff' && VCE < VCE_SAT) VCE = VCE_SAT;
                if (region === 'Cutoff') VCE = VCC; // Reinforce for clarity

                // Update displayed values
                ibValueDisplay.textContent = (IB * 1e6).toFixed(2); // IB in µA
                icValueDisplay.textContent = (IC * 1e3).toFixed(2); // IC in mA
                vceValueDisplay.textContent = VCE.toFixed(2);       // VCE in V

                // Update region display (text and box style)
                regionDisplay.textContent = region.toUpperCase();
                regionDisplay.className = 'region-box ' + region.toLowerCase();
                
                // Update BJT symbol color in SVG
                // Use classList for cleaner manipulation of SVG classes
                bjtSymbolCircle.classList.remove('cutoff', 'active', 'saturation');
                bjtSymbolCircle.classList.add(bjtSvgClass);

                // Update explanation text
                switch (region) {
                    case 'Cutoff':
                        regionExplanation.textContent = explanations.cutoff;
                        break;
                    case 'Active':
                        regionExplanation.textContent = explanations.active;
                        break;
                    case 'Saturation':
                        regionExplanation.textContent = explanations.saturation;
                        break;
                }
            }

            vinSlider.addEventListener('input', updateCircuit);

            // Initial calculation and display on page load
            updateCircuit();
        });
    </script>
</body>
</html>
