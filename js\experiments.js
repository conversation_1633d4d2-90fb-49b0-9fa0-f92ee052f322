/**
 * تفاصيل الدوائر للتجارب من المبتدئ إلى المتوسط
 *
 * هذا الملف يحتوي على مجموعة من التجارب المتدرجة في الصعوبة
 * كل تجربة تحتوي على وصف، مكونات، خطوات، نتائج متوقعة، وتطبيقات عملية
 */

// مصفوفة التجارب
const experiments = [
    // ===== المستوى المبتدئ =====
    {
        id: 'basic-resistor-circuit',
        title: 'دائرة المقاومة البسيطة',
        level: 'beginner',
        description: 'دائرة بسيطة تتكون من مصدر جهد ومقاومة لفهم قانون أوم وكيفية قياس الجهد والتيار.',
        components: [
            { type: 'dc_voltage', properties: { voltage: 9 }, count: 1 },
            { type: 'resistor', properties: { resistance: 1000 }, count: 1 },
            { type: 'voltmeter', count: 1 },
            { type: 'ammeter', count: 1 }
        ],
        circuit: {
            components: [
                { id: 'v1', type: 'dc_voltage', x: 200, y: 200, rotation: 0, properties: { voltage: 9 } },
                { id: 'r1', type: 'resistor', x: 300, y: 200, rotation: 0, properties: { resistance: 1000 } },
                { id: 'vm1', type: 'voltmeter', x: 300, y: 150, rotation: 0 },
                { id: 'am1', type: 'ammeter', x: 250, y: 200, rotation: 0 }
            ],
            wires: [
                { start: { x: 200, y: 170 }, end: { x: 200, y: 120 }, startComponent: 'v1', startTerminal: 'positive', endComponent: null, endTerminal: null },
                { start: { x: 200, y: 120 }, end: { x: 350, y: 120 }, startComponent: null, startTerminal: null, endComponent: null, endTerminal: null },
                { start: { x: 350, y: 120 }, end: { x: 350, y: 200 }, startComponent: null, startTerminal: null, endComponent: 'r1', endTerminal: 'output' },
                { start: { x: 250, y: 200 }, end: { x: 270, y: 200 }, startComponent: 'am1', startTerminal: 'output', endComponent: 'r1', endTerminal: 'input' },
                { start: { x: 200, y: 230 }, end: { x: 200, y: 280 }, startComponent: 'v1', startTerminal: 'negative', endComponent: null, endTerminal: null },
                { start: { x: 200, y: 280 }, end: { x: 350, y: 280 }, startComponent: null, startTerminal: null, endComponent: null, endTerminal: null },
                { start: { x: 350, y: 280 }, end: { x: 350, y: 200 }, startComponent: null, startTerminal: null, endComponent: null, endTerminal: null },
                { start: { x: 300, y: 150 }, end: { x: 270, y: 150 }, startComponent: 'vm1', startTerminal: 'positive', endComponent: null, endTerminal: null },
                { start: { x: 270, y: 150 }, end: { x: 270, y: 200 }, startComponent: null, startTerminal: null, endComponent: null, endTerminal: null },
                { start: { x: 330, y: 150 }, end: { x: 350, y: 150 }, startComponent: 'vm1', startTerminal: 'negative', endComponent: null, endTerminal: null },
                { start: { x: 350, y: 150 }, end: { x: 350, y: 200 }, startComponent: null, startTerminal: null, endComponent: null, endTerminal: null },
                { start: { x: 230, y: 200 }, end: { x: 200, y: 200 }, startComponent: 'am1', startTerminal: 'input', endComponent: 'v1', endTerminal: 'output' }
            ]
        },
        steps: [
            'قم بتوصيل المصدر الكهربائي (9 فولت) بالدائرة.',
            'قم بتوصيل المقاومة (1 كيلو أوم) على التوالي مع المصدر.',
            'قم بتوصيل الأميتر على التوالي مع المقاومة لقياس التيار.',
            'قم بتوصيل الفولتميتر على التوازي مع المقاومة لقياس فرق الجهد.',
            'قم بتشغيل المحاكاة وقراءة قيم الجهد والتيار.'
        ],
        expected_results: [
            'قراءة الفولتميتر: 9 فولت',
            'قراءة الأميتر: 9 ميلي أمبير (حسب قانون أوم: I = V/R = 9V/1000Ω = 0.009A = 9mA)'
        ],
        applications: [
            'فهم قانون أوم وتطبيقاته',
            'تعلم كيفية قياس الجهد والتيار في الدوائر الكهربائية',
            'أساس لفهم الدوائر الكهربائية البسيطة'
        ],
        theory: 'تعتمد هذه الدائرة على قانون أوم الذي ينص على أن التيار المار في موصل يتناسب طردياً مع فرق الجهد بين طرفيه وعكسياً مع مقاومته. القانون: I = V/R حيث I هو التيار بالأمبير، V هو فرق الجهد بالفولت، و R هي المقاومة بالأوم.'
    },

    {
        id: 'voltage-divider',
        title: 'مقسم الجهد',
        level: 'beginner',
        description: 'دائرة مقسم جهد تستخدم مقاومتين متصلتين على التوالي للحصول على جهد مخرج أقل من جهد المصدر.',
        components: [
            { type: 'dc_voltage', properties: { voltage: 12 }, count: 1 },
            { type: 'resistor', properties: { resistance: 1000 }, count: 1 },
            { type: 'resistor', properties: { resistance: 2000 }, count: 1 },
            { type: 'voltmeter', count: 2 }
        ],
        circuit: {
            components: [
                { id: 'v1', type: 'dc_voltage', x: 200, y: 200, rotation: 0, properties: { voltage: 12 } },
                { id: 'r1', type: 'resistor', x: 300, y: 200, rotation: 90, properties: { resistance: 1000 } },
                { id: 'r2', type: 'resistor', x: 300, y: 300, rotation: 90, properties: { resistance: 2000 } },
                { id: 'vm1', type: 'voltmeter', x: 350, y: 200, rotation: 0 },
                { id: 'vm2', type: 'voltmeter', x: 350, y: 300, rotation: 0 }
            ],
            wires: [
                { start: { x: 200, y: 170 }, end: { x: 200, y: 120 }, startComponent: 'v1', startTerminal: 'positive', endComponent: null, endTerminal: null },
                { start: { x: 200, y: 120 }, end: { x: 300, y: 120 }, startComponent: null, startTerminal: null, endComponent: null, endTerminal: null },
                { start: { x: 300, y: 120 }, end: { x: 300, y: 170 }, startComponent: null, startTerminal: null, endComponent: 'r1', endTerminal: 'input' },
                { start: { x: 300, y: 230 }, end: { x: 300, y: 270 }, startComponent: 'r1', startTerminal: 'output', endComponent: 'r2', endTerminal: 'input' },
                { start: { x: 300, y: 330 }, end: { x: 300, y: 380 }, startComponent: 'r2', startTerminal: 'output', endComponent: null, endTerminal: null },
                { start: { x: 300, y: 380 }, end: { x: 200, y: 380 }, startComponent: null, startTerminal: null, endComponent: null, endTerminal: null },
                { start: { x: 200, y: 380 }, end: { x: 200, y: 230 }, startComponent: null, startTerminal: null, endComponent: 'v1', endTerminal: 'negative' },
                { start: { x: 350, y: 200 }, end: { x: 320, y: 200 }, startComponent: 'vm1', startTerminal: 'positive', endComponent: null, endTerminal: null },
                { start: { x: 320, y: 200 }, end: { x: 300, y: 200 }, startComponent: null, startTerminal: null, endComponent: null, endTerminal: null },
                { start: { x: 380, y: 200 }, end: { x: 400, y: 200 }, startComponent: 'vm1', startTerminal: 'negative', endComponent: null, endTerminal: null },
                { start: { x: 400, y: 200 }, end: { x: 400, y: 380 }, startComponent: null, startTerminal: null, endComponent: null, endTerminal: null },
                { start: { x: 400, y: 380 }, end: { x: 300, y: 380 }, startComponent: null, startTerminal: null, endComponent: null, endTerminal: null },
                { start: { x: 350, y: 300 }, end: { x: 320, y: 300 }, startComponent: 'vm2', startTerminal: 'positive', endComponent: null, endTerminal: null },
                { start: { x: 320, y: 300 }, end: { x: 300, y: 300 }, startComponent: null, startTerminal: null, endComponent: null, endTerminal: null },
                { start: { x: 380, y: 300 }, end: { x: 400, y: 300 }, startComponent: 'vm2', startTerminal: 'negative', endComponent: null, endTerminal: null },
                { start: { x: 400, y: 300 }, end: { x: 400, y: 380 }, startComponent: null, startTerminal: null, endComponent: null, endTerminal: null }
            ]
        },
        steps: [
            'قم بتوصيل المصدر الكهربائي (12 فولت) بالدائرة.',
            'قم بتوصيل المقاومتين R1 (1 كيلو أوم) و R2 (2 كيلو أوم) على التوالي.',
            'قم بتوصيل الفولتميتر الأول على التوازي مع المقاومة R1 لقياس فرق الجهد عليها.',
            'قم بتوصيل الفولتميتر الثاني على التوازي مع المقاومة R2 لقياس فرق الجهد عليها.',
            'قم بتشغيل المحاكاة وقراءة قيم الجهد على كل مقاومة.'
        ],
        expected_results: [
            'قراءة الفولتميتر الأول (على R1): 4 فولت',
            'قراءة الفولتميتر الثاني (على R2): 8 فولت',
            'مجموع الجهود: 12 فولت (يساوي جهد المصدر)'
        ],
        applications: [
            'تقسيم الجهد للحصول على قيم جهد مختلفة من مصدر واحد',
            'استخدام في دوائر الاستشعار والتحكم',
            'ضبط مستويات الإشارة في الدوائر الإلكترونية'
        ],
        theory: 'في دائرة مقسم الجهد، يتم توزيع الجهد بين المقاومات المتصلة على التوالي بنسبة قيمها. الجهد على أي مقاومة يساوي: V_R = V_in × (R / R_total) حيث V_R هو الجهد على المقاومة، V_in هو جهد المصدر، R هي قيمة المقاومة، و R_total هي مجموع المقاومات.'
    },

    // ===== المستوى المتوسط =====
    {
        id: 'rc-circuit',
        title: 'دائرة RC (مقاومة-مكثف)',
        level: 'intermediate',
        description: 'دائرة تتكون من مقاومة ومكثف لدراسة عملية الشحن والتفريغ والثابت الزمني.',
        components: [
            { type: 'dc_voltage', properties: { voltage: 9 }, count: 1 },
            { type: 'resistor', properties: { resistance: 10000 }, count: 1 },
            { type: 'capacitor', properties: { capacitance: 100 }, count: 1 },
            { type: 'voltmeter', count: 1 },
            { type: 'oscilloscope', count: 1 }
        ],
        circuit: {
            components: [
                { id: 'v1', type: 'dc_voltage', x: 200, y: 200, rotation: 0, properties: { voltage: 9 } },
                { id: 'r1', type: 'resistor', x: 300, y: 200, rotation: 0, properties: { resistance: 10000 } },
                { id: 'c1', type: 'capacitor', x: 400, y: 250, rotation: 90, properties: { capacitance: 100 } },
                { id: 'vm1', type: 'voltmeter', x: 450, y: 250, rotation: 0 },
                { id: 'osc1', type: 'oscilloscope', x: 500, y: 250, rotation: 0 }
            ],
            wires: [
                { start: { x: 200, y: 170 }, end: { x: 200, y: 120 }, startComponent: 'v1', startTerminal: 'positive', endComponent: null, endTerminal: null },
                { start: { x: 200, y: 120 }, end: { x: 300, y: 120 }, startComponent: null, startTerminal: null, endComponent: null, endTerminal: null },
                { start: { x: 300, y: 120 }, end: { x: 300, y: 170 }, startComponent: null, startTerminal: null, endComponent: 'r1', endTerminal: 'input' },
                { start: { x: 300, y: 230 }, end: { x: 300, y: 250 }, startComponent: 'r1', startTerminal: 'output', endComponent: null, endTerminal: null },
                { start: { x: 300, y: 250 }, end: { x: 400, y: 250 }, startComponent: null, startTerminal: null, endComponent: 'c1', endTerminal: 'input' },
                { start: { x: 400, y: 280 }, end: { x: 400, y: 350 }, startComponent: 'c1', startTerminal: 'output', endComponent: null, endTerminal: null },
                { start: { x: 400, y: 350 }, end: { x: 200, y: 350 }, startComponent: null, startTerminal: null, endComponent: null, endTerminal: null },
                { start: { x: 200, y: 350 }, end: { x: 200, y: 230 }, startComponent: null, startTerminal: null, endComponent: 'v1', endTerminal: 'negative' },
                { start: { x: 450, y: 250 }, end: { x: 420, y: 250 }, startComponent: 'vm1', startTerminal: 'positive', endComponent: null, endTerminal: null },
                { start: { x: 420, y: 250 }, end: { x: 400, y: 250 }, startComponent: null, startTerminal: null, endComponent: null, endTerminal: null },
                { start: { x: 480, y: 250 }, end: { x: 500, y: 250 }, startComponent: 'vm1', startTerminal: 'negative', endComponent: null, endTerminal: null },
                { start: { x: 500, y: 250 }, end: { x: 500, y: 350 }, startComponent: null, startTerminal: null, endComponent: null, endTerminal: null },
                { start: { x: 500, y: 350 }, end: { x: 400, y: 350 }, startComponent: null, startTerminal: null, endComponent: null, endTerminal: null },
                { start: { x: 500, y: 240 }, end: { x: 400, y: 240 }, startComponent: 'osc1', startTerminal: 'ch1_pos', endComponent: null, endTerminal: null },
                { start: { x: 500, y: 260 }, end: { x: 500, y: 350 }, startComponent: 'osc1', startTerminal: 'ch1_neg', endComponent: null, endTerminal: null }
            ]
        },
        steps: [
            'قم بتوصيل المصدر الكهربائي (9 فولت) بالدائرة.',
            'قم بتوصيل المقاومة (10 كيلو أوم) على التوالي مع المكثف (100 ميكروفاراد).',
            'قم بتوصيل الفولتميتر على التوازي مع المكثف لقياس فرق الجهد عليه.',
            'قم بتوصيل راسم الإشارة لمراقبة تغير الجهد على المكثف مع الزمن.',
            'قم بتشغيل المحاكاة ومراقبة عملية شحن المكثف.',
            'قم بفصل المصدر ومراقبة عملية تفريغ المكثف.'
        ],
        expected_results: [
            'عند بدء المحاكاة، يبدأ المكثف بالشحن وترتفع قراءة الفولتميتر تدريجياً حتى تصل إلى 9 فولت.',
            'الثابت الزمني للدائرة: τ = R × C = 10000Ω × 100μF = 1 ثانية.',
            'يصل المكثف إلى 63% من الشحن الكامل (5.67 فولت) بعد ثابت زمني واحد (1 ثانية).',
            'يصل المكثف إلى 95% من الشحن الكامل (8.55 فولت) بعد 3 ثوابت زمنية (3 ثواني).',
            'عند فصل المصدر، يبدأ المكثف بالتفريغ وتنخفض قراءة الفولتميتر تدريجياً حتى تصل إلى 0 فولت.'
        ],
        applications: [
            'دوائر التوقيت والمؤقتات',
            'دوائر الترشيح وإزالة الضوضاء',
            'دوائر التكامل والتفاضل',
            'مزودات الطاقة المستمرة'
        ],
        theory: 'تعتمد دائرة RC على خصائص شحن وتفريغ المكثف عبر مقاومة. عند شحن المكثف، يتبع الجهد المعادلة: V(t) = V_s × (1 - e^(-t/RC)) حيث V_s هو جهد المصدر، R هي المقاومة، C هي سعة المكثف، و t هو الزمن. الثابت الزمني τ = RC يحدد سرعة الشحن والتفريغ.'
    },

    // ===== تجارب الترانزستور =====
    {
        id: 'transistor-experiment-1',
        title: 'تجربة 1: خصائص الترانزستور NPN في الانحياز الأمامي',
        level: 'intermediate',
        description: 'دراسة خصائص الترانزستور NPN في حالة الانحياز الأمامي وتحديد منحنى الخصائص الإدخال والإخراج.',
        components: [
            { type: 'dc_voltage', properties: { voltage: 5 }, count: 1 },
            { type: 'dc_voltage', properties: { voltage: 10 }, count: 1 },
            { type: 'resistor', properties: { resistance: 10000 }, count: 1 },
            { type: 'resistor', properties: { resistance: 1000 }, count: 1 },
            { type: 'npn_transistor', properties: { gain: 100 }, count: 1 },
            { type: 'voltmeter', count: 2 },
            { type: 'ammeter', count: 2 }
        ],
        circuit: {
            components: [
                { id: 'vcc', type: 'dc_voltage', x: 400, y: 100, rotation: 0, properties: { voltage: 10 } },
                { id: 'vbb', type: 'dc_voltage', x: 150, y: 200, rotation: 0, properties: { voltage: 5 } },
                { id: 'rb', type: 'resistor', x: 250, y: 200, rotation: 0, properties: { resistance: 10000 } },
                { id: 'rc', type: 'resistor', x: 400, y: 150, rotation: 90, properties: { resistance: 1000 } },
                { id: 'q1', type: 'npn_transistor', x: 350, y: 200, rotation: 0, properties: { gain: 100 } },
                { id: 'vbe', type: 'voltmeter', x: 300, y: 250, rotation: 0 },
                { id: 'vce', type: 'voltmeter', x: 450, y: 200, rotation: 0 },
                { id: 'ib', type: 'ammeter', x: 300, y: 200, rotation: 0 },
                { id: 'ic', type: 'ammeter', x: 400, y: 200, rotation: 90 }
            ],
            wires: [
                // توصيل مصدر الجهد VCC
                { start: { x: 400, y: 70 }, end: { x: 400, y: 120 }, startComponent: 'vcc', startTerminal: 'positive', endComponent: 'rc', endTerminal: 'input' },
                { start: { x: 400, y: 130 }, end: { x: 400, y: 180 }, startComponent: 'vcc', startTerminal: 'negative', endComponent: 'rc', endTerminal: 'output' },

                // توصيل مصدر الجهد VBB
                { start: { x: 150, y: 170 }, end: { x: 150, y: 200 }, startComponent: 'vbb', startTerminal: 'positive', endComponent: 'rb', endTerminal: 'input' },
                { start: { x: 150, y: 230 }, end: { x: 150, y: 300 }, startComponent: 'vbb', startTerminal: 'negative', endComponent: null, endTerminal: null },

                // توصيل المقاومة RB
                { start: { x: 220, y: 200 }, end: { x: 280, y: 200 }, startComponent: 'rb', startTerminal: 'output', endComponent: 'ib', endTerminal: 'input' },

                // توصيل الترانزستور
                { start: { x: 320, y: 200 }, end: { x: 350, y: 200 }, startComponent: 'ib', startTerminal: 'output', endComponent: 'q1', endTerminal: 'base' },
                { start: { x: 350, y: 180 }, end: { x: 350, y: 150 }, startComponent: 'q1', startTerminal: 'collector', endComponent: 'ic', endTerminal: 'input' },
                { start: { x: 350, y: 220 }, end: { x: 350, y: 300 }, startComponent: 'q1', startTerminal: 'emitter', endComponent: null, endTerminal: null },

                // توصيل الأميتر IC
                { start: { x: 400, y: 180 }, end: { x: 350, y: 180 }, startComponent: 'ic', startTerminal: 'output', endComponent: null, endTerminal: null },

                // توصيل الفولتميتر VBE
                { start: { x: 300, y: 250 }, end: { x: 350, y: 250 }, startComponent: 'vbe', startTerminal: 'positive', endComponent: null, endTerminal: null },
                { start: { x: 350, y: 250 }, end: { x: 350, y: 200 }, startComponent: null, startTerminal: null, endComponent: null, endTerminal: null },
                { start: { x: 330, y: 250 }, end: { x: 330, y: 300 }, startComponent: 'vbe', startTerminal: 'negative', endComponent: null, endTerminal: null },

                // توصيل الفولتميتر VCE
                { start: { x: 450, y: 200 }, end: { x: 400, y: 200 }, startComponent: 'vce', startTerminal: 'positive', endComponent: null, endTerminal: null },
                { start: { x: 400, y: 200 }, end: { x: 350, y: 200 }, startComponent: null, startTerminal: null, endComponent: null, endTerminal: null },
                { start: { x: 480, y: 200 }, end: { x: 480, y: 300 }, startComponent: 'vce', startTerminal: 'negative', endComponent: null, endTerminal: null },

                // توصيل الأرضي المشترك
                { start: { x: 150, y: 300 }, end: { x: 480, y: 300 }, startComponent: null, startTerminal: null, endComponent: null, endTerminal: null }
            ]
        },
        steps: [
            'قم بتوصيل الدائرة كما هو موضح في الرسم.',
            'قم بضبط مصدر الجهد VCC على 10 فولت.',
            'قم بضبط مصدر الجهد VBB على 5 فولت.',
            'قم بتشغيل المحاكاة وقراءة قيم الجهود والتيارات.',
            'قم بتغيير قيمة VBB تدريجياً من 0 إلى 5 فولت وسجل قراءات VBE، IB، IC، VCE.',
            'ارسم منحنى الإدخال (العلاقة بين VBE و IB).',
            'ارسم منحنى الإخراج (العلاقة بين VCE و IC).',
            'احسب معامل التضخيم β = IC/IB.'
        ],
        expected_results: [
            'عند VBE < 0.7 فولت: الترانزستور في حالة القطع، IB ≈ 0، IC ≈ 0.',
            'عند VBE ≈ 0.7 فولت: الترانزستور يبدأ بالتوصيل، IB يزداد تدريجياً، IC = β × IB.',
            'عند VBE > 0.7 فولت: الترانزستور في حالة التوصيل، IB و IC يزدادان مع زيادة VBE.',
            'قيمة معامل التضخيم β تكون حوالي 100 (حسب خصائص الترانزستور).',
            'عند زيادة IB، تنخفض قيمة VCE بسبب زيادة هبوط الجهد على RC.'
        ],
        applications: [
            'دوائر التضخيم',
            'دوائر التحكم الإلكترونية',
            'دوائر التبديل',
            'مصادر التيار الثابت',
            'دوائر المنطق الرقمية'
        ],
        theory: 'الترانزستور ثنائي القطبية (BJT) هو عنصر إلكتروني نشط يستخدم لتضخيم الإشارات الكهربائية أو كمفتاح إلكتروني. في حالة الانحياز الأمامي، يتم توصيل وصلة القاعدة-باعث (B-E) في الاتجاه الأمامي، ووصلة المجمع-باعث (C-E) في الاتجاه العكسي. عندما يكون جهد القاعدة-باعث (VBE) أكبر من جهد العتبة (حوالي 0.7 فولت للسيليكون)، يبدأ تيار القاعدة (IB) بالتدفق، مما يؤدي إلى تدفق تيار المجمع (IC) الذي يكون أكبر بمقدار معامل التضخيم β. العلاقة بينهما: IC = β × IB.'
    }
];

// تصدير المصفوفة لاستخدامها في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { experiments };
}
