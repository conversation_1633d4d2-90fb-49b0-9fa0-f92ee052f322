{"name": "electronic-circuits-lab", "version": "1.0.0", "description": "Electronic Circuits Lab - Virtual Simulation Workbench", "main": "main.js", "scripts": {"start": "electron .", "pack": "electron-builder --dir", "dist": "electron-builder", "postinstall": "electron-builder install-app-deps"}, "keywords": ["electronics", "circuits", "simulation", "transistor", "lab"], "author": "Your Name", "license": "MIT", "devDependencies": {"electron": "^25.0.0", "electron-builder": "^24.4.0"}, "build": {"appId": "com.electronic.circuits.lab", "productName": "Electronic Circuits Lab", "directories": {"output": "dist"}, "win": {"target": ["nsis"], "icon": "build/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true}, "files": ["**/*", "!node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!node_modules/*.d.ts", "!node_modules/.bin", "!dist/**/*", "!build/**/*", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,__pycache__,thumbs.db,.gitignore,.gitattributes,.flowconfig,.yarn-metadata.json,.idea,appveyor.yml,.travis.yml,circle.yml,npm-debug.log,.nyc_output,yarn.lock,.yarn-integrity}"]}}