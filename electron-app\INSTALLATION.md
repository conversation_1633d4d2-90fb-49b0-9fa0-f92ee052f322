# Electronic Circuits Lab - Installation Guide

This guide will help you install and run the Electronic Circuits Lab application on your computer.

## System Requirements

- Windows 7 or later (64-bit)
- 4 GB RAM (minimum)
- 500 MB free disk space
- 1280x720 screen resolution (minimum)

## Installation Steps

1. Download the installer (`Electronic-Circuits-Lab-Setup-x.x.x.exe`) from the provided location.

2. Double-click the installer file to start the installation process.

3. Follow the on-screen instructions:
   - Choose the installation location
   - Select whether to create a desktop shortcut
   - Click "Install" to begin the installation

4. Wait for the installation to complete.

5. Click "Finish" to exit the installer.

## Running the Application

You can run the application in one of the following ways:

- Double-click the desktop shortcut (if you chose to create one during installation)
- Navigate to the Start Menu > Electronic Circuits Lab > Electronic Circuits Lab
- Go to the installation folder and double-click `Electronic Circuits Lab.exe`

## First-Time Setup

When you run the application for the first time:

1. You may see a security warning from Windows. This is normal for applications that are not signed with a certificate from a recognized certificate authority. Click "Run" to continue.

2. The application will open to the main screen, where you can start creating and simulating electronic circuits.

## Uninstalling

To uninstall the application:

1. Go to Control Panel > Programs > Programs and Features
2. Find "Electronic Circuits Lab" in the list
3. Click "Uninstall" and follow the on-screen instructions

Alternatively, you can use the uninstaller in the installation directory.

## Troubleshooting

If you encounter any issues:

1. **Application doesn't start**: Make sure your computer meets the system requirements and try reinstalling the application.

2. **Graphics issues**: Update your graphics drivers to the latest version.

3. **Error messages**: Note the exact error message and contact support with this information.

## Support

If you need assistance, please contact:

- Email: <EMAIL>
- Website: www.example.com/support
