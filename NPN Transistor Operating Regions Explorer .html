<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transistor Operating Regions Explorer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            line-height: 1.6;
            margin: 0;
            padding: 15px;
            background-color: #f0f2f5;
            color: #333;
        }
        .container {
            max-width: 900px;
            margin: auto;
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #1a237e; /* Dark blue */
            text-align: center;
            margin-top: 0;
            margin-bottom: 20px;
        }
        p {
            margin-bottom: 15px;
        }
        .main-content {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 25px;
        }
        .circuit-section, .controls-section {
            flex: 1;
            min-width: 280px; /* Min width before wrapping */
            padding: 15px;
            border: 1px solid #d1d9e6; /* Lighter border */
            border-radius: 6px;
            background-color: #f8f9fa; /* Very light grey */
        }
        .circuit-section svg {
            width: 100%;
            max-width: 320px;
            height: auto;
            display: block;
            margin: 0 auto;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500; /* Medium weight */
            color: #34495e; /* Darker grey-blue */
        }
        input[type="range"] {
            width: 100%;
            margin-bottom: 15px;
            cursor: pointer;
        }
        .plot-section {
            width: 100%;
            margin-bottom: 25px;
        }
        #transistorPlot {
            width: 100%;
            /* height is set by JS for aspect ratio */
            border: 1px solid #ced4da; /* Grey border */
            border-radius: 5px;
            background-color: #ffffff;
        }
        .value-display p {
            margin: 8px 0;
            font-size: 1em; /* Adjusted for consistency */
        }
        .value-display span {
            font-weight: bold;
            color: #007bff; /* Bootstrap primary blue */
        }
        .value-display .param-details {
            font-size:0.85em; 
            margin-top:15px; 
            color: #555;
            line-height: 1.4;
        }
        .value-display .param-details span {
            color: #28a745; /* Green for param values */
        }
        .explanation-section {
            padding: 20px;
            border: 1px solid #d1d9e6;
            border-radius: 6px;
            background-color: #e9ecef; /* Light grey for explanation */
        }
        .explanation-section h3 {
            margin-top: 0;
            color: #1a237e; /* Dark blue, same as h2 */
            margin-bottom: 10px;
        }
        .explanation-section ul {
            list-style-type: none;
            padding-left: 0;
        }
        .explanation-section li {
            margin-bottom: 12px;
            padding-left: 20px;
            position: relative;
        }
        .explanation-section li::before {
            content: "•";
            position: absolute;
            left: 0;
            color: #007bff; /* Blue bullet point */
            font-weight: bold;
        }
        .explanation-section strong {
            color: #333;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            body { padding: 10px; }
            .container { padding: 15px; }
            .main-content {
                flex-direction: column;
            }
            .circuit-section, .controls-section {
                min-width: 100%; /* Take full width on smaller screens */
            }
            h1 { font-size: 1.8em; }
            h2 { font-size: 1.4em; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>NPN Transistor Operating Regions Explorer</h1>
        <p>This interactive tool helps you understand the behavior of an NPN transistor in a common-emitter configuration. By adjusting the Base Voltage (V<sub>B</sub>) using the slider, you can observe how the Collector Current (I<sub>C</sub>) changes and how the transistor transitions through its three main operating regions: Cutoff, Active, and Saturation. The I<sub>C</sub> vs. V<sub>B</sub> plot dynamically updates to show the current operating point.</p>

        <div class="main-content">
            <div class="circuit-section">
                <h2>Circuit Diagram</h2>
                <svg viewBox="0 0 220 180" xmlns="http://www.w3.org/2000/svg" aria-labelledby="circuitTitle circuitDesc">
                    <title id="circuitTitle">Common-Emitter NPN Transistor Amplifier Circuit</title>
                    <desc id="circuitDesc">A diagram showing VCC connected to RC and the collector. RB is connected between VB and the base. The emitter is grounded. The transistor is NPN.</desc>
                    <style>
                        .line { stroke: black; stroke-width: 1.5; }
                        .component-label { font-size: 10px; font-family: Arial, sans-serif; }
                        .node-label { font-size: 10px; font-family: Arial, sans-serif; font-weight: bold; }
                        .resistor-box { fill: none; stroke: black; stroke-width: 1.5; }
                        .transistor-circle { fill: none; stroke: black; stroke-width: 1.5; }
                        .transistor-lines { stroke: black; stroke-width: 1.5; }
                        .arrowhead { fill: black; }
                    </style>

                    <!-- VCC -->
                    <text x="105" y="18" class="node-label">VCC (+)</text>
                    <line class="line" x1="100" y1="8" x2="100" y2="25" />
                    
                    <!-- RC -->
                    <line class="line" x1="100" y1="25" x2="100" y2="40" />
                    <rect x="90" y="40" width="20" height="12" class="resistor-box" />
                    <text x="115" y="48" class="component-label">RC</text>
                    <line class="line" x1="100" y1="52" x2="100" y2="70" /> <!-- To Collector -->

                    <!-- Transistor Q1 -->
                    <circle cx="100" cy="95" r="20" class="transistor-circle"/> <!-- Center of transistor -->
                    <line class="transistor-lines" x1="100" y1="70" x2="100" y2="80" /> <!-- Collector line to transistor body -->
                    <line class="transistor-lines" x1="85" y1="85" x2="115" y2="85" /> <!-- Base plate inside circle -->
                    <line class="transistor-lines" x1="70" y1="95" x2="85" y2="85" /> <!-- Base connection to plate -->
                    
                    <line class="transistor-lines" x1="100" y1="110" x2="100" y2="125" /> <!-- Emitter line from transistor body -->
                    <line class="transistor-lines" x1="88" y1="105" x2="100" y2="95" /> <!-- C-E angled line 1 -->
                    <line class="transistor-lines" x1="88" y1="85" x2="100" y2="95" /> <!-- B-E angled line 2 -->
                    
                    <polygon class="arrowhead" points="100,125 96,117 104,117" /> <!-- Emitter Arrow (NPN: pointing out) -->
                    <text x="125" y="98" class="component-label">Q1</text>

                    <!-- RB -->
                    <text x="10" y="98" class="node-label">VB</text>
                    <line class="line" x1="30" y1="95" x2="50" y2="95" /> <!-- VB input line -->
                    <rect x="50" y="89" width="20" height="12" class="resistor-box" />
                    <text x="52" y="86" class="component-label">RB</text>
                    <line class="line" x1="70" y1="95" x2="80" y2="95" /> <!-- RB to Base line -->


                    <!-- Emitter to Ground -->
                    <line class="line" x1="100" y1="125" x2="100" y2="145" />
                    <line class="line" x1="85" y1="145" x2="115" y2="145" /> <!-- Ground symbol top -->
                    <line class="line" x1="90" y1="150" x2="110" y2="150" />
                    <line class="line" x1="95" y1="155" x2="105" y2="155" />
                </svg>
            </div>

            <div class="controls-section">
                <h2>Controls & Values</h2>
                <div class="value-display">
                    <label for="vbSlider">Base Voltage (V<sub>B</sub>): <span id="vbValue">0.00</span> V</label>
                    <input type="range" id="vbSlider" min="0" step="0.01" value="0"> <!-- max will be set by JS -->
                    <p>Collector Current (I<sub>C</sub>): <span id="icValue">0.00</span> mA</p>
                    <p>Operating Region: <span id="regionValue">Cutoff</span></p>
                    <div class="param-details">
                        <strong>Circuit Parameters:</strong><br>
                        VCC = <span id="vccParam">5.0</span>V, RB = <span id="rbParam">10</span>kΩ, RC = <span id="rcParam">1</span>kΩ, β = <span id="betaParam">100</span><br>
                        V<sub>BE(on)</sub> = <span id="vbeOnParam">0.7</span>V, V<sub>CE(sat)</sub> = <span id="vceSatParam">0.2</span>V
                    </div>
                </div>
            </div>
        </div>

        <div class="plot-section">
            <h2>I<sub>C</sub> vs. V<sub>B</sub> Plot</h2>
            <canvas id="transistorPlot"></canvas>
        </div>

        <div class="explanation-section">
            <h3>Operating Regions Explained</h3>
            <ul>
                <li><strong>Cutoff Region:</strong> The transistor is 'OFF'. The base voltage (V<sub>B</sub>) is below the turn-on voltage (V<sub>BE(on)</sub> ≈ 0.7V), so the base-emitter junction is not forward-biased. No significant base current (I<sub>B</sub>) flows, and thus, collector current (I<sub>C</sub>) is practically zero. The transistor acts like an open switch.</li>
                <li><strong>Active Region:</strong> The transistor acts as an amplifier. V<sub>B</sub> is high enough to forward-bias the base-emitter junction, allowing I<sub>B</sub> to flow. The collector current I<sub>C</sub> is proportionally controlled by I<sub>B</sub> (I<sub>C</sub> = β × I<sub>B</sub>), where β (beta) is the DC current gain. The collector-emitter voltage (V<sub>CE</sub>) is greater than V<sub>CE(sat)</sub>. Small changes in I<sub>B</sub> (or V<sub>B</sub>) result in larger, proportional changes in I<sub>C</sub>.</li>
                <li><strong>Saturation Region:</strong> The transistor is 'fully ON'. V<sub>B</sub> and I<sub>B</sub> are high enough that I<sub>C</sub> reaches its maximum value, limited primarily by the supply voltage (VCC) and the collector resistor (RC), such that I<sub>C(sat)</sub> ≈ (VCC - V<sub>CE(sat)</sub>) / RC. Increasing V<sub>B</sub> or I<sub>B</sub> further has minimal effect on I<sub>C</sub>. V<sub>CE</sub> is at its minimum value, V<sub>CE(sat)</sub> (typically ≈ 0.2V). The transistor acts like a closed switch.</li>
            </ul>
        </div>
    </div>

    <script>
        // --- Transistor Circuit Constants ---
        const VCC = 5.0;       // Supply Voltage (Volts)
        const RB_val = 10000;  // Base Resistor (Ohms, 10kΩ)
        const RC_val = 1000;   // Collector Resistor (Ohms, 1kΩ)
        const BETA = 100;      // DC Current Gain (hFE)
        const VBE_ON = 0.7;    // Base-Emitter turn-on voltage (Volts)
        const VCE_SAT = 0.2;   // Collector-Emitter saturation voltage (Volts)

        // --- Calculated Circuit Properties ---
        const IC_SAT_AMPS = (VCC - VCE_SAT) / RC_val; // Saturation current in Amps
        const IC_SAT_mA = IC_SAT_AMPS * 1000; // Saturation current in mA

        // VB value at which saturation theoretically begins
        const IB_AT_SAT_EDGE = IC_SAT_AMPS / BETA;
        let VB_ACTIVE_REGION_END = (IB_AT_SAT_EDGE * RB_val) + VBE_ON;
        if (VB_ACTIVE_REGION_END < VBE_ON) VB_ACTIVE_REGION_END = VBE_ON;
        VB_ACTIVE_REGION_END = Math.min(VCC, VB_ACTIVE_REGION_END);

        // --- DOM Elements ---
        const vbSlider = document.getElementById('vbSlider');
        const vbValueDisplay = document.getElementById('vbValue');
        const icValueDisplay = document.getElementById('icValue');
        const regionValueDisplay = document.getElementById('regionValue');
        const canvas = document.getElementById('transistorPlot');
        const ctx = canvas.getContext('2d');

        // --- Display Fixed Parameters in UI ---
        document.getElementById('vccParam').textContent = VCC.toFixed(1);
        document.getElementById('rbParam').textContent = (RB_val / 1000).toFixed(0);
        document.getElementById('rcParam').textContent = (RC_val / 1000).toFixed(0);
        document.getElementById('betaParam').textContent = BETA;
        document.getElementById('vbeOnParam').textContent = VBE_ON.toFixed(1);
        document.getElementById('vceSatParam').textContent = VCE_SAT.toFixed(1);

        // --- Slider Configuration ---
        vbSlider.max = VCC.toFixed(2);

        // --- Plotting Variables ---
        let plotContainerWidth, plotEffectiveHeight;
        const plotPadding = { top: 40, right: 30, bottom: 50, left: 60 }; // Padding for labels etc.
        const opPointSize = 4; // Radius of the operating point circle

        // --- Core Transistor Logic ---
        function calculateOperatingPoint(vb_str) {
            const vb = parseFloat(vb_str);
            let ib_amps, ic_amps, vce_volts, region;

            if (vb < VBE_ON) { // Cutoff Region
                ib_amps = 0;
                ic_amps = 0;
                vce_volts = VCC; // Transistor is off, VCE approx VCC
                region = "Cutoff";
            } else { // Potentially Active or Saturation Region
                ib_amps = (vb - VBE_ON) / RB_val;
                if (ib_amps < 0) ib_amps = 0; // Should not happen if vb >= VBE_ON

                let ic_potential_active_amps = BETA * ib_amps;
                let vce_if_active = VCC - (ic_potential_active_amps * RC_val);

                if (vce_if_active < VCE_SAT) { // Saturation Region
                    ic_amps = IC_SAT_AMPS; 
                    vce_volts = VCE_SAT; // VCE is clamped at VCE_SAT
                    region = "Saturation";
                } else { // Active Region
                    ic_amps = ic_potential_active_amps;
                    vce_volts = vce_if_active;
                    region = "Active";
                }
            }
            ic_amps = Math.max(0, Math.min(ic_amps, IC_SAT_AMPS)); // Clamp IC just in case
            
            return {
                vb: vb,
                ic_mA: ic_amps * 1000,
                region: region
            };
        }
        
        // --- Canvas Drawing Functions ---
        function mapRange(value, inMin, inMax, outMin, outMax) {
            return (value - inMin) * (outMax - outMin) / (inMax - inMin) + outMin;
        }

        function drawPlot() {
            const dpr = window.devicePixelRatio || 1;
            const rect = canvas.getBoundingClientRect();
            
            canvas.width = rect.width * dpr;
            // Maintain a common aspect ratio like 16:9 or 2:1 for the plot content area
            // The canvas height will be rect.width * (desired_aspect_ratio_height / desired_aspect_ratio_width)
            // e.g. for 2:1 aspect ratio for the plot content: rect.width * 0.5
            // The actual canvas height will be more due to padding.
            // Let's set plot content height to be half of its width.
            plotContainerWidth = rect.width; // Actual width of canvas element on page
            plotEffectiveHeight = plotContainerWidth * 0.55; // Desired height for plot content
            canvas.height = plotEffectiveHeight * dpr;
            
            ctx.scale(dpr, dpr);

            ctx.clearRect(0, 0, plotContainerWidth, plotEffectiveHeight);
            ctx.fillStyle = "#ffffff";
            ctx.fillRect(0, 0, plotContainerWidth, plotEffectiveHeight);

            const plotAreaX_start = plotPadding.left;
            const plotAreaY_start = plotPadding.top;
            const plotAreaWidth = plotContainerWidth - plotPadding.left - plotPadding.right;
            const plotAreaHeight = plotEffectiveHeight - plotPadding.top - plotPadding.bottom;
            const plotAreaX_end = plotAreaX_start + plotAreaWidth;
            const plotAreaY_end = plotAreaY_start + plotAreaHeight;

            const vbMax = VCC;
            const icMaxPlot = IC_SAT_mA > 0 ? IC_SAT_mA * 1.1 : 1; // 10% margin or default if IC_SAT is 0

            const vbToPx = (vb_val) => mapRange(vb_val, 0, vbMax, plotAreaX_start, plotAreaX_end);
            const icToPx = (ic_val_mA) => mapRange(ic_val_mA, 0, icMaxPlot, plotAreaY_end, plotAreaY_start); // Y inverted

            // Draw Region Backgrounds & Labels
            ctx.font = "bold 11px Arial";
            const regionLabelY = plotAreaY_start - 15;

            const cutoffX_end_px = vbToPx(VBE_ON);
            ctx.fillStyle = "rgba(255, 152, 152, 0.2)"; // Light red
            ctx.fillRect(plotAreaX_start, plotAreaY_start, Math.max(0, cutoffX_end_px - plotAreaX_start), plotAreaHeight);
            ctx.fillStyle = "#c0392b"; // Darker red for text
            if (cutoffX_end_px - plotAreaX_start > 30) {
                 ctx.textAlign = "center";
                 ctx.fillText("Cutoff", plotAreaX_start + (cutoffX_end_px - plotAreaX_start) / 2, regionLabelY);
            }

            const activeX_start_px = cutoffX_end_px;
            const activeX_end_px = vbToPx(VB_ACTIVE_REGION_END);
            ctx.fillStyle = "rgba(152, 255, 152, 0.2)"; // Light green
            ctx.fillRect(activeX_start_px, plotAreaY_start, Math.max(0, activeX_end_px - activeX_start_px), plotAreaHeight);
            ctx.fillStyle = "#27ae60"; // Darker green for text
            if (activeX_end_px - activeX_start_px > 30) {
                ctx.textAlign = "center";
                ctx.fillText("Active", activeX_start_px + (activeX_end_px - activeX_start_px) / 2, regionLabelY);
            }
            
            const saturationX_start_px = activeX_end_px;
            ctx.fillStyle = "rgba(152, 152, 255, 0.2)"; // Light blue
            ctx.fillRect(saturationX_start_px, plotAreaY_start, Math.max(0, plotAreaX_end - saturationX_start_px), plotAreaHeight);
            ctx.fillStyle = "#2980b9"; // Darker blue for text
             if (plotAreaX_end - saturationX_start_px > 30) {
                ctx.textAlign = "center";
                ctx.fillText("Saturation", saturationX_start_px + (plotAreaX_end - saturationX_start_px) / 2, regionLabelY);
            }

            // Draw Axes
            ctx.strokeStyle = "#555"; // Dark grey for axes
            ctx.lineWidth = 1.5;
            ctx.beginPath(); // Y-axis
            ctx.moveTo(plotAreaX_start, plotAreaY_start - 5); ctx.lineTo(plotAreaX_start, plotAreaY_end + 5); ctx.stroke();
            ctx.beginPath(); // X-axis
            ctx.moveTo(plotAreaX_start - 5, plotAreaY_end); ctx.lineTo(plotAreaX_end + 5, plotAreaY_end); ctx.stroke();

            // Draw Ticks and Labels for Axes
            ctx.fillStyle = "#333";
            ctx.font = "10px Arial";
            
            const numVBTicks = 5; // X-axis ticks (VB)
            ctx.textAlign = "center";
            for (let i = 0; i <= numVBTicks; i++) {
                const vbTickVal = (vbMax / numVBTicks) * i;
                const x = vbToPx(vbTickVal);
                ctx.beginPath(); ctx.moveTo(x, plotAreaY_end); ctx.lineTo(x, plotAreaY_end + 5); ctx.stroke();
                ctx.fillText(vbTickVal.toFixed(1), x, plotAreaY_end + 18);
            }
            ctx.fillText("Base Voltage V_B (V)", plotAreaX_start + plotAreaWidth / 2, plotAreaY_end + 35);

            const numICTicks = Math.min(5, Math.floor(icMaxPlot)); // Y-axis ticks (IC)
            ctx.textAlign = "right";
            for (let i = 0; i <= numICTicks; i++) {
                const icTickVal = (icMaxPlot / numICTicks) * i;
                const y = icToPx(icTickVal);
                ctx.beginPath(); ctx.moveTo(plotAreaX_start, y); ctx.lineTo(plotAreaX_start - 5, y); ctx.stroke();
                ctx.fillText(icTickVal.toFixed(icMaxPlot < 2 ? 2:1), plotAreaX_start - 8, y + 4);
            }
            ctx.save();
            ctx.translate(plotPadding.left - 45, plotAreaY_start + plotAreaHeight / 2);
            ctx.rotate(-Math.PI / 2);
            ctx.textAlign = "center";
            ctx.fillText("Collector Current I_C (mA)", 0, 0);
            ctx.restore();

            // Draw Characteristic Curve (IC vs VB)
            ctx.strokeStyle = "#007bff"; // Blue for the curve
            ctx.lineWidth = 2.5;
            ctx.beginPath();
            const curvePoints = 200;
            for (let i = 0; i <= curvePoints; i++) {
                const vb_step = (VCC / curvePoints) * i;
                const op = calculateOperatingPoint(vb_step.toString());
                const x = vbToPx(op.vb);
                const y = icToPx(op.ic_mA);
                if (i === 0) ctx.moveTo(x, y); else ctx.lineTo(x, y);
            }
            ctx.stroke();

            // Draw current operating point
            const currentVB_val = parseFloat(vbSlider.value);
            const currentOpData = calculateOperatingPoint(currentVB_val.toString());
            const opX = vbToPx(currentOpData.vb);
            const opY = icToPx(currentOpData.ic_mA);

            ctx.fillStyle = "#dc3545"; // Red for operating point
            ctx.beginPath();
            ctx.arc(opX, opY, opPointSize, 0, 2 * Math.PI);
            ctx.fill();
            ctx.strokeStyle = "#771111"; // Darker red border
            ctx.lineWidth = 1;
            ctx.stroke();
        }

        // --- Update UI and Plot ---
        function updateValuesAndPlot() {
            const vb_val = parseFloat(vbSlider.value);
            const opData = calculateOperatingPoint(vb_val.toString());

            vbValueDisplay.textContent = vb_val.toFixed(2);
            icValueDisplay.textContent = opData.ic_mA.toFixed(2);
            regionValueDisplay.textContent = opData.region;
            
            drawPlot(); // Redraw the entire plot including the updated operating point
        }

        // --- Event Listeners ---
        vbSlider.addEventListener('input', updateValuesAndPlot);
        
        let resizeTimer;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => { // Debounce resize event
                drawPlot();
            }, 100); 
        });

        // --- Initial Setup ---
        updateValuesAndPlot(); // Set initial values and draw plot
    </script>
</body>
</html>
