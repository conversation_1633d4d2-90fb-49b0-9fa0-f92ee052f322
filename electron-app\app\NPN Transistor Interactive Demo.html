<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Transistor Basics</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f9;
            color: #333;
            line-height: 1.5;
        }
        .container {
            background-color: #ffffff;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 700px;
            box-sizing: border-box;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-top: 0;
            margin-bottom: 25px;
        }

        .schematic-container {
            width: 100%;
            margin-bottom: 25px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fdfdfd;
            overflow: hidden; /* Ensures SVG fits nicely */
        }
        #circuit-diagram {
            width: 100%;
            height: auto;
            display: block;
        }
        #circuit-diagram text {
            font-family: 'Consolas', 'Courier New', Courier, monospace;
            font-size: 12px;
            fill: #333;
            pointer-events: none; /* Text should not interfere with mouse events */
        }
        #circuit-diagram .label-bold {
            font-weight: bold;
            font-size: 16px;
            fill: #0056b3; 
        }

        .controls {
            margin-bottom: 25px;
            padding: 20px;
            background-color: #e9ecef;
            border-radius: 8px;
        }
        .controls label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
            color: #495057;
        }
        .controls input[type="range"] {
            width: 100%;
            cursor: pointer;
            margin-top: 5px;
        }
        #rb-pot-value {
            font-weight: bold;
            color: #007bff;
        }

        .info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding: 15px 20px;
            background-color: #e9ecef;
            border-radius: 8px;
            flex-wrap: wrap;
        }
        .info p {
            margin: 8px 0;
            color: #495057;
            flex-basis: calc(50% - 20px); /* Two items per row on larger screens */
        }
        .info strong {
            color: #007bff;
        }
        .led-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 8px 0;
            flex-basis: 100%; /* Full width for LED container initially */
        }
        .led-container span {
            font-size: 0.95em;
            margin-bottom: 8px;
            color: #495057;
            font-weight: bold;
        }
        #led {
            width: 45px;
            height: 45px;
            background-color: #555;
            border-radius: 50%;
            transition: background-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            border: 3px solid #ced4da;
        }

        .explanation {
            padding: 20px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            line-height: 1.7;
            color: #495057;
        }
        .explanation strong {
            color: #0056b3;
        }
        .explanation p:last-child {
            margin-bottom: 0;
        }

        /* Responsive adjustments */
        @media (min-width: 500px) { /* Adjust flex-basis for info items */
            .info p {
                 flex-basis: auto; /* Allow natural sizing */
            }
             .led-container {
                flex-basis: auto; /* Allow natural sizing */
            }
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px;
            }
            h1 {
                font-size: 1.6em;
            }
            .info {
                flex-direction: column;
                align-items: flex-start;
            }
            .info p, .led-container {
                width: 100%;
                margin-bottom: 12px;
            }
            #led {
                width: 40px;
                height: 40px;
            }
            #circuit-diagram text {
                font-size: 11px;
            }
            #circuit-diagram .label-bold {
                font-size: 14px;
            }
        }
         @media (max-width: 400px) {
            #circuit-diagram text {
                font-size: 10px;
            }
            #circuit-diagram .label-bold {
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>NPN Transistor Interactive Demo</h1>

        <div class="schematic-container">
            <svg id="circuit-diagram" viewBox="0 0 420 300" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="black"/>
                    </marker>
                </defs>

                <!-- VCC Source (+9V) -->
                <line x1="50" y1="30" x2="50" y2="70" stroke="black" stroke-width="2"/>
                <line x1="38" y1="40" x2="62" y2="40" stroke="black" stroke-width="2.5"/>
                <line x1="43" y1="60" x2="57" y2="60" stroke="black" stroke-width="2.5"/>
                <text x="68" y="55">VCC (+9V)</text>

                <!-- Collector Resistor (Rc) -->
                <rect x="130" y="45" width="50" height="20" stroke="black" stroke-width="1.5" fill="#fff" rx="2" ry="2"/>
                <text x="140" y="38">R<sub>C</sub></text>

                <!-- Schematic LED -->
                <circle cx="230" cy="55" r="10" stroke="black" stroke-width="1.5" fill="lightgray" id="schematic-led-visual"/>
                <line x1="225" y1="40" x2="220" y2="35" stroke="black" stroke-width="1"/>
                <line x1="235" y1="40" x2="240" y2="35" stroke="black" stroke-width="1"/>
                <text x="245" y="60" font-size="10">LED</text>

                <!-- Transistor NPN -->
                <circle cx="230" cy="130" r="25" stroke="black" stroke-width="2" fill="white"/>
                <line x1="230" y1="105" x2="230" y2="80" stroke="black" stroke-width="2"/> 
                <text x="240" y="95" class="label-bold">C</text>

                <line x1="180" y1="130" x2="205" y2="130" stroke="black" stroke-width="2"/> 
                <text x="165" y="135" class="label-bold">B</text>

                <line x1="230" y1="155" x2="230" y2="185" stroke="black" stroke-width="2" marker-end="url(#arrowhead)"/> 
                <text x="240" y="175" class="label-bold">E</text>

                <!-- Wires - Collector Path -->
                <line x1="50" y1="55" x2="130" y2="55" stroke="black" stroke-width="1.5"/> 
                <line x1="180" y1="55" x2="220" y2="55" stroke="black" stroke-width="1.5"/> 
                <line x1="230" y1="65" x2="230" y2="80" stroke="black" stroke-width="1.5"/> 


                <!-- Ground -->
                <line x1="230" y1="185" x2="230" y2="205" stroke="black" stroke-width="1.5"/>
                <line x1="210" y1="205" x2="250" y2="205" stroke="black" stroke-width="2"/>
                <line x1="215" y1="210" x2="245" y2="210" stroke="black" stroke-width="2"/>
                <line x1="220" y1="215" x2="240" y2="215" stroke="black" stroke-width="2"/>

                <!-- Base Circuit -->
                <line x1="50" y1="110" x2="50" y2="150" stroke="black" stroke-width="2"/>
                <line x1="38" y1="120" x2="62" y2="120" stroke="black" stroke-width="2.5"/>
                <line x1="43" y1="140" x2="57" y2="140" stroke="black" stroke-width="2.5"/>
                <text x="0" y="135">V<sub>B_src</sub> (+5V)</text>


                <!-- Potentiometer (Rb_pot) -->
                <rect x="90" y="125" width="60" height="20" stroke="black" stroke-width="1.5" fill="#fff" rx="2" ry="2"/>
                <text x="95" y="120">R<sub>B_pot</sub></text>
                <line x1="100" y1="142" x2="140" y2="118" stroke="black" stroke-width="1.5" marker-end="url(#arrowhead)"/>

                <!-- Wires - Base Path -->
                <line x1="50" y1="130" x2="90" y2="130" stroke="black" stroke-width="1.5"/>
                <line x1="150" y1="130" x2="180" y2="130" stroke="black" stroke-width="1.5"/>
            </svg>
        </div>

        <div class="controls">
            <label for="base-pot-slider">Base Potentiometer (R<sub>B_pot</sub>): <span id="rb-pot-value">50.0</span> kΩ</label>
            <input type="range" id="base-pot-slider" min="1" max="200" value="50" step="0.1">
        </div>

        <div class="info">
            <p>Base Current (I<sub>B</sub>): <strong id="ib-value">0.00</strong> µA</p>
            <p>Collector Current (I<sub>C</sub>): <strong id="ic-value">0.00</strong> mA</p>
            <div class="led-container">
                <span>Visual LED (Load)</span>
                <div id="led"></div>
            </div>
        </div>

        <div class="explanation">
            <p>The NPN transistor acts somewhat like a current-controlled switch or variable resistor. A small current (I<sub>B</sub>) flowing into the <strong>Base (B)</strong> terminal allows a much larger current (I<sub>C</sub>) to flow from the <strong>Collector (C)</strong> to the <strong>Emitter (E)</strong>.</p>
            <p>By adjusting the Base Potentiometer (R<sub>B_pot</sub>), you change the resistance in the base circuit. Decreasing this resistance increases I<sub>B</sub>. As I<sub>B</sub> increases, I<sub>C</sub> also increases, causing the LED (representing a load in the collector circuit) to get brighter. If I<sub>B</sub> becomes high enough, the transistor enters 'saturation.' In saturation, I<sub>C</sub> reaches its maximum possible value, limited mainly by the VCC voltage and the Collector Resistor (R<sub>C</sub>). Further increases in I<sub>B</sub> will then have little to no effect on I<sub>C</sub>.</p>
        </div>
    </div>

    <script>
        const VCC = 9; // Volts
        const RC = 470; // Ohms - Collector Resistor
        const BETA = 100; // Transistor current gain (hFE)
        const V_BE_ON = 0.7; // Volts - Base-Emitter forward voltage
        const V_BASE_SUPPLY = 5; // Volts - Voltage source for the base circuit
        const V_CE_SAT = 0.2; // Volts - Collector-Emitter saturation voltage

        const potSlider = document.getElementById('base-pot-slider');
        const rbPotValueDisplay = document.getElementById('rb-pot-value');
        const ibValueDisplay = document.getElementById('ib-value');
        const icValueDisplay = document.getElementById('ic-value');
        const ledElement = document.getElementById('led');
        const schematicLedVisual = document.getElementById('schematic-led-visual');

        function updateCircuit() {
            const rPotBase_kOhms = parseFloat(potSlider.value);
            rbPotValueDisplay.textContent = rPotBase_kOhms.toFixed(1);
            const rPotBase_Ohms = rPotBase_kOhms * 1000;

            let ib = 0;
            if (V_BASE_SUPPLY > V_BE_ON && rPotBase_Ohms > 0) {
                ib = (V_BASE_SUPPLY - V_BE_ON) / rPotBase_Ohms; 
            }
            ib = Math.max(0, ib); 

            let ic_ideal = BETA * ib; 

            let ic_max_saturation = 0;
            if (VCC > V_CE_SAT && RC > 0) {
                 ic_max_saturation = (VCC - V_CE_SAT) / RC;
            }
            ic_max_saturation = Math.max(0, ic_max_saturation);

            let ic = Math.min(ic_ideal, ic_max_saturation); 
            ic = Math.max(0, ic); 

            ibValueDisplay.textContent = (ib * 1_000_000).toFixed(2); 
            icValueDisplay.textContent = (ic * 1_000).toFixed(2); 

            let normalizedIc = 0;
            if (ic_max_saturation > 1e-9) { // Avoid division by zero if ic_max_saturation is effectively zero
                normalizedIc = Math.min(1, Math.max(0, ic / ic_max_saturation));
            } else if (ic > 1e-9) { 
                normalizedIc = 1; // If max is zero but current flows, show as full (edge case)
            }
            
            const isEffectivelyOff = ic < 0.000001; // Threshold for "off" state

            // Main LED div styling
            const mainLedLightness = isEffectivelyOff ? 33 : Math.floor(20 + normalizedIc * 30); // 20% to 50% for red, 33% for grey
            const mainLedColor = isEffectivelyOff ? '#555555' : `hsl(0, 100%, ${mainLedLightness}%)`;
            ledElement.style.backgroundColor = mainLedColor;
            if (!isEffectivelyOff) {
                 ledElement.style.boxShadow = `0 0 ${Math.floor(normalizedIc * 12 + 3)}px hsl(0, 100%, ${Math.min(100, mainLedLightness + 10)}%)`;
            } else {
                 ledElement.style.boxShadow = 'none';
            }

            // Schematic LED styling
            const schematicLedLightness = isEffectivelyOff ? 80 : Math.floor(30 + normalizedIc * 40); // 30% to 70% for red, 80% for lightgray
            const schematicLedColor = isEffectivelyOff ? 'lightgray' : `hsl(0, 100%, ${schematicLedLightness}%)`;
            schematicLedVisual.setAttribute('fill', schematicLedColor);
        }

        potSlider.addEventListener('input', updateCircuit);
        document.addEventListener('DOMContentLoaded', updateCircuit);
    </script>
</body>
</html>
