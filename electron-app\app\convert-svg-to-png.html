<!DOCTYPE html>
<html>
<head>
    <title>SVG to PNG Converter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }
        #status {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>SVG to PNG Converter</h1>
    <p>This tool converts SVG files in the images/lab-notes folder to PNG format.</p>
    
    <button id="convertButton">Convert SVG to PNG</button>
    <div id="status">Status: Ready</div>
    
    <script>
        const svgFiles = [
            'common-emitter',
            'common-collector',
            'common-base',
            'differential-amplifier',
            'darlington-pair',
            'push-pull',
            'astable-multivibrator',
            'bistable-multivibrator'
        ];
        
        function convertSvgToPng(svgFileName) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                const svgUrl = `images/lab-notes/${svgFileName}.svg`;
                
                img.onload = function() {
                    const canvas = document.createElement('canvas');
                    canvas.width = img.width;
                    canvas.height = img.height;
                    
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(img, 0, 0);
                    
                    try {
                        const pngUrl = canvas.toDataURL('image/png');
                        
                        // Create download link
                        const a = document.createElement('a');
                        a.href = pngUrl;
                        a.download = `${svgFileName}.png`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        
                        resolve(svgFileName);
                    } catch (error) {
                        reject(error);
                    }
                };
                
                img.onerror = function() {
                    reject(new Error(`Failed to load SVG: ${svgUrl}`));
                };
                
                img.src = svgUrl;
            });
        }
        
        document.getElementById('convertButton').addEventListener('click', async function() {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = 'Status: Starting conversion...';
            
            for (const svgFile of svgFiles) {
                try {
                    statusDiv.innerHTML = `Status: Converting ${svgFile}.svg to PNG...`;
                    await convertSvgToPng(svgFile);
                    statusDiv.innerHTML = `Status: Successfully converted ${svgFile}.svg to PNG.`;
                } catch (error) {
                    statusDiv.innerHTML = `Status: Error converting ${svgFile}.svg: ${error.message}`;
                    console.error(error);
                    break;
                }
            }
            
            statusDiv.innerHTML = 'Status: All conversions completed.';
        });
    </script>
</body>
</html>
