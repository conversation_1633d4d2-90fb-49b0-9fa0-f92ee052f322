<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Transistor Simulator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 700px;
        }

        h1 {
            text-align: center;
            color: #333;
        }

        .description p {
            margin-bottom: 15px;
        }

        .app-section {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }

        .circuit-diagram-container {
            flex: 1 1 300px; /* Flex grow, shrink, basis */
            border: 1px solid #ccc;
            padding: 10px;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 250px; /* Ensure space for diagram */
        }

        #circuitDiagram {
            width: 100%;
            max-width: 350px;
            height: auto;
        }

        .controls-meters {
            flex: 1 1 300px; /* Flex grow, shrink, basis */
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .control-group, .meter-group {
            background-color: #e9e9e9;
            padding: 10px;
            border-radius: 4px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input[type="range"] {
            width: 100%;
            cursor: pointer;
        }

        .value-display {
            font-weight: bold;
            color: #007bff;
        }
        
        .param-display span {
            font-weight: normal;
            color: #333;
        }

        #led {
            transition: opacity 0.2s ease-in-out;
        }
        
        /* Responsive adjustments */
        @media (max-width: 600px) {
            .app-section {
                flex-direction: column;
            }
            .circuit-diagram-container, .controls-meters {
                flex-basis: auto; /* Reset basis for column layout */
            }
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>NPN Transistor Behavior Simulator</h1>

        <div class="description">
            <p>This interactive app helps you understand how an NPN bipolar junction transistor (BJT) works as a voltage-controlled switch. A small current applied to the 'base' (Ib) controls a much larger current flowing between the 'collector' (Ic) and 'emitter'.</p>
            <p>The transistor has three main regions of operation:</p>
            <ul>
                <li><strong>Cut-off:</strong> No significant current flows from collector to emitter (Ic ≈ 0). The transistor acts like an open switch.</li>
                <li><strong>Active:</strong> The collector current (Ic) is proportional to the base current (Ic = β * Ib). The transistor acts as an amplifier.</li>
                <li><strong>Saturation:</strong> The transistor acts like a closed switch. Collector current (Ic) is at its maximum possible value, limited by the supply voltage (VCC) and collector resistance (Rc). Further increases in base current do not increase collector current.</li>
            </ul>
            <p><strong>Key principles for this NPN transistor circuit:</strong></p>
            <ol>
                <li>For the NPN transistor to conduct current from collector to emitter, the base terminal must be at a higher voltage (approximately 0.6-0.7V higher) than the emitter terminal. This forward-biases the base-emitter junction, allowing base current (Ib) to flow.</li>
                <li>To switch the LED in this example circuit on or off, or to control its brightness, the base current (Ib) must be precisely controlled. A small change in Ib can cause a large change in collector current (Ic), thereby changing the LED's state.</li>
            </ol>
        </div>

        <div class="app-section">
            <div class="circuit-diagram-container">
                <svg id="circuitDiagram" viewBox="0 0 200 150" preserveAspectRatio="xMidYMid meet">
                    <!-- VCC Source -->
                    <circle cx="30" cy="20" r="10" stroke="black" stroke-width="1" fill="none"/>
                    <text x="30" y="23" text-anchor="middle" font-size="8px">+</text>
                    <line x1="30" y1="10" x2="30" y2="0" stroke="black" stroke-width="1"/>
                    <text x="35" y="10" font-size="7px">VCC</text>
                    
                    <!-- Rc (Collector Resistor) -->
                    <line x1="30" y1="30" x2="30" y2="40" stroke="black" stroke-width="1"/>
                    <rect x="25" y="40" width="10" height="20" stroke="black" stroke-width="1" fill="none"/>
                    <text x="40" y="50" font-size="7px">Rc</text>
                    <line x1="30" y1="60" x2="30" y2="70" stroke="black" stroke-width="1"/>

                    <!-- LED -->
                    <line x1="30" y1="70" x2="50" y2="70" stroke="black" stroke-width="1"/>
                    <polygon id="led" points="50,65 70,70 50,75" stroke="black" stroke-width="1" fill="red" opacity="0"/>
                    <line x1="50" y1="70" x2="70" y2="70" stroke="black" stroke-width="1"/> <!-- Diode line part -->
                    <line x1="70" y1="65" x2="70" y2="75" stroke="black" stroke-width="1"/> <!-- Diode bar part -->
                    <!-- Light rays for LED -->
                    <line x1="60" y1="60" x2="65" y2="55" stroke="black" stroke-width="0.5" class="led-ray" opacity="0"/>
                    <line x1="65" y1="60" x2="70" y2="58" stroke="black" stroke-width="0.5" class="led-ray" opacity="0"/>
                     <text x="75" y="73" font-size="7px">LED</text>
                    <line x1="70" y1="70" x2="90" y2="70" stroke="black" stroke-width="1"/> <!-- Wire to collector -->


                    <!-- NPN Transistor -->
                    <circle cx="100" cy="90" r="15" stroke="black" stroke-width="1" fill="none"/>
                    <line x1="85" y1="90" x2="95" y2="90" stroke="black" stroke-width="1"/> <!-- Base line inside circle -->
                    <line x1="70" y1="90" x2="85" y2="90" stroke="black" stroke-width="1"/> <!-- Base wire -->
                    <text x="65" y="88" font-size="7px">B</text>
                    <line x1="95" y1="90" x2="100" y2="78" stroke="black" stroke-width="1"/> <!-- Collector arm -->
                    <line x1="100" y1="78" x2="100" y2="70" stroke="black" stroke-width="1"/> <!-- Collector wire -->
                    <line x1="90" y1="70" x2="100" y2="70" stroke="black" stroke-width="1"/> <!-- Connect LED path to Collector -->
                    <text x="105" y="68" font-size="7px">C</text>
                    <line x1="95" y1="90" x2="100" y2="102" stroke="black" stroke-width="1"/> <!-- Emitter arm -->
                    <polygon points="100,102 104,98 100,100" fill="black" stroke="black" stroke-width="1"/> <!-- Emitter arrow -->
                    <line x1="100" y1="102" x2="100" y2="110" stroke="black" stroke-width="1"/> <!-- Emitter wire -->
                    <text x="105" y="112" font-size="7px">E</text>

                    <!-- Ground -->
                    <line x1="100" y1="110" x2="100" y2="120" stroke="black" stroke-width="1"/>
                    <line x1="90" y1="120" x2="110" y2="120" stroke="black" stroke-width="1"/>
                    <line x1="93" y1="125" x2="107" y2="125" stroke="black" stroke-width="1"/>
                    <line x1="96" y1="130" x2="104" y2="130" stroke="black" stroke-width="1"/>
                    <text x="100" y="140" text-anchor="middle" font-size="7px">GND</text>

                    <!-- Base current source (conceptual) -->
                    <text x="40" y="93" font-size="7px">Ib source</text>
                    <line x1="55" y1="90" x2="70" y2="90" stroke-dasharray="2,2" stroke="black" stroke-width="1"/>
                </svg>
            </div>

            <div class="controls-meters">
                <div class="control-group param-display">
                    <p><strong>Fixed Parameters:</strong></p>
                    <p>Supply Voltage (VCC): <span id="vccDisplay"></span></p>
                    <p>Collector Resistor (Rc): <span id="rcDisplay"></span></p>
                    <p>Transistor Gain (β): <span id="betaDisplay"></span></p>
                </div>

                <div class="control-group">
                    <label for="ibSlider">Base Current (Ib): <span id="ibValue" class="value-display">0 µA</span></label>
                    <input type="range" id="ibSlider" min="0" max="400" value="0" step="1">
                </div>

                <div class="meter-group">
                    <p><strong>Calculated Values:</strong></p>
                    <p>Base Current (Ib): <span id="ibMeter" class="value-display">0.00 µA</span></p>
                    <p>Collector Current (Ic): <span id="icMeter" class="value-display">0.00 mA</span></p>
                    <p>Collector-Emitter Voltage (VCE): <span id="vceMeter" class="value-display">0.00 V</span></p>
                    <p>Operating Region: <span id="regionMeter" class="value-display">Cut-off</span></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Fixed parameters
        const VCC = 5;      // Volts
        const RC = 220;     // Ohms
        const BETA = 100;
        const VCE_SAT = 0.2; // Volts, typical saturation voltage for VCE

        // LED turn-on threshold condition related value (0.7V from spec 4.c)
        // Interpreted as: LED starts to light up IF Ic*Rc > 0.7V
        const LED_VOLTAGE_THRESHOLD_RC = 0.7; // Volts
        const IC_LED_ON_THRESHOLD = LED_VOLTAGE_THRESHOLD_RC / RC; // Amps

        // DOM Elements
        const ibSlider = document.getElementById('ibSlider');
        const ibValueDisplay = document.getElementById('ibValue');
        
        const vccDisplay = document.getElementById('vccDisplay');
        const rcDisplay = document.getElementById('rcDisplay');
        const betaDisplay = document.getElementById('betaDisplay');

        const ibMeterDisplay = document.getElementById('ibMeter');
        const icMeterDisplay = document.getElementById('icMeter');
        const vceMeterDisplay = document.getElementById('vceMeter');
        const regionMeterDisplay = document.getElementById('regionMeter');
        
        const ledElement = document.getElementById('led');
        const ledRayElements = document.querySelectorAll('.led-ray');

        function updateCircuit() {
            // 1. Get Base Current (Ib) from slider (convert µA to A)
            const ib_uA = parseFloat(ibSlider.value);
            const ib = ib_uA / 1e6; // Convert µA to A

            // Display Ib input value
            ibValueDisplay.textContent = `${ib_uA.toFixed(0)} µA`;
            ibMeterDisplay.textContent = `${ib_uA.toFixed(2)} µA`;

            // 2. Calculate Collector Current (Ic)
            let ic = BETA * ib;
            const ic_sat = VCC / RC;

            if (ic >= ic_sat) {
                ic = ic_sat; // Cap at saturation current
            }
            if (ic < 0) ic = 0; // Ensure non-negative

            // 3. Calculate Collector-Emitter Voltage (VCE)
            let vce = VCC - (ic * RC);
            
            if (vce < VCE_SAT) { // If calculated VCE is less than VCE_sat (deep saturation)
                vce = VCE_SAT;
                // Recalculate Ic if VCE is clamped to VCE_SAT, only if not already at ic_sat.
                // This happens if BETA*Ib is very high, pushing it deep into saturation.
                // The primary limit is ic_sat. If ic = ic_sat, then VCE = VCC - ic_sat * RC = 0 (ideal).
                // So, if ic = ic_sat, we set VCE = VCE_SAT.
                if (ic < ic_sat) { // This case is less common with simple model
                     // ic = (VCC - VCE_SAT) / RC; // This would be a more complex model adjustment
                }
            }
             if (ic === 0) { // Cut-off
                vce = VCC;
            }


            // Display Ic and VCE
            icMeterDisplay.textContent = `${(ic * 1000).toFixed(2)} mA`; // Display in mA
            vceMeterDisplay.textContent = `${vce.toFixed(2)} V`;

            // 4. Determine Operating Region
            let region = "Active";
            if (ic <= 1e-7 || ib_uA < 0.1) { // Consider cut-off if Ic is negligible (e.g. < 0.1 µA for Ib)
                region = "Cut-off";
                vce = VCC; // Ensure VCE is VCC in cut-off
                ic = 0; // Ensure Ic is 0 in cut-off
            } else if (vce <= VCE_SAT + 0.05) { // Add small tolerance for float comparison
                region = "Saturation";
                ic = ic_sat; // Ensure Ic is exactly Ic_sat in saturation
                vce = VCE_SAT; // Ensure VCE is VCE_SAT in saturation
            }
            regionMeterDisplay.textContent = region;
            
            // Update actual displayed values again after region-specific adjustments
            icMeterDisplay.textContent = `${(ic * 1000).toFixed(2)} mA`;
            vceMeterDisplay.textContent = `${vce.toFixed(2)} V`;


            // 5. Update LED Brightness
            // "fully lit at saturation current level"
            // "LED will only start to light up once the Ic reaches a value that results in a VCE greater than that (0.7V)"
            // This was interpreted as: LED lights up if Ic*Rc > 0.7V (i.e., Ic > IC_LED_ON_THRESHOLD)
            // And brightness scales with Ic, max at Ic_sat.
            
            let ledOpacity = 0;
            if (ic > IC_LED_ON_THRESHOLD && ic_sat > IC_LED_ON_THRESHOLD) {
                 // Scale brightness from the point it turns on, up to saturation
                let effectiveIcForLed = ic - IC_LED_ON_THRESHOLD;
                let rangeForLed = ic_sat - IC_LED_ON_THRESHOLD;
                if (rangeForLed <= 0) rangeForLed = ic_sat; // Avoid division by zero if threshold is near/at saturation

                ledOpacity = effectiveIcForLed / rangeForLed;
                ledOpacity = Math.max(0, Math.min(1, ledOpacity)); // Clamp between 0 and 1
            } else if (ic > 0 && ic <= IC_LED_ON_THRESHOLD) { // Very dim or just starting if below threshold but not zero
                 ledOpacity = (ic / IC_LED_ON_THRESHOLD) * 0.1; // Arbitrary small opacity
            }


            if (region === "Cut-off" || ic === 0) {
                ledOpacity = 0;
            }
            if (region === "Saturation") {
                ledOpacity = 1; // Fully lit at saturation
            }
            
            ledElement.style.opacity = ledOpacity;
            ledRayElements.forEach(ray => ray.style.opacity = ledOpacity > 0.5 ? ledOpacity : 0); // Show rays when bright
        }

        // Initial setup
        function init() {
            vccDisplay.textContent = `${VCC.toFixed(1)} V`;
            rcDisplay.textContent = `${RC.toFixed(0)} Ω`;
            betaDisplay.textContent = `${BETA}`;

            ibSlider.addEventListener('input', updateCircuit);
            updateCircuit(); // Initial call to set values based on slider's default
        }

        // Run init when DOM is ready
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
