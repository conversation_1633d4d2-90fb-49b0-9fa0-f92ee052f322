@echo off
echo ===================================================
echo Electronic Circuits Lab - Launcher
echo ===================================================
echo.

:: Check if the executable exists
if exist "electron-app\dist\Electronic Circuits Lab Setup *.exe" (
    echo Executable found. Launching the installer...
    for /f "delims=" %%i in ('dir /b "electron-app\dist\Electronic Circuits Lab Setup *.exe"') do (
        start "" "electron-app\dist\%%i"
    )
) else (
    echo Executable not found.
    echo Please run build-exe.bat first to build the application.
    pause
    exit /b 1
)

echo.
echo If the installer doesn't start automatically, please navigate to:
echo electron-app\dist
echo and run the "Electronic Circuits Lab Setup" executable manually.
echo.
pause
