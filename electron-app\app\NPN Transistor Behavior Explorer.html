<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مستكشف سلوك الترانزستور (NPN)</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <header>
        <h1>مستكشف سلوك الترانزستور (NPN)</h1>
    </header>

    <nav>
        <ul>
            <li><a href="index.html">الرئيسية</a></li>
            <li><a href="simulation.html">بدء المحاكاة</a></li>
            <!-- يمكنك إضافة روابط لتجارب أخرى هنا -->
        </ul>
    </nav>

    <main>
        <section id="npn-explorer-intro">
            <h2>مقدمة عن مستكشف سلوك الترانزستور NPN</h2>
            <p>هذه التجربة تسمح لك باستكشاف كيف يتغير سلوك الترانزستور من نوع NPN بتغيير الجهود والتيارات المختلفة. ستركز هذه التجربة على فهم مناطق عمل الترانزستور (القطع، التشبع، والفعالة) بشكل عملي.</p>
        </section>

        <section id="npn-simulation-area">
            <h2>منطقة المحاكاة التفاعلية (NPN)</h2>
            <p>هنا سيتم عرض واجهة تفاعلية يمكنك من خلالها التحكم في قيم مثل جهد القاعدة (Vb) وجهد المجمع (Vc) وملاحظة تأثير ذلك على تيار المجمع (Ic) وتيار القاعدة (Ib).</p>
            <!-- عناصر التحكم والرسوم البيانية ستضاف هنا -->
            <div class="simulation-area">
                <p>مساحة محاكاة سلوك الترانزستور NPN (قيد الإنشاء)</p>
                <!-- مثال لعناصر التحكم -->
                <div>
                    <label for="vb-input">جهد القاعدة (Vb):</label>
                    <input type="range" id="vb-input" name="vb-input" min="0" max="5" step="0.1" value="0.7">
                    <span id="vb-value">0.7 V</span>
                </div>
                <div>
                    <label for="vc-input">جهد المجمع (Vc):</label>
                    <input type="range" id="vc-input" name="vc-input" min="0" max="12" step="0.5" value="5">
                    <span id="vc-value">5 V</span>
                </div>
                <div id="npn-results">
                    <p>تيار القاعدة (Ib): <span id="ib-output">--</span> mA</p>
                    <p>تيار المجمع (Ic): <span id="ic-output">--</span> mA</p>
                    <p>منطقة العمل: <span id="operation-region">--</span></p>
                </div>
            </div>
        </section>

        <section id="npn-concepts">
            <h2>مفاهيم أساسية لسلوك NPN</h2>
            <ul>
                <li><strong>جهد الانحياز الأمامي (Forward Bias):</strong> يجب أن يكون جهد القاعدة-الباعث (Vbe) حوالي 0.7 فولت للسيليكون حتى يبدأ الترانزستور في التوصيل.</li>
                <li><strong>كسب التيار (Beta / hFE):</strong> النسبة بين تيار المجمع وتيار القاعدة (Ic / Ib) في المنطقة الفعالة.</li>
                <li><strong>منطقة القطع (Cutoff):</strong> عندما يكون الترانزستور "مطفأ" ولا يمر تيار يذكر عبر المجمع.</li>
                <li><strong>منطقة التشبع (Saturation):</strong> عندما يكون الترانزستور "مفتوح بالكامل" ويمر أقصى تيار ممكن عبر المجمع (محدود بمقاومة الدائرة).</li>
                <li><strong>المنطقة الفعالة (Active Region):</strong> المنطقة التي يعمل فيها الترانزستور كمضخم، حيث يتناسب تيار المجمع طردياً مع تيار القاعدة.</li>
            </ul>
        </section>
    </main>

    <footer>
        <p>&copy; 2024 معمل الترانزستور الافتراضي. جميع الحقوق محفوظة.</p>
    </footer>

    <script src="js/main.js"></script>
    <script>
        // JavaScript خاص بهذه الصفحة لاستكشاف سلوك NPN
        document.addEventListener('DOMContentLoaded', function() {
            const vbInput = document.getElementById('vb-input');
            const vcInput = document.getElementById('vc-input');
            const vbValueSpan = document.getElementById('vb-value');
            const vcValueSpan = document.getElementById('vc-value');
            const ibOutputSpan = document.getElementById('ib-output');
            const icOutputSpan = document.getElementById('ic-output');
            const operationRegionSpan = document.getElementById('operation-region');

            function updateNpnSimulation() {
                const vb = parseFloat(vbInput.value);
                const vc = parseFloat(vcInput.value);
                vbValueSpan.textContent = vb.toFixed(1) + ' V';
                vcValueSpan.textContent = vc.toFixed(1) + ' V';

                // منطق محاكاة مبسط (سيتم تطويره بشكل أكثر دقة)
                let ib = 0;
                let ic = 0;
                let region = 'القطع';

                const vbeThreshold = 0.7; // جهد بداية التوصيل للقاعدة-باعث
                const beta = 100; // مثال لقيمة كسب التيار

                if (vb >= vbeThreshold) {
                    // افتراض مقاومة قاعدة بسيطة لحساب تيار القاعدة
                    // هذا تبسيط كبير، في الواقع يعتمد على دائرة القاعدة
                    ib = (vb - vbeThreshold) / 10; // مثال: مقاومة قاعدة 10 كيلو أوم
                    ib = Math.max(0, ib); // لا يمكن أن يكون التيار سالبًا

                    ic_active = beta * ib;

                    // تحديد منطقة التشبع بشكل مبسط
                    // Vce_sat هو جهد التشبع بين المجمع والباعث (مثلاً 0.2 فولت)
                    const vce_sat = 0.2;
                    if (vc <= vbeThreshold + vce_sat) { // تقدير مبسط لمنطقة التشبع
                        region = 'التشبع';
                        // في التشبع، Ic لا يتبع beta*Ib بالضرورة، بل يحدده جهد المجمع ودائرة الحمل
                        // هذا تبسيط، يجب حساب Ic بناءً على مقاومة الحمل وجهد المصدر للمجمع
                        ic = (vc - vce_sat) / 1; // مثال: مقاومة مجمع 1 كيلو أوم
                        ic = Math.max(0, ic);
                    } else {
                        region = 'الفعالة';
                        ic = ic_active;
                    }
                } else {
                    region = 'القطع';
                    ib = 0;
                    ic = 0;
                }
                
                // تحديد أقصى تيار للمجمع بناءً على جهد المجمع ومقاومة الحمل (افتراضية هنا)
                // const R_collector_load = 1; // 1kOhm
                // const max_ic_possible = vc / R_collector_load;
                // ic = Math.min(ic, max_ic_possible);


                ibOutputSpan.textContent = ib.toFixed(2) + ' mA';
                icOutputSpan.textContent = ic.toFixed(2) + ' mA';
                operationRegionSpan.textContent = region;
            }

            vbInput.addEventListener('input', updateNpnSimulation);
            vcInput.addEventListener('input', updateNpnSimulation);

            // Initial call to display values
            updateNpnSimulation();
        });
    </script>
</body>
</html>