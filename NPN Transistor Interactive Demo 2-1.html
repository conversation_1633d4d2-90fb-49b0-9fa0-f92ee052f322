<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Transistor Circuit Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: auto;
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        h1, h2 {
            color: #333;
            text-align: center;
        }

        .main-layout {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .circuit-section {
            flex: 1 1 500px; /* Grow, shrink, basis */
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .controls-section {
            flex: 1 1 300px;
            padding: 15px;
            background-color: #e9e9e9;
            border-radius: 5px;
        }
        
        #circuitDiagram {
            width: 100%;
            max-width: 500px;
            height: auto;
            border: 1px solid #ccc;
            background-color: #fdfdfd;
        }

        .control-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input[type="range"], select {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border-radius: 4px;
            border: 1px solid #ccc;
        }
        
        input[type="range"] {
            padding: 0; /* Override default padding for range sliders */
        }

        .readouts {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }

        .readouts p {
            margin: 5px 0;
            font-size: 0.9em;
        }
        .readouts p strong {
            display: inline-block;
            width: 50px; /* Align values */
        }

        #saturationIndicator {
            margin-top: 10px;
            padding: 8px;
            text-align: center;
            border-radius: 4px;
            font-size: 1.1em;
        }

        .explanation-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .explanation-section h3 {
            margin-top: 0;
        }
        .explanation-section ul {
            list-style-type: disc;
            padding-left: 20px;
        }

        /* SVG specific styles */
        .wire { stroke: #333; stroke-width: 2; }
        .component-outline { stroke: black; stroke-width: 1; fill: none; }
        .resistor-body { fill: #fff; stroke: black; stroke-width: 1;}
        .transistor-circle { fill: none; stroke: black; stroke-width: 2; }
        .led-body { stroke: black; stroke-width: 1; }
        .led-ray { stroke: #FFD700; stroke-width: 2; opacity: 0; transition: opacity 0.2s; }
        .current-path { stroke-width: 2; transition: stroke-width 0.2s, stroke 0.2s; }


        @media (max-width: 768px) {
            .main-layout {
                flex-direction: column;
            }
            .circuit-section, .controls-section {
                flex-basis: auto; /* Reset basis for column layout */
            }
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>NPN Transistor Interactive Demo</h1>

        <div class="main-layout">
            <div class="circuit-section">
                <h2>Circuit Diagram</h2>
                <svg id="circuitDiagram" viewBox="0 0 450 350" preserveAspectRatio="xMidYMid meet">
                    <!-- VCC -->
                    <text x="50" y="35" font-size="14">+Vcc (5V)</text>
                    <line x1="50" y1="50" x2="50" y2="70" class="wire" /> <!-- Vcc rail start -->
                    <line x1="50" y1="50" x2="200" y2="50" class="wire" id="vccRailTop"/> <!-- Vcc rail to Rc -->

                    <!-- Rc (Collector Resistor) -->
                    <line x1="200" y1="50" x2="200" y2="80" class="wire current-path" id="vccToRcPath"/>
                    <rect x="180" y="80" width="40" height="15" class="resistor-body"/>
                    <text x="225" y="90" font-size="12">Rc</text>
                    <line x1="200" y1="95" x2="200" y2="120" class="wire current-path" id="rcToLedPath"/>

                    <!-- LED -->
                    <g id="ledGroup">
                        <line x1="200" y1="120" x2="200" y2="130" class="wire current-path" id="ledAnodePath"/> <!-- Anode wire -->
                        <!-- Diode Symbol: Triangle and Bar -->
                        <polygon points="190,130 210,130 200,145" class="led-body" id="ledDiodeTriangle" fill="red" opacity="0.3"/>
                        <line x1="190" y1="145" x2="210" y2="145" class="led-body" id="ledDiodeBar"/>
                        <!-- Light Rays -->
                        <line x1="185" y1="125" x2="175" y2="115" class="led-ray"/>
                        <line x1="200" y1="115" x2="200" y2="105" class="led-ray"/>
                        <line x1="215" y1="125" x2="225" y2="115" class="led-ray"/>
                        <line x1="200" y1="145" x2="200" y2="160" class="wire current-path" id="collectorCurrentPath"/> <!-- Cathode wire to Collector -->
                    </g>

                    <!-- NPN Transistor -->
                    <g id="transistor">
                        <circle cx="200" cy="200" r="25" class="transistor-circle"/>
                        <!-- Collector -->
                        <line x1="200" y1="160" x2="200" y2="175" class="wire current-path" id="collectorToTransistorPath"/> <!-- Connects LED path to transistor circle -->
                        <text x="205" y="170" font-size="12">C</text>
                        <!-- Base -->
                        <line x1="130" y1="200" x2="175" y2="200" class="wire current-path" id="baseCurrentPath"/> <!-- Rb to Base -->
                        <text x="165" y="195" font-size="12">B</text>
                        <!-- Emitter -->
                        <line x1="200" y1="225" x2="200" y2="250" class="wire current-path" id="emitterCurrentPath"/>
                        <text x="205" y="245" font-size="12">E</text>
                        <!-- Emitter Arrow (NPN: pointing out) -->
                        <polygon points="195,228 205,228 200,238" fill="black" />
                    </g>

                    <!-- Base Circuit -->
                    <text x="50" y="175" font-size="14">Vin</text>
                    <line x1="50" y1="185" x2="50" y2="200" class="wire"/> <!-- Vin rail start -->
                    <line x1="50" y1="200" x2="80" y2="200" class="wire current-path" id="vinToRbPath"/> <!-- Vin to Rb -->
                    
                    <!-- Rb (Base Resistor) -->
                    <rect x="80" y="192.5" width="40" height="15" class="resistor-body"/>
                    <text x="65" y="202.5" font-size="12">Rb</text>
                    <line x1="120" y1="200" x2="130" y2="200" class="wire current-path" id="rbToBaseLeadPath"/> <!-- Rb to Base lead of transistor -->

                    <!-- Ground -->
                    <line x1="200" y1="250" x2="200" y2="280" class="wire current-path" id="emitterToGndPath"/> <!-- Emitter to Ground symbol -->
                    <line x1="180" y1="280" x2="220" y2="280" class="wire"/>
                    <line x1="185" y1="285" x2="215" y2="285" class="wire"/>
                    <line x1="190" y1="290" x2="210" y2="290" class="wire"/>
                    <text x="225" y="285" font-size="14">GND</text>
                </svg>
            </div>

            <div class="controls-section">
                <h2>Controls & Readouts</h2>
                <div class="control-group">
                    <label for="vinBaseSlider">Base Input Voltage (Vin): <span id="vinBaseVal">0.00</span> V</label>
                    <input type="range" id="vinBaseSlider" min="0" max="5" step="0.01" value="0">
                </div>

                <div class="control-group">
                    <label for="rbResistor">Base Resistor (Rb):</label>
                    <select id="rbResistor">
                        <option value="1000">1 kΩ</option>
                        <option value="2200">2.2 kΩ</option>
                        <option value="4700">4.7 kΩ</option>
                        <option value="10000" selected>10 kΩ</option>
                        <option value="22000">22 kΩ</option>
                        <option value="47000">47 kΩ</option>
                        <option value="100000">100 kΩ</option>
                    </select>
                </div>

                <div class="control-group">
                    <label for="rcResistor">Collector Resistor (Rc for LED):</label>
                    <select id="rcResistor">
                        <option value="100">100 Ω</option>
                        <option value="220">220 Ω</option>
                        <option value="330" selected>330 Ω</option>
                        <option value="470">470 Ω</option>
                        <option value="1000">1 kΩ</option>
                    </select>
                </div>

                <div class="readouts">
                    <p><strong>Ib:</strong> <span id="ibVal">0.00</span> µA</p>
                    <p><strong>Ic:</strong> <span id="icVal">0.00</span> mA</p>
                    <p><strong>Vce:</strong> <span id="vceVal">5.00</span> V</p>
                </div>
                <div id="saturationIndicator">CUTOFF</div>
            </div>
        </div>

        <div class="explanation-section">
            <h2>How This Circuit Works</h2>
            <p>This circuit demonstrates how an NPN bipolar junction transistor (BJT) can act as a controlled switch or amplifier. A small current at the Base (B) terminal controls a larger current flowing from the Collector (C) to the Emitter (E).</p>
            
            <h3>Components:</h3>
            <ul>
                <li><strong>NPN Transistor:</strong> The heart of the circuit. It has three terminals: Base (B), Collector (C), and Emitter (E). For an NPN transistor, current flows into the Base, and this allows a larger current to flow from Collector to Emitter.</li>
                <li><strong>Power Source (Vcc):</strong> Provides the voltage (typically 5V in this demo) to power the circuit.</li>
                <li><strong>LED (Light Emitting Diode):</strong> Acts as a load. Its brightness indicates the amount of current flowing through the collector circuit. It also has a forward voltage drop (Vf, typically ~2V for a red LED) when current flows through it.</li>
                <li><strong>Base Resistor (Rb):</strong> Limits the current flowing into the Base of the transistor. This is crucial to prevent damage to the transistor and to set the desired base current (Ib) for a given input voltage (Vin).</li>
                <li><strong>Collector Resistor (Rc):</strong> Limits the current flowing through the LED and the Collector of the transistor. This protects both the LED from overcurrent and the transistor, and also helps determine the operating point of the transistor.</li>
                <li><strong>Input Voltage (Vin):</strong> The control signal applied to the base resistor.</li>
            </ul>

            <h3>Circuit Operation:</h3>
            <p>The behavior of the transistor can be categorized into three main regions:</p>
            <ol>
                <li><strong>Cutoff Region:</strong>
                    <ul>
                        <li>If the voltage at the base (after Vin is applied through Rb) is not enough to overcome the base-emitter forward voltage (Vbe_on, typically ~0.7V for silicon transistors), or if Vin itself is very low, no significant base current (Ib) flows.</li>
                        <li>Consequently, no collector current (Ic) flows. The transistor acts like an open switch.</li>
                        <li>The LED is OFF.</li>
                        <li>The voltage across collector-emitter (Vce) is approximately equal to Vcc because no current flows through Rc and the LED to cause a voltage drop.</li>
                    </ul>
                </li>
                <li><strong>Active Region:</strong>
                    <ul>
                        <li>When Vin is high enough (Vin > Vbe_on), a base current (Ib) starts to flow. It's calculated as: <code>Ib = (Vin - Vbe_on) / Rb</code>.</li>
                        <li>In the active region, the collector current (Ic) is proportional to the base current (Ib) by a factor called the DC current gain (β or hFE): <code>Ic = β * Ib</code>.</li>
                        <li>This demonstrates amplification: a small Ib controls a much larger Ic.</li>
                        <li>The LED turns ON, and its brightness increases with Ic.</li>
                        <li>The collector-emitter voltage (Vce) is given by: <code>Vce = Vcc - Ic * Rc - Vf_LED</code> (where Vf_LED is the forward voltage drop of the LED, if Ic > 0).</li>
                        <li>As Ib increases, Ic increases, and Vce decreases.</li>
                    </ul>
                </li>
                <li><strong>Saturation Region:</strong>
                    <ul>
                        <li>If Ib is increased further, Ic also increases. However, Ic cannot increase indefinitely. It's limited by the external circuit (Vcc, Rc, Vf_LED).</li>
                        <li>When Vce drops to its minimum possible value (Vce_sat, typically ~0.2V), the transistor is said to be saturated. It acts like a closed switch.</li>
                        <li>Further increases in Ib do NOT significantly increase Ic. Ic is now primarily determined by: <code>Ic_sat = (Vcc - Vf_LED - Vce_sat) / Rc</code>.</li>
                        <li>The LED is at its maximum brightness for the given Rc and Vcc.</li>
                        <li>The saturation indicator will highlight this state.</li>
                    </ul>
                </li>
            </ol>
            <p><strong>Relationship between Vce, Ic, and Ib:</strong> The core idea is that Ib controls Ic, and Ic, along with Rc and the LED, determines Vce. The transistor operates along a "load line" defined by Vcc, Rc, and the LED. By changing Ib, you move the operating point along this load line, transitioning between cutoff, active, and saturation regions.</p>
            <p>This interactive demo allows you to adjust Vin (which in turn affects Ib) and see the resulting changes in Ib, Ic, Vce, and the LED's brightness, visually demonstrating these principles.</p>
        </div>
    </div>

    <script>
        const vinBaseSlider = document.getElementById('vinBaseSlider');
        const rbResistorSelect = document.getElementById('rbResistor');
        const rcResistorSelect = document.getElementById('rcResistor');
        
        const vinBaseValSpan = document.getElementById('vinBaseVal');
        const ibValSpan = document.getElementById('ibVal');
        const icValSpan = document.getElementById('icVal');
        const vceValSpan = document.getElementById('vceVal');
        
        const saturationIndicatorDiv = document.getElementById('saturationIndicator');
        const ledDiodeTriangle = document.getElementById('ledDiodeTriangle');
        const ledRays = document.querySelectorAll('.led-ray');

        // SVG Path elements for current visualization
        const vccToRcPath = document.getElementById('vccToRcPath');
        const rcToLedPath = document.getElementById('rcToLedPath');
        const ledAnodePath = document.getElementById('ledAnodePath');
        const collectorCurrentPath = document.getElementById('collectorCurrentPath'); // LED cathode to C
        const collectorToTransistorPath = document.getElementById('collectorToTransistorPath'); // C to transistor body
        
        const vinToRbPath = document.getElementById('vinToRbPath');
        const rbToBaseLeadPath = document.getElementById('rbToBaseLeadPath');
        const baseCurrentPath = document.getElementById('baseCurrentPath'); // Rb to B (inside transistor area)

        const emitterCurrentPath = document.getElementById('emitterCurrentPath'); // E (inside transistor) to lead
        const emitterToGndPath = document.getElementById('emitterToGndPath'); // E lead to GND symbol

        const allCollectorPaths = [vccToRcPath, rcToLedPath, ledAnodePath, collectorCurrentPath, collectorToTransistorPath];
        const allBasePaths = [vinToRbPath, rbToBaseLeadPath, baseCurrentPath];
        const allEmitterPaths = [emitterCurrentPath, emitterToGndPath];


        function updateCircuit() {
            // --- Constants ---
            const VCC = 5.0;       // Volts
            const BETA = 100;      // DC Current Gain (hFE)
            const VBE_ON = 0.7;    // Base-Emitter turn-on voltage (Volts)
            const VCE_SAT = 0.2;   // Collector-Emitter saturation voltage (Volts)
            const VF_LED = 2.0;    // LED forward voltage (Volts, typical red)

            // --- Get User Inputs ---
            let Vin_base_supply = parseFloat(vinBaseSlider.value);
            let Rb_val = parseFloat(rbResistorSelect.value); // Ohms
            let Rc_val = parseFloat(rcResistorSelect.value); // Ohms

            if (Rb_val <= 0) Rb_val = 1e9; // Prevent division by zero, effectively open
            if (Rc_val <= 0) Rc_val = 1e9; // Prevent division by zero, effectively open

            // --- Calculations ---
            let Ib;
            if (Vin_base_supply <= VBE_ON) {
                Ib = 0;
            } else {
                Ib = (Vin_base_supply - VBE_ON) / Rb_val;
            }
            Ib = Math.max(0, Ib); // Ensure Ib is not negative

            let Ic, Vce;
            let state = "CUTOFF";
            let ledBrightness = 0;

            if (Ib <= 1e-9) { // Effectively CUTOFF
                Ib = 0; // Normalize for display
                Ic = 0;
                Vce = VCC;
                state = "CUTOFF";
            } else {
                // Potential Ic if in active region
                let Ic_potential_active = BETA * Ib;

                // Calculate Vce if it were in active region with this Ic_potential_active
                // Voltage drop across LED only if current flows and is sufficient.
                // For simplicity, assume VF_LED drop if Ic_potential_active > 0.
                let Vce_if_active = VCC - (Ic_potential_active * Rc_val) - (Ic_potential_active > 1e-9 ? VF_LED : 0);
                
                if (Vce_if_active <= VCE_SAT) { // SATURATION
                    state = "SATURATION";
                    Vce = VCE_SAT;
                    // Collector current is now limited by the external circuit
                    Ic = (VCC - (VF_LED) - VCE_SAT) / Rc_val; // VF_LED always applies if trying to saturate
                    if (VCC - VF_LED - VCE_SAT < 0) { // Not enough voltage to even turn on LED in saturation
                        Ic = 0;
                         // If Ic becomes 0, then Vce is not VCE_SAT, but VCC or VCC-VF_LED if LED could partially light
                         // This is an edge case. If Ic_calc is 0, effectively it's cutoff-like for collector path.
                         Vce = VCC - (Ic > 1e-9 ? VF_LED : 0); // If Ic is zero, no VF_LED drop.
                         if (Ic <= 1e-9) Vce = VCC; state = "ACTIVE"; // Or cutoff if Ib was also zero
                    }
                } else { // ACTIVE
                    state = "ACTIVE";
                    Ic = Ic_potential_active;
                    Vce = Vce_if_active;
                    // If Ic makes (Ic*Rc + VF_LED) > VCC, then Ic is too high for this model.
                    // This implies Vce would be negative, which is not physical.
                    // The Vce_if_active check handles this; if it's < VCE_SAT, it goes to saturation.
                    // If VCC isn't enough for VF_LED, Ic will be very small or zero.
                    if (VCC < VF_LED && Ic > 0) { // Not enough voltage for LED
                        Ic = 0; // No current can flow through LED
                        Vce = VCC; // No drop across Rc
                        // state remains ACTIVE if Ib > 0, but LED is off.
                    }
                }
            }

            // Final clamping and sanity checks
            Ic = Math.max(0, Ic);
            if (state === "CUTOFF") {
                Vce = VCC;
            } else if (state === "SATURATION") {
                // Vce is already VCE_SAT, but ensure Ic is not such that Vce would be forced higher
                // if (VCC - VF_LED - VCE_SAT) / Rc_val < 0, Ic was set to 0.
                // If Ic is 0, Vce should not be VCE_SAT unless VCC is also very low.
                if (Ic <= 1e-9 && (VCC - VF_LED - VCE_SAT < 0)) {
                     Vce = VCC - (Ic > 1e-9 ? VF_LED : 0); // Recalculate Vce if Ic is forced to 0
                     if (Ic <= 1e-9) Vce = VCC;
                } else {
                     Vce = Math.max(VCE_SAT, Vce); // Vce shouldn't go below VCE_SAT
                }
            } else { // ACTIVE
                 Vce = Math.max(VCE_SAT, Math.min(VCC, Vce));
                 // if Ic is essentially zero due to VCC < VF_LED
                 if (Ic < 1e-9 && (VCC < VF_LED || Vin_base_supply <= VBE_ON)) {
                     Vce = VCC;
                 }
            }


            // LED Brightness
            let Ic_max_for_led_circuit = (VCC - VF_LED - VCE_SAT) / Rc_val;
            if (Ic_max_for_led_circuit <= 0 || Ic <= 1e-9) {
                ledBrightness = 0;
            } else {
                ledBrightness = Math.min(1, Math.max(0, Ic / Ic_max_for_led_circuit));
            }
            if (Ic < 1e-7) ledBrightness = 0; // Ensure LED is visibly off for tiny currents

            // --- Update UI Elements ---
            vinBaseValSpan.textContent = Vin_base_supply.toFixed(2);
            ibValSpan.textContent = (Ib * 1e6).toFixed(2); // µA
            icValSpan.textContent = (Ic * 1e3).toFixed(2); // mA
            vceValSpan.textContent = Vce.toFixed(2);       // V

            ledDiodeTriangle.style.opacity = 0.3 + ledBrightness * 0.7; // Min opacity 0.3, max 1
            ledRays.forEach(ray => ray.style.opacity = ledBrightness);

            if (state === "SATURATION") {
                saturationIndicatorDiv.textContent = 'SATURATION';
                saturationIndicatorDiv.style.color = 'white';
                saturationIndicatorDiv.style.backgroundColor = 'red';
                saturationIndicatorDiv.style.fontWeight = 'bold';
            } else if (state === "CUTOFF") {
                saturationIndicatorDiv.textContent = 'CUTOFF';
                saturationIndicatorDiv.style.color = 'white';
                saturationIndicatorDiv.style.backgroundColor = 'blue';
                saturationIndicatorDiv.style.fontWeight = 'normal';
            } else { // ACTIVE
                saturationIndicatorDiv.textContent = 'ACTIVE REGION';
                saturationIndicatorDiv.style.color = 'white';
                saturationIndicatorDiv.style.backgroundColor = 'green';
                saturationIndicatorDiv.style.fontWeight = 'normal';
            }
            
            updateCurrentFlowVisuals(Ib, Ic);
        }

        function updateCurrentFlowVisuals(ib, ic) {
            const activeColor = "rgba(0, 100, 255, 0.8)"; // A blue for current
            const baseWireColor = "#333"; // Default wire color
            const minStrokeWidth = 2;
            const maxStrokeWidth = 5;

            // Base Current
            let ib_sw = minStrokeWidth;
            if (ib > 1e-9) {
                ib_sw = minStrokeWidth + (ib / 0.0005) * (maxStrokeWidth - minStrokeWidth); // Scale up to 500uA for max width
                ib_sw = Math.min(maxStrokeWidth, Math.max(minStrokeWidth, ib_sw));
                allBasePaths.forEach(p => { p.style.stroke = activeColor; p.style.strokeWidth = ib_sw; });
            } else {
                allBasePaths.forEach(p => { p.style.stroke = baseWireColor; p.style.strokeWidth = minStrokeWidth; });
            }

            // Collector Current
            let ic_sw = minStrokeWidth;
            if (ic > 1e-9) {
                ic_sw = minStrokeWidth + (ic / 0.02) * (maxStrokeWidth - minStrokeWidth); // Scale up to 20mA for max width
                ic_sw = Math.min(maxStrokeWidth, Math.max(minStrokeWidth, ic_sw));
                allCollectorPaths.forEach(p => { p.style.stroke = activeColor; p.style.strokeWidth = ic_sw; });
            } else {
                allCollectorPaths.forEach(p => { p.style.stroke = baseWireColor; p.style.strokeWidth = minStrokeWidth; });
            }
            
            // Emitter Current (Ie = Ib + Ic)
            let ie = ib + ic;
            let ie_sw = minStrokeWidth;
             if (ie > 1e-9) {
                ie_sw = minStrokeWidth + (ie / 0.0205) * (maxStrokeWidth - minStrokeWidth); // Scale up to ~20.5mA for max width
                ie_sw = Math.min(maxStrokeWidth, Math.max(minStrokeWidth, ie_sw));
                allEmitterPaths.forEach(p => { p.style.stroke = activeColor; p.style.strokeWidth = ie_sw; });
            } else {
                allEmitterPaths.forEach(p => { p.style.stroke = baseWireColor; p.style.strokeWidth = minStrokeWidth; });
            }
        }

        // Event Listeners
        vinBaseSlider.addEventListener('input', updateCircuit);
        rbResistorSelect.addEventListener('change', updateCircuit);
        rcResistorSelect.addEventListener('change', updateCircuit);

        // Initial call to set up the circuit display
        window.addEventListener('load', updateCircuit);

    </script>
</body>
</html>
