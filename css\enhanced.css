/* Enhanced Styles for Electronic Circuits Lab */

/* Top Bar with Author Information */
.top-bar {
    background-color: var(--dark-color);
    padding: 8px 0;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.top-bar-contact {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.top-bar-contact span,
.top-bar-author span {
    display: flex;
    align-items: center;
    gap: 8px;
}

.top-bar-contact i,
.top-bar-author i {
    color: var(--accent-color);
}

.top-bar-author span {
    font-weight: 600;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 3px 12px;
    border-radius: 20px;
    border-right: 2px solid var(--accent-color);
}

@media (max-width: 768px) {
    .top-bar-content {
        justify-content: center;
        text-align: center;
    }

    .top-bar-contact {
        justify-content: center;
        margin-bottom: 5px;
    }
}

/* Experiment Overview Styles */
.experiment-overview {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 20px;
    margin: 30px 0;
}

.overview-item {
    flex: 1;
    min-width: 200px;
    background: var(--light-color);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 5px 15px var(--shadow-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.overview-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: var(--gradient-primary);
    opacity: 0.7;
}

.overview-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-hover-shadow);
}

.overview-icon {
    font-size: 2rem;
    color: var(--secondary-color);
    margin-bottom: 10px;
}

.overview-count {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.overview-label {
    font-size: 1rem;
    color: var(--text-color);
}

.experiment-count {
    background-color: var(--light-color);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 5px;
}

.experiment-count span {
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--secondary-color);
}

/* Tabs Styles */
.tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin: 20px 0;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 2px;
}

.tab {
    padding: 12px 25px;
    background-color: var(--light-color);
    border-radius: 10px 10px 0 0;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
}

.tab:hover {
    color: var(--primary-color);
}

.tab.active {
    color: white;
}

.tab.active::before {
    opacity: 1;
}

.tab-content {
    display: none;
    padding: 20px 0;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

.tab-header {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px dashed var(--border-color);
}

.tab-header h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: var(--primary-color);
    position: relative;
    display: inline-block;
}

.tab-header h3::after {
    content: '';
    position: absolute;
    bottom: -5px;
    right: 0;
    width: 50px;
    height: 3px;
    background: var(--gradient-accent);
    border-radius: 3px;
}

.tab-header p {
    color: var(--text-color);
    opacity: 0.8;
    font-size: 1.05rem;
    line-height: 1.6;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Advanced Search Styles */
.search-input-wrapper {
    position: relative;
    display: flex;
    width: 100%;
}

.search-icon {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    color: #777;
}

#experiment-search {
    padding-right: 35px;
}

.advanced-search-toggle {
    text-align: left;
    margin: 10px 0;
}

.text-button {
    background: none;
    border: none;
    color: var(--secondary-color);
    cursor: pointer;
    padding: 5px 10px;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: color 0.3s ease;
}

.text-button:hover {
    color: var(--accent-color);
}

.text-button.active {
    color: var(--accent-color);
}

.advanced-search-options {
    display: none;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 15px;
}

.advanced-search-options.show {
    display: block;
}

.filter-group {
    margin-bottom: 15px;
}

.filter-group h3 {
    font-size: 1rem;
    margin-bottom: 10px;
}

.filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.filter-option {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
}

.search-results-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0;
    padding: 10px;
    background-color: var(--light-color);
    border-radius: var(--border-radius);
}

.sort-options {
    display: flex;
    align-items: center;
    gap: 10px;
}

.sort-options select {
    padding: 5px;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    background-color: var(--card-bg);
    color: var(--text-color);
    margin-bottom: 0;
}

/* Updates Grid */
.updates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.update-card {
    display: flex;
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 5px var(--shadow-color);
}

.update-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    background-color: var(--secondary-color);
    color: white;
    font-size: 1.5rem;
}

.update-content {
    flex: 1;
    padding: 15px;
}

.update-content h3 {
    margin-top: 0;
    color: var(--secondary-color);
}

.update-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    font-size: 0.9rem;
}

.update-date {
    color: #777;
}

.update-link {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Lab Notes Preview */
.lab-notes-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.lab-note-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: 0 2px 5px var(--shadow-color);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.lab-note-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px var(--shadow-color);
}

.lab-note-icon {
    font-size: 2rem;
    color: var(--secondary-color);
    margin-bottom: 15px;
}

/* Newsletter Section */
.newsletter-container {
    display: flex;
    align-items: center;
    gap: 40px;
    background: var(--gradient-primary);
    color: white;
    padding: 40px;
    border-radius: 20px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px var(--shadow-color);
}

.newsletter-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="10" cy="10" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
    opacity: 0.5;
}

.newsletter-content {
    flex: 2;
    position: relative;
    z-index: 1;
}

.newsletter-content h2 {
    color: white;
    font-size: 1.8rem;
    margin-top: 0;
    border-bottom: none;
}

.newsletter-content p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    margin-bottom: 25px;
}

.newsletter-image {
    flex: 1;
    text-align: center;
    position: relative;
    z-index: 1;
}

.newsletter-image img {
    max-width: 100%;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    transform: perspective(800px) rotateY(-10deg);
    transition: transform 0.5s ease;
}

.newsletter-image img:hover {
    transform: perspective(800px) rotateY(0deg);
}

.newsletter-form {
    margin-top: 25px;
}

.form-group {
    display: flex;
    margin-bottom: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-radius: 30px;
    overflow: hidden;
}

.form-group input {
    flex: 1;
    border-radius: 30px 0 0 30px;
    border: none;
    padding: 15px 20px;
    font-size: 1rem;
}

.form-group button {
    border-radius: 0 30px 30px 0;
    margin: 0;
    padding: 15px 25px;
    background: var(--gradient-accent);
    color: var(--dark-color);
    font-weight: bold;
}

.form-check {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
}

.form-check input {
    width: auto;
    margin: 0;
    accent-color: var(--accent-color);
    transform: scale(1.2);
}

/* FAQ Section */
.faq-container {
    margin-top: 30px;
}

.faq-item {
    margin-bottom: 20px;
    border: none;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px var(--shadow-color);
    transition: all 0.3s ease;
}

.faq-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-hover-shadow);
}

.faq-question {
    padding: 20px;
    background: var(--light-color);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.faq-item.active .faq-question {
    background: var(--gradient-primary);
}

.faq-question h3 {
    margin: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.faq-item.active .faq-question h3 {
    color: white;
}

.faq-question i {
    transition: transform 0.3s ease;
    font-size: 1rem;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
    background-color: var(--accent-color);
    color: var(--dark-color);
}

.faq-answer {
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: all 0.5s ease;
    background-color: var(--card-bg);
}

.faq-item.active .faq-answer {
    padding: 20px;
    max-height: 500px;
}

.faq-answer p {
    margin: 0;
    line-height: 1.6;
}

/* Enhanced Footer */
.footer-top {
    padding: 60px 0 40px;
    background: var(--gradient-primary);
    position: relative;
    overflow: hidden;
}

.footer-top::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: var(--gradient-accent);
}

.footer-top::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="150" height="150" viewBox="0 0 150 150"><circle cx="10" cy="10" r="2" fill="rgba(255,255,255,0.03)"/><circle cx="30" cy="10" r="1" fill="rgba(255,255,255,0.02)"/><circle cx="50" cy="10" r="1.5" fill="rgba(255,255,255,0.03)"/></svg>');
    opacity: 0.5;
    pointer-events: none;
}

.footer-columns {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 40px;
    position: relative;
    z-index: 1;
}

.footer-column h3 {
    color: white;
    margin-top: 0;
    font-size: 1.3rem;
    border-bottom: none;
    padding-bottom: 10px;
    margin-bottom: 20px;
    position: relative;
}

.footer-column h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 3px;
    background: var(--accent-color);
    border-radius: 3px;
}

.footer-column p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    font-size: 1.2rem;
}

.social-links a:hover {
    background-color: var(--accent-color);
    color: var(--dark-color);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 12px;
    transition: transform 0.3s ease;
}

.footer-links li:hover {
    transform: translateX(5px);
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.footer-links a::before {
    content: '\f105'; /* FontAwesome arrow icon */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 0.8rem;
    opacity: 0;
    transform: translateX(-5px);
    transition: all 0.3s ease;
}

.footer-links a:hover {
    color: white;
}

.footer-links a:hover::before {
    opacity: 1;
    transform: translateX(0);
}

.footer-bottom {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 20px 0;
    text-align: center;
    position: relative;
    z-index: 1;
}

.footer-bottom p {
    margin: 5px 0;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.95rem;
}

/* Author Information Styles */
.author-info {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    border-right: 3px solid var(--accent-color);
}

.author-name {
    font-size: 1.1rem;
    color: white;
    margin-bottom: 10px;
}

.author-name strong {
    color: var(--accent-color);
    font-weight: 700;
}

.author-contact {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
}

.author-contact span {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: rgba(255, 255, 255, 0.05);
    padding: 5px 12px;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.author-contact span:hover {
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-3px);
}

.author-contact i {
    color: var(--accent-color);
}

/* Scroll to top button */
.scroll-top {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    background: var(--gradient-accent);
    color: var(--dark-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 5px 15px var(--shadow-color);
    cursor: pointer;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
    z-index: 999;
}

.scroll-top.visible {
    opacity: 1;
    transform: translateY(0);
}

.scroll-top:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px var(--shadow-color);
}

/* Screen Reader Only */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}
