<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BJT Common Emitter Simulator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        #app-container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 900px;
        }

        h1, h2 {
            color: #333;
            text-align: center;
        }
        h2 {
            margin-top: 10px;
            font-size: 1.2em;
        }

        #main-content {
            display: flex;
            flex-wrap: wrap; /* Allow wrapping for smaller screens */
            gap: 20px;
            margin-bottom: 20px;
        }

        #schematic-container, #controls-info-container {
            flex: 1;
            min-width: 300px; /* Minimum width before wrapping */
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        
        #schematic-container svg, #graph-container svg {
            width: 100%;
            height: auto;
            display: block;
            margin: 0 auto;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .control-group input[type="range"] {
            width: 100%;
        }

        .value-display {
            font-size: 1em;
            margin-top: 5px;
            color: #007bff;
        }
        
        .output-values p {
            margin: 8px 0;
            font-size: 0.95em;
        }
        .output-values strong {
            color: #555;
        }

        #region-info {
            margin-top: 15px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
        }
        #region-info h3 {
            margin-top: 0;
            font-size: 1.1em;
            color: #0056b3;
        }
        #region-info p {
            font-size: 0.9em;
            line-height: 1.4;
        }

        /* SVG specific styles */
        .resistor-symbol {
            stroke: black;
            stroke-width: 1.5;
            fill: none;
        }
        .wire {
            stroke: black;
            stroke-width: 1;
        }
        .component-label {
            font-size: 10px;
            font-family: monospace;
        }
        .transistor-body {
            stroke: black;
            stroke-width: 1.5;
        }
        .ground-symbol line {
            stroke: black;
            stroke-width: 1;
        }
        .current-arrow {
            stroke-width: 1.5;
            transition: stroke-width 0.3s ease, opacity 0.3s ease;
        }
        
        /* Graph styles */
        .axis-line {
            stroke: #333;
            stroke-width: 1;
        }
        .grid-line {
            stroke: #ccc;
            stroke-width: 0.5;
            stroke-dasharray: 2,2;
        }
        .axis-label {
            font-size: 10px;
            fill: #333;
            text-anchor: middle;
        }
        .tick-label {
            font-size: 8px;
            fill: #555;
        }
        .characteristic-curve {
            fill: none;
            stroke-width: 2;
        }
        .active-region-line {
            stroke: #28a745; /* Green */
        }
        .saturation-region-line {
            stroke: #ffc107; /* Yellow/Orange */
        }
        .operating-point {
            fill: #dc3545; /* Red */
            stroke: #333;
            stroke-width: 0.5;
            transition: cx 0.1s linear, cy 0.1s linear;
        }

        /* Transistor state colors */
        .cutoff-state { fill: #d3d3d3; } /* Light gray */
        .active-state { fill: #90ee90; } /* Light green */
        .saturation-state { fill: #ffcccb; } /* Light red/coral */

        @media (max-width: 768px) {
            #main-content {
                flex-direction: column;
            }
            #app-container {
                padding: 10px;
            }
            h1 { font-size: 1.5em; }
        }

    </style>
</head>
<body>
    <div id="app-container">
        <h1>BJT Common Emitter Simulator</h1>

        <div id="main-content">
            <div id="schematic-container">
                <h2>Circuit Diagram (NPN)</h2>
                <svg id="circuit-diagram" viewBox="0 0 320 280">
                    <!-- Vcc -->
                    <text x="150" y="18" class="component-label" text-anchor="middle">Vcc (+12V)</text>
                    <line x1="150" y1="20" x2="150" y2="40" class="wire"/>

                    <!-- Rc -->
                    <rect x="140" y="40" width="20" height="30" class="resistor-symbol"/>
                    <text x="175" y="55" class="component-label">Rc</text>
                    <text x="175" y="68" class="component-label">1kΩ</text>
                    <line x1="150" y1="70" x2="150" y2="90" class="wire"/>

                    <!-- Transistor -->
                    <circle cx="150" cy="120" r="20" class="transistor-body" id="transistor-symbol-body"/>
                    <line x1="130" y1="120" x2="100" y2="120" class="wire"/> <!-- Base horizontal -->
                    <text x="150" y="115" class="component-label" text-anchor="middle" id="transistor-type-label">NPN</text>
                    <text x="150" y="130" class="component-label" text-anchor="middle" id="transistor-state-text">State</text>

                    <line x1="150" y1="100" x2="150" y2="90" class="wire"/> <!-- Collector to body -->
                    <line x1="150" y1="140" x2="150" y2="170" class="wire"/> <!-- Emitter from body -->
                    <!-- Emitter Arrow (NPN) -->
                    <polygon points="145,155 155,155 150,165" fill="black" transform="translate(0, -5)"/>

                    <!-- Ground for Emitter -->
                    <g class="ground-symbol">
                        <line x1="150" y1="170" x2="150" y2="180" />
                        <line x1="135" y1="180" x2="165" y2="180" />
                        <line x1="140" y1="185" x2="160" y2="185" />
                        <line x1="145" y1="190" x2="155" y2="190" />
                    </g>

                    <!-- Rb -->
                    <line x1="100" y1="120" x2="60" y2="120" class="wire"/>
                    <rect x="40" y="112.5" width="20" height="15" class="resistor-symbol"/>
                    <text x="30" y="105" class="component-label" text-anchor="middle">Rb</text>
                    <text x="30" y="118" class="component-label" text-anchor="middle">10kΩ</text>
                    <line x1="40" y1="120" x2="20" y2="120" class="wire"/>

                    <!-- Vin -->
                    <circle cx="10" cy="120" r="8" stroke="black" stroke-width="1" fill="none"/>
                    <text x="8" y="123" font-size="7px">+</text>
                    <line x1="8" y1="126" x2="12" y2="126" stroke="black" stroke-width="1.5"/> <!-- Minus sign -->
                    <text x="0" y="140" class="component-label">Vin</text>

                    <!-- Current Arrows -->
                    <defs>
                        <marker id="arrowhead-ib" markerWidth="6" markerHeight="4" refX="5" refY="2" orient="auto" markerUnits="strokeWidth">
                            <path d="M0,0 L5,2 L0,4 Z" fill="#007bff"/>
                        </marker>
                        <marker id="arrowhead-ic" markerWidth="6" markerHeight="4" refX="5" refY="2" orient="auto" markerUnits="strokeWidth">
                            <path d="M0,0 L5,2 L0,4 Z" fill="#dc3545"/>
                        </marker>
                    </defs>
                    <!-- Ib Arrow: from Vin towards base -->
                    <line id="ib-arrow" x1="75" y1="115" x2="95" y2="115" stroke="#007bff" class="current-arrow" marker-end="url(#arrowhead-ib)"/>
                    <text x="85" y="110" font-size="9px" fill="#007bff" text-anchor="middle">Ib</text>
                    
                    <!-- Ic Arrow: from Vcc/Rc towards collector -->
                    <line id="ic-arrow" x1="155" y1="75" x2="155" y2="95" stroke="#dc3545" class="current-arrow" marker-end="url(#arrowhead-ic)"/>
                    <text x="168" y="85" font-size="9px" fill="#dc3545">Ic</text>
                </svg>
            </div>

            <div id="controls-info-container">
                <h2>Controls & Values</h2>
                <div class="control-group">
                    <label for="vin-slider">Input Voltage (Vin): <span id="vin-value-display">0.00 V</span></label>
                    <input type="range" id="vin-slider" min="0" max="5" step="0.01" value="0">
                </div>

                <div class="output-values">
                    <p><strong>Base Current (Ib):</strong> <span id="ib-value">0.0 µA</span></p>
                    <p><strong>Collector Current (Ic):</strong> <span id="ic-value">0.00 mA</span></p>
                    <p><strong>Collector-Emitter Voltage (Vce):</strong> <span id="vce-value">12.00 V</span></p>
                </div>

                <div id="region-info">
                    <h3 id="region-name">Cutoff</h3>
                    <p id="region-explanation">Transistor is OFF.</p>
                </div>
            </div>
        </div>

        <div id="graph-container">
            <h2>Ic vs. Ib Characteristic</h2>
            <svg id="ic-ib-graph" viewBox="0 0 450 350">
                <!-- Axes, labels, characteristic curve, operating point will be drawn by JS -->
            </svg>
        </div>
    </div>

    <script>
        // Circuit Parameters
        const VCC = 12;      // Volts
        const RB = 10000;    // Ohms (10k)
        const RC = 1000;     // Ohms (1k)
        const BETA = 100;
        const VBE_ON = 0.7;  // Volts
        const VCE_SAT = 0.2; // Volts

        // Max values for graph scaling and arrow visualization
        const IB_MAX_THEORETICAL = (5.0 - VBE_ON) / RB; // Max possible Ib from Vin=5V
        const IC_MAX_SATURATION = (VCC - VCE_SAT) / RC;

        // DOM Elements
        const vinSlider = document.getElementById('vin-slider');
        const vinValueDisplay = document.getElementById('vin-value-display');
        
        const ibValueDisplay = document.getElementById('ib-value');
        const icValueDisplay = document.getElementById('ic-value');
        const vceValueDisplay = document.getElementById('vce-value');
        
        const regionNameDisplay = document.getElementById('region-name');
        const regionExplanationDisplay = document.getElementById('region-explanation');
        
        const transistorSymbolBody = document.getElementById('transistor-symbol-body');
        const transistorStateText = document.getElementById('transistor-state-text');
        
        const ibArrow = document.getElementById('ib-arrow');
        const icArrow = document.getElementById('ic-arrow');

        const graphSvg = document.getElementById('ic-ib-graph');

        // Graph setup
        const graphPadding = { top: 30, right: 30, bottom: 50, left: 60 };
        const graphViewBox = graphSvg.viewBox.baseVal;
        const graphWidth = graphViewBox.width - graphPadding.left - graphPadding.right;
        const graphHeight = graphViewBox.height - graphPadding.top - graphPadding.bottom;

        const IB_GRAPH_MAX_UA = Math.ceil(IB_MAX_THEORETICAL * 1e6 / 50) * 50 + 50; // Max Ib for graph in µA (e.g., 450 or 500 uA)
        const IC_GRAPH_MAX_MA = Math.ceil(IC_MAX_SATURATION * 1e3 / 2) * 2 + 2; // Max Ic for graph in mA (e.g., 12 or 14 mA)

        function calculateBJTState(vin) {
            let ib, ic, vce, region;

            if (vin <= VBE_ON) { // Cutoff
                ib = 0;
                ic = 0;
                vce = VCC;
                region = "Cutoff";
            } else {
                ib = (vin - VBE_ON) / RB;
                let ic_active = BETA * ib;
                let vce_if_active = VCC - ic_active * RC;

                if (vce_if_active < VCE_SAT) { // Saturation
                    vce = VCE_SAT;
                    ic = (VCC - VCE_SAT) / RC; // Ic is limited by Rc
                    region = "Saturation";
                } else { // Active
                    ic = ic_active;
                    vce = vce_if_active;
                    region = "Active";
                }
            }
            // Clamp VCE to be not less than VCE_SAT (if active but very close to sat) and not more than VCC
            vce = Math.max(VCE_SAT, Math.min(vce, VCC));
            if (region === "Active" && vce === VCE_SAT) region = "Saturation"; // Edge case refinement
            if (ib < 0) ib = 0; // Ensure Ib is not negative
            if (ic < 0) ic = 0; // Ensure Ic is not negative

            return { ib, ic, vce, region };
        }

        function updateUI(vin) {
            const state = calculateBJTState(vin);

            vinValueDisplay.textContent = `${parseFloat(vin).toFixed(2)} V`;
            
            ibValueDisplay.textContent = `${(state.ib * 1e6).toFixed(1)} µA`;
            icValueDisplay.textContent = `${(state.ic * 1e3).toFixed(2)} mA`;
            vceValueDisplay.textContent = `${state.vce.toFixed(2)} V`;

            regionNameDisplay.textContent = state.region;
            transistorStateText.textContent = state.region.toUpperCase();
            
            let explanation = "";
            transistorSymbolBody.classList.remove('cutoff-state', 'active-state', 'saturation-state');
            if (state.region === "Cutoff") {
                explanation = "The transistor is OFF. Vin is too low (<= 0.7V) to turn on the base-emitter junction. No significant base current (Ib ≈ 0) flows, so no collector current (Ic ≈ 0) flows. Vce is approximately equal to Vcc.";
                transistorSymbolBody.classList.add('cutoff-state');
            } else if (state.region === "Active") {
                explanation = "The transistor is ON and acting as an amplifier. Vin is high enough to forward bias the base-emitter junction, allowing base current (Ib) to flow. This Ib controls a larger collector current (Ic = β * Ib). Vce is between Vcc and Vce(sat) (approx 0.2V).";
                transistorSymbolBody.classList.add('active-state');
            } else { // Saturation
                explanation = "The transistor is fully ON, like a closed switch. Vin is high, causing a large enough Ib that the transistor tries to pass more collector current than the external circuit (Rc and Vcc) allows. Ic is limited to (Vcc - Vce(sat)) / Rc. Vce is at its minimum (≈ 0.2V).";
                transistorSymbolBody.classList.add('saturation-state');
            }
            regionExplanationDisplay.textContent = explanation;

            updateCurrentArrows(state.ib, state.ic);
            updateGraph(state.ib, state.ic);
        }

        function updateCurrentArrows(ib, ic) {
            const mapCurrentToStyle = (current, maxCurrent, arrowElement) => {
                const minStroke = 1;
                const maxStroke = 4;
                const minOpacity = 0.2;
                const maxOpacity = 1.0;

                if (current <= 1e-9) { // Effectively zero
                    arrowElement.style.strokeWidth = `${minStroke}px`;
                    arrowElement.style.opacity = minOpacity;
                } else {
                    let ratio = Math.min(current / maxCurrent, 1.0); // Cap at 1.0
                    if (maxCurrent === 0) ratio = 1.0; // Avoid division by zero if maxCurrent is somehow 0
                    
                    arrowElement.style.strokeWidth = `${minStroke + ratio * (maxStroke - minStroke)}px`;
                    arrowElement.style.opacity = minOpacity + ratio * (maxOpacity - minOpacity);
                }
            };
            
            mapCurrentToStyle(ib, IB_MAX_THEORETICAL, ibArrow);
            mapCurrentToStyle(ic, IC_MAX_SATURATION, icArrow);
        }
        
        function initGraph() {
            graphSvg.innerHTML = ''; // Clear previous graph elements

            // Create group for graph elements for easier padding
            const g = document.createElementNS("http://www.w3.org/2000/svg", "g");
            g.setAttribute("transform", `translate(${graphPadding.left}, ${graphPadding.top})`);
            graphSvg.appendChild(g);

            // Draw X and Y axes
            const xAxis = document.createElementNS("http://www.w3.org/2000/svg", "line");
            xAxis.setAttribute("x1", 0);
            xAxis.setAttribute("y1", graphHeight);
            xAxis.setAttribute("x2", graphWidth);
            xAxis.setAttribute("y2", graphHeight);
            xAxis.setAttribute("class", "axis-line");
            g.appendChild(xAxis);

            const yAxis = document.createElementNS("http://www.w3.org/2000/svg", "line");
            yAxis.setAttribute("x1", 0);
            yAxis.setAttribute("y1", 0);
            yAxis.setAttribute("x2", 0);
            yAxis.setAttribute("y2", graphHeight);
            yAxis.setAttribute("class", "axis-line");
            g.appendChild(yAxis);

            // Axis labels
            const xLabel = document.createElementNS("http://www.w3.org/2000/svg", "text");
            xLabel.setAttribute("x", graphWidth / 2);
            xLabel.setAttribute("y", graphHeight + graphPadding.bottom / 1.5);
            xLabel.setAttribute("class", "axis-label");
            xLabel.textContent = `Ib (µA)`;
            g.appendChild(xLabel);

            const yLabel = document.createElementNS("http://www.w3.org/2000/svg", "text");
            yLabel.setAttribute("transform", `rotate(-90)`);
            yLabel.setAttribute("x", -graphHeight / 2);
            yLabel.setAttribute("y", -graphPadding.left / 1.5);
            yLabel.setAttribute("class", "axis-label");
            yLabel.textContent = `Ic (mA)`;
            g.appendChild(yLabel);

            // Ticks and grid lines for X axis (Ib)
            const numXTicks = 5;
            for (let i = 0; i <= numXTicks; i++) {
                const ib_ua = (IB_GRAPH_MAX_UA / numXTicks) * i;
                const x = (ib_ua / IB_GRAPH_MAX_UA) * graphWidth;
                
                const tick = document.createElementNS("http://www.w3.org/2000/svg", "line");
                tick.setAttribute("x1", x);
                tick.setAttribute("y1", graphHeight);
                tick.setAttribute("x2", x);
                tick.setAttribute("y2", graphHeight + 5);
                tick.setAttribute("class", "axis-line");
                g.appendChild(tick);

                const tickLabel = document.createElementNS("http://www.w3.org/2000/svg", "text");
                tickLabel.setAttribute("x", x);
                tickLabel.setAttribute("y", graphHeight + 15);
                tickLabel.setAttribute("class", "tick-label");
                tickLabel.setAttribute("text-anchor", "middle");
                tickLabel.textContent = ib_ua.toFixed(0);
                g.appendChild(tickLabel);

                if (i > 0) {
                    const gridLine = document.createElementNS("http://www.w3.org/2000/svg", "line");
                    gridLine.setAttribute("x1", x);
                    gridLine.setAttribute("y1", 0);
                    gridLine.setAttribute("x2", x);
                    gridLine.setAttribute("y2", graphHeight);
                    gridLine.setAttribute("class", "grid-line");
                    g.appendChild(gridLine);
                }
            }

            // Ticks and grid lines for Y axis (Ic)
            const numYTicks = Math.floor(IC_GRAPH_MAX_MA / 2); // e.g. if max 14mA, 7 ticks
            for (let i = 0; i <= numYTicks; i++) {
                const ic_ma = (IC_GRAPH_MAX_MA / numYTicks) * i;
                const y = graphHeight - (ic_ma / IC_GRAPH_MAX_MA) * graphHeight;

                const tick = document.createElementNS("http://www.w3.org/2000/svg", "line");
                tick.setAttribute("x1", -5);
                tick.setAttribute("y1", y);
                tick.setAttribute("x2", 0);
                tick.setAttribute("y2", y);
                tick.setAttribute("class", "axis-line");
                g.appendChild(tick);

                const tickLabel = document.createElementNS("http://www.w3.org/2000/svg", "text");
                tickLabel.setAttribute("x", -10);
                tickLabel.setAttribute("y", y + 3); // Adjust for text alignment
                tickLabel.setAttribute("class", "tick-label");
                tickLabel.setAttribute("text-anchor", "end");
                tickLabel.textContent = ic_ma.toFixed(0);
                g.appendChild(tickLabel);
                
                if (i > 0) {
                    const gridLine = document.createElementNS("http://www.w3.org/2000/svg", "line");
                    gridLine.setAttribute("x1", 0);
                    gridLine.setAttribute("y1", y);
                    gridLine.setAttribute("x2", graphWidth);
                    gridLine.setAttribute("y2", y);
                    gridLine.setAttribute("class", "grid-line");
                    g.appendChild(gridLine);
                }
            }

            // Draw characteristic curve
            // Active region part
            const ib_sat_edge_A = IC_MAX_SATURATION / BETA; // Ib at the edge of saturation
            const ib_sat_edge_uA = ib_sat_edge_A * 1e6;
            const ic_max_sat_mA = IC_MAX_SATURATION * 1e3;

            const x_active_end = (ib_sat_edge_uA / IB_GRAPH_MAX_UA) * graphWidth;
            const y_active_end = graphHeight - (ic_max_sat_mA / IC_GRAPH_MAX_MA) * graphHeight;
            
            const activeLine = document.createElementNS("http://www.w3.org/2000/svg", "path");
            activeLine.setAttribute("d", `M 0 ${graphHeight} L ${x_active_end} ${y_active_end}`);
            activeLine.setAttribute("class", "characteristic-curve active-region-line");
            g.appendChild(activeLine);

            // Saturation region part
            const x_sat_end = graphWidth; // End of graph
            const saturationLine = document.createElementNS("http://www.w3.org/2000/svg", "path");
            saturationLine.setAttribute("d", `M ${x_active_end} ${y_active_end} L ${x_sat_end} ${y_active_end}`);
            saturationLine.setAttribute("class", "characteristic-curve saturation-region-line");
            g.appendChild(saturationLine);

            // Operating point
            const opPoint = document.createElementNS("http://www.w3.org/2000/svg", "circle");
            opPoint.setAttribute("id", "operating-point");
            opPoint.setAttribute("r", 5);
            opPoint.setAttribute("class", "operating-point");
            g.appendChild(opPoint); // Add to g for correct coordinate system
        }

        function updateGraph(ib, ic) {
            const opPoint = document.getElementById('operating-point');
            if (!opPoint) return;

            const ib_ua = ib * 1e6;
            const ic_ma = ic * 1e3;

            let x = (ib_ua / IB_GRAPH_MAX_UA) * graphWidth;
            let y = graphHeight - (ic_ma / IC_GRAPH_MAX_MA) * graphHeight;

            // Clamp to graph boundaries
            x = Math.max(0, Math.min(x, graphWidth));
            y = Math.max(0, Math.min(y, graphHeight));

            opPoint.setAttribute("cx", x);
            opPoint.setAttribute("cy", y);
        }
        
        // Event Listener
        vinSlider.addEventListener('input', (event) => {
            updateUI(event.target.value);
        });

        // Initial call
        initGraph();
        updateUI(vinSlider.value);

    </script>
</body>
</html>
