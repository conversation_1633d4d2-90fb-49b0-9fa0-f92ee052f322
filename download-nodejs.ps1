# PowerShell script to download Node.js installer

$nodeJsUrl = "https://nodejs.org/dist/v18.16.1/node-v18.16.1-x64.msi"
$outputPath = "$PSScriptRoot\node-installer.msi"

Write-Host "Downloading Node.js installer..."
Invoke-WebRequest -Uri $nodeJsUrl -OutFile $outputPath

if (Test-Path $outputPath) {
    Write-Host "Download completed successfully. The installer is saved at: $outputPath"
    Write-Host "Please run the installer to install Node.js."
} else {
    Write-Host "Failed to download Node.js installer."
}

Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
