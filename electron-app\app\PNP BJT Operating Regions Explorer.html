<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PNP BJT Explorer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .app-container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 900px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }

        .main-layout {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .controls-panel {
            flex: 1;
            min-width: 280px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }

        .simulation-display {
            flex: 2;
            min-width: 300px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .circuit-outputs-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: flex-start; /* Align items to the top */
        }

        .circuit-diagram-container {
            flex: 1; /* Allow it to take space */
            min-width: 220px; /* Ensure it has enough width */
            text-align: center; /* Center SVG if it's smaller */
        }

        .outputs-panel {
            flex: 1; /* Allow it to take space */
            min-width: 180px; /* Ensure it has enough width */
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }


        .control-group {
            margin-bottom: 15px;
        }
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .control-group input[type="range"] {
            width: calc(100% - 70px);
            margin-right: 10px;
        }
        .control-group span {
            display: inline-block;
            width: 60px;
            text-align: right;
        }

        #bjtRegionIndicatorText {
            padding: 8px;
            border-radius: 4px;
            text-align: center;
            font-weight: bold;
            margin-top: 10px;
            border: 1px solid transparent;
        }
        .tooltip {
            position: relative;
            display: inline-block;
        }
        .tooltip .tooltiptext {
            visibility: hidden;
            width: 220px;
            background-color: #555;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px 0;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -110px;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        .graph-panel {
            width: 100%;
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        #transferCurveGraph {
            width: 100%;
            height: 300px;
            background-color: #fff;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        .explanations-panel {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .explanations-panel h3 {
            margin-top: 0;
        }
        .explanations-panel p {
            margin-bottom: 10px;
            font-size: 0.9em;
            line-height: 1.4;
        }
        .explanations-panel details {
            margin-bottom: 10px;
        }
        .explanations-panel summary {
            font-weight: bold;
            cursor: pointer;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .main-layout {
                flex-direction: column;
            }
            .controls-panel, .simulation-display {
                min-width: 100%;
            }
            .circuit-outputs-container {
                flex-direction: column;
                align-items: center; /* Center items when stacked */
            }
            .circuit-diagram-container, .outputs-panel {
                width: 100%;
                max-width: 300px; /* Limit width for better centering */
            }
        }

    </style>
</head>
<body>
    <div class="app-container">
        <h1>PNP BJT Operating Regions Explorer</h1>

        <div class="main-layout">
            <section id="circuit-diagram-workbench-pnp" style="width: 100%; margin-bottom: 20px; padding: 15px; border: 1px solid #007bff; background-color: #f0f8ff; border-radius: 5px;">
                <h2>Circuit Diagram & Workbench</h2>
                <div class="circuit-diagram-container" style="text-align: center; margin-bottom: 15px;">
                    <p><strong>Circuit Diagram:</strong></p>
                    <!-- Placeholder for PNP circuit diagram -->
                    <img src="images/placeholder_circuit_pnp_regions.png" alt="PNP BJT Regions Circuit Diagram" style="max-width: 400px; border: 1px solid #ccc; padding: 10px;">
                </div>
                <div class="workbench-container" style="border: 1px solid #0056b3; padding: 15px; margin-top: 10px; background-color: #e7f3fe;">
                    <p><strong>Workbench:</strong></p>
                    <p><em>(Interactive components for circuit design will be added here)</em></p>
                    <div>
                        <label for="component-select-pnp">Select Component:</label>
                        <select id="component-select-pnp">
                            <option value="resistor">Resistor</option>
                            <option value="capacitor">Capacitor</option>
                            <option value="transistor_pnp">PNP Transistor</option>
                            <option value="voltage_source_pnp">PNP Voltage Source</option>
                    </select>
                    <button id="addComponentButtonPnpRegions">Add Component</button>
                </div>
                <div id="interactive-circuit-drawing-area-pnp" style="width: 100%; height: 200px; background-color: #e0e0e0; margin-top:10px; border:1px dashed #999; display:flex; flex-direction: column; align-items:center; justify-content:center;">
                    <p>Circuit Drawing Area (Under Development)</p>
                </div>
                </div>
            </section>

            <div class="controls-panel">
                <h2>Controls</h2>
                <div class="control-group">
                    <label for="vcc">VCC (Supply Voltage):</label>
                    <input type="range" id="vcc" min="1" max="20" value="10" step="0.1">
                    <span id="vccValue">10.0 V</span>
                </div>
                <div class="control-group">
                    <label for="rc">RC (Collector Resistor):</label>
                    <input type="range" id="rc" min="100" max="10000" value="1000" step="100">
                    <span id="rcValue">1.00 kΩ</span>
                </div>
                <div class="control-group">
                    <label for="rb">RB (Base Resistor):</label>
                    <input type="range" id="rb" min="1000" max="1000000" value="100000" step="1000">
                    <span id="rbValue">100 kΩ</span>
                </div>
                <div class="control-group">
                    <label for="vin">Vin (Base Input Voltage):</label>
                    <input type="range" id="vin" min="0" max="10" value="5" step="0.01"> <!-- Max will be updated by VCC -->
                    <span id="vinValue">5.00 V</span>
                </div>
            </div>

            <div class="simulation-display">
                <div class="circuit-outputs-container">
                    <div class="circuit-diagram-container">
                        <h3>Circuit (PNP Common Emitter)</h3>
                        <svg id="circuitDiagram" width="220" height="180" viewBox="0 0 220 180" style="display: block; margin: auto;">
                            <rect id="bjtRegionBg" x="78" y="33" width="44" height="44" fill="#E0E0E0" opacity="0.7" />
                            <!-- VCC -->
                            <text x="95" y="15" font-size="10px" fill="#333">VCC</text>
                            <line x1="100" y1="20" x2="100" y2="35" stroke="black" stroke-width="1.5"/>
                          
                            <!-- PNP Transistor (Emitter at top) -->
                            <circle cx="100" cy="55" r="18" stroke="black" fill="white" stroke-width="1.5"/> <!-- Body -->
                            <line x1="100" y1="37" x2="100" y2="46" stroke="black" stroke-width="1.5"/> <!-- Emitter line to circle -->
                            <line x1="100" y1="46" x2="90" y2="50" stroke="black" stroke-width="1.5"/> <!-- Emitter internal line -->
                            <polygon points="90,50 94,47 94,53" fill="black" stroke="black"/> <!-- Emitter arrow (points in for PNP) -->
                            <text x="105" y="40" font-size="10px" fill="#333">E</text>
                          
                            <line x1="82" y1="55" x2="60" y2="55" stroke="black" stroke-width="1.5"/> <!-- Base line from circle left -->
                            <text x="70" y="50" font-size="10px" fill="#333">B</text>
                          
                            <line x1="100" y1="73" x2="100" y2="90" stroke="black" stroke-width="1.5"/> <!-- Collector line from circle bottom -->
                            <text x="105" y="80" font-size="10px" fill="#333">C</text>
                          
                            <!-- Base Resistor RB and Vin -->
                            <line x1="0" y1="55" x2="15" y2="55" stroke="black" stroke-width="1.5"/>
                            <circle cx="8" cy="55" r="4" stroke="black" fill="white" stroke-width="1"/>
                            <text x="6" y="58" font-size="8px" fill="#333">V</text>
                            <text x="0" y="50" font-size="10px" fill="#333">Vin</text>
                            <line x1="15" y1="55" x2="30" y2="55" stroke="black" stroke-width="1.5"/>
                            <rect x="30" y="50" width="30" height="10" stroke="black" fill="none" stroke-width="1"/>
                            <text x="35" y="45" font-size="10px" fill="#333">RB</text>
                          
                            <!-- Collector Resistor RC -->
                            <line x1="100" y1="90" x2="100" y2="100" stroke="black" stroke-width="1.5"/>
                            <rect x="95" y="100" width="10" height="30" stroke="black" fill="none" stroke-width="1"/>
                            <text x="110" y="115" font-size="10px" fill="#333">RC</text>
                            <line x1="100" y1="130" x2="100" y2="140" stroke="black" stroke-width="1.5"/>
                          
                            <!-- Ground -->
                            <line x1="100" y1="140" x2="100" y2="150" stroke="black" stroke-width="1.5"/>
                            <line x1="90" y1="150" x2="110" y2="150" stroke="black" stroke-width="1.5"/>
                            <line x1="93" y1="155" x2="107" y2="155" stroke="black" stroke-width="1.5"/>
                            <line x1="96" y1="160" x2="104" y2="160" stroke="black" stroke-width="1.5"/>
                          
                            <!-- Vout / VCE -->
                            <text x="130" y="60" font-size="10px" fill="#333">VCE (Vout)</text>
                            <line x1="100" y1="35" x2="125" y2="35" stroke-dasharray="2,2" stroke="gray" stroke-width="1"/>
                            <line x1="100" y1="90" x2="125" y2="90" stroke-dasharray="2,2" stroke="gray" stroke-width="1"/>
                            <line x1="125" y1="35" x2="125" y2="90" stroke="gray" stroke-width="1"/>
                            <text x="123" y="32" font-size="10px" fill="gray">+</text>
                            <text x="123" y="98" font-size="10px" fill="gray">-</text>
                        </svg>
                    </div>
                    <div class="outputs-panel">
                        <h3>Calculated Values</h3>
                        <p>IB (Base Current): <strong id="ibValue">0 µA</strong></p>
                        <p>IC (Collector Current): <strong id="icValue">0 mA</strong></p>
                        <p>VCE (Collector-Emitter Voltage): <strong id="vceValue">0 V</strong></p>
                        <div class="tooltip">
                            <div id="bjtRegionIndicatorText">Region: --</div>
                            <span class="tooltiptext" id="regionTooltipText">--</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="graph-panel">
            <h3>Vin vs. Vout (VCE) Graph</h3>
            <canvas id="transferCurveGraph"></canvas>
        </div>

        <div class="explanations-panel">
            <h2>BJT Operating Regions Explained (PNP)</h2>
            <details>
                <summary>Cut-off Region</summary>
                <p>The transistor is effectively "OFF". This occurs when the emitter-base junction is not sufficiently forward-biased (for PNP, VE - VB ≤ VBE_on ≈ 0.7V). In this state, the base current (IB) is negligible (ideally zero), and consequently, the collector current (IC) is also negligible. The collector-emitter voltage (VCE) is approximately equal to the supply voltage (VCC) as no current flows through the collector resistor (RC).</p>
            </details>
            <details>
                <summary>Active Region</summary>
                <p>The transistor acts as a current amplifier. The emitter-base junction is forward-biased (VE - VB > VBE_on), and the collector-base junction is reverse-biased. In this region, the collector current (IC) is proportional to the base current (IB) by a factor called Beta (β or hFE), i.e., IC = β * IB. Changes in IB lead to amplified changes in IC. VCE is greater than VCE_sat (typically ~0.2V) and less than VCC. This is the region used for analog amplification.</p>
            </details>
            <details>
                <summary>Saturation Region</summary>
                <p>The transistor is "fully ON", acting like a closed switch. Both the emitter-base and collector-base junctions are forward-biased. The base current (IB) is large enough that β * IB is greater than the maximum current the collector circuit (RC and VCC) can supply. IC is now limited by the external circuit, approximately IC_sat = (VCC - VCE_sat) / RC. The collector-emitter voltage (VCE) drops to a small, nearly constant saturation voltage (VCE_sat, typically ~0.2V).</p>
            </details>
        </div>

        <section id="experiment-procedure-pnp">
            <h2>الخطوات الأساسية لإجراء التجربة (PNP)</h2>
            <ol>
                <li><strong>فهم الهدف:</strong> استكشاف كيف تتغير تيارات وجهود الترانزستور PNP ومنطقة عمله بتغيير قيم V<sub>EB</sub> و V<sub>EC</sub>. (لاحظ أننا نتعامل مع V<sub>EB</sub> = V<sub>E</sub> - V<sub>B</sub> و V<sub>EC</sub> = V<sub>E</sub> - V<sub>C</sub> للترانزستور PNP).</li>
                <li><strong>التعرف على واجهة المحاكاة:</strong>
                    <ul>
                        <li><strong>V<sub>EB_in</sub> (جهد الباعث-القاعدة):</strong> هذا هو جهد الدخل الرئيسي الذي يتحكم في تيار القاعدة. تذكر أنه في PNP، يكون الباعث موجبًا بالنسبة للقاعدة لكي يمر تيار القاعدة.</li>
                        <li><strong>V<sub>EC_in</sub> (جهد الباعث-المجمع):</strong> هذا هو الجهد عبر طرفي المجمع والباعث. تذكر أنه في PNP، يكون الباعث موجبًا بالنسبة للمجمع لكي يعمل الترانزستور بشكل صحيح.</li>
                        <li><strong>β (Beta):</strong> معامل تكبير التيار.</li>
                    </ul>
                </li>
                <li><strong>استكشاف منطقة القطع (Cutoff):</strong>
                    <ul>
                        <li>اجعل V<sub>EB_in</sub> صغيرة (أقل من حوالي 0.7 فولت، مثلاً 0.2 فولت).</li>
                        <li>غيّر V<sub>EC_in</sub> (مثلاً من 1 فولت إلى 10 فولت).
                        <li>لاحظ أن I<sub>B</sub> و I<sub>C</sub> تكونان صغيرتين جداً (قريبة من الصفر). الترانزستور "مغلق".</li>
                        <li>سجل ملاحظاتك في الجدول.</li>
                    </ul>
                </li>
                <li><strong>استكشاف المنطقة الفعالة (Active Region):</strong>
                    <ul>
                        <li>اجعل V<sub>EB_in</sub> أكبر من جهد العتبة (مثلاً 0.7 فولت أو 0.8 فولت).
                        <li>ابدأ بـ V<sub>EC_in</sub> أكبر من V<sub>EB_in</sub> (مثلاً إذا V<sub>EB_in</sub> = 0.7V، اجعل V<sub>EC_in</sub> = 2V أو أكثر).
                        <li>لاحظ أن I<sub>C</sub> ≈ β * I<sub>B</sub>. الترانزستور يعمل كمضخم.
                        <li>غيّر V<sub>EB_in</sub> قليلاً ولاحظ كيف يتغير I<sub>C</sub> بشكل كبير.
                        <li>غيّر V<sub>EC_in</sub> (مع بقائه أكبر من V<sub>EB_in</sub>) ولاحظ أن I<sub>C</sub> يتغير بشكل طفيف فقط.
                        <li>سجل ملاحظاتك.</li>
                    </ul>
                </li>
                <li><strong>استكشاف منطقة التشبع (Saturation):</strong>
                    <ul>
                        <li>حافظ على V<sub>EB_in</sub> في قيمة تجعل الترانزستور موصلاً (مثلاً 0.8 فولت).
                        <li>قلل V<sub>EC_in</sub> تدريجياً حتى يصبح قريباً من أو أقل قليلاً من V<sub>EB_in</sub> (مثلاً V<sub>EC_in</sub> ≤ 0.3V أو قريب من V<sub>EB_sat</sub>).
                        <li>لاحظ أن I<sub>C</sub> يتوقف عن الزيادة مع زيادة I<sub>B</sub> (أو زيادة V<sub>EB_in</sub>)، ويصبح V<sub>EC</sub> صغيراً وثابتاً (V<sub>ECsat</sub>، عادة حوالي 0.2V-0.3V).
                        <li>في هذه المنطقة، I<sub>C</sub> < β * I<sub>B</sub>. الترانزستور "مفتوح بالكامل".</li>
                        <li>سجل ملاحظاتك.</li>
                    </ul>
                </li>
                <li><strong>تأثير β:</strong>
                    <ul>
                        <li>اختر قيم لـ V<sub>EB_in</sub> و V<sub>EC_in</sub> بحيث يكون الترانزستور في المنطقة الفعالة.
                        <li>غيّر قيمة β ولاحظ كيف يتغير I<sub>C</sub> (مع ثبات I<sub>B</sub>).
                        <li>سجل ملاحظاتك.</li>
                    </ul>
                </li>
            </ol>
        </section>

        <section id="report-writing-pnp">
            <h2>كتابة تقرير التجربة (PNP)</h2>
            <p>يهدف التقرير إلى تلخيص فهمك لأوضاع تشغيل الترانزستور PNP وكيفية التحكم فيها. يجب أن يتضمن التقرير العناصر التالية:</p>
            <ul>
                <li><strong>مقدمة:</strong> وصف موجز للترانزستور PNP وأهمية فهم أوضاع تشغيله.</li>
                <li><strong>الأدوات المستخدمة:</strong> ذكر أن التجربة تمت باستخدام "مستكشف أوضاع تشغيل الترانزستور PNP".</li>
                <li><strong>الخطوات المتبعة:</strong> تلخيص للخطوات التي قمت بها لاستكشاف كل منطقة تشغيل.</li>
                <li><strong>النتائج والملاحظات:</strong> عرض البيانات التي جمعتها في جداول، مع ملاحظاتك حول سلوك الترانزستور في كل منطقة.</li>
                <li><strong>تحليل النتائج:</strong>
                    <ul>
                        <li>اشرح كيف تتوافق النتائج مع الخصائص النظرية للترانزستور PNP.</li>
                        <li>ناقش الشروط (قيم V<sub>EB</sub> و V<sub>EC</sub>) التي تحدد كل منطقة تشغيل.</li>
                        <li>قارن بين سلوك الترانزستور في المناطق الثلاث.</li>
                    </ul>
                </li>
                <li><strong>الاستنتاج:</strong> تلخيص لما تعلمته عن التحكم في أوضاع تشغيل الترانزستور PNP وتطبيقاته المحتملة.</li>
            </ul>

            <h3>جداول مقترحة لتسجيل النتائج:</h3>
            <h4>جدول 1: استكشاف منطقة القطع</h4>
            <table>
                <thead>
                    <tr>
                        <th>V<sub>EB_in</sub> (V)</th>
                        <th>V<sub>EC_in</sub> (V)</th>
                        <th>β</th>
                        <th>I<sub>B</sub> (µA)</th>
                        <th>I<sub>C</sub> (mA)</th>
                        <th>V<sub>EB_actual</sub> (V)</th>
                        <th>V<sub>EC_actual</sub> (V)</th>
                        <th>منطقة العمل</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>0.2</td><td>5</td><td>100</td><td></td><td></td><td></td><td></td><td></td><td></td>
                    </tr>
                    <tr>
                        <td>0.5</td><td>5</td><td>100</td><td></td><td></td><td></td><td></td><td></td><td></td>
                    </tr>
                    <!-- أضف المزيد من الصفوف حسب الحاجة -->
                </tbody>
            </table>

            <h4>جدول 2: استكشاف المنطقة الفعالة</h4>
            <table>
                <thead>
                    <tr>
                        <th>V<sub>EB_in</sub> (V)</th>
                        <th>V<sub>EC_in</sub> (V)</th>
                        <th>β</th>
                        <th>I<sub>B</sub> (µA)</th>
                        <th>I<sub>C</sub> (mA)</th>
                        <th>V<sub>EB_actual</sub> (V)</th>
                        <th>V<sub>EC_actual</sub> (V)</th>
                        <th>منطقة العمل</th>
                        <th>ملاحظات (هل I<sub>C</sub> ≈ β*I<sub>B</sub>?)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>0.7</td><td>5</td><td>100</td><td></td><td></td><td></td><td></td><td></td><td></td>
                    </tr>
                    <tr>
                        <td>0.75</td><td>5</td><td>100</td><td></td><td></td><td></td><td></td><td></td><td></td>
                    </tr>
                    <tr>
                        <td>0.7</td><td>10</td><td>100</td><td></td><td></td><td></td><td></td><td></td><td></td>
                    </tr>
                    <tr>
                        <td>0.7</td><td>5</td><td>200</td><td></td><td></td><td></td><td></td><td></td><td></td>
                    </tr>
                    <!-- أضف المزيد من الصفوف حسب الحاجة -->
                </tbody>
            </table>

            <h4>جدول 3: استكشاف منطقة التشبع</h4>
            <table>
                <thead>
                    <tr>
                        <th>V<sub>EB_in</sub> (V)</th>
                        <th>V<sub>EC_in</sub> (V)</th>
                        <th>β</th>
                        <th>I<sub>B</sub> (µA)</th>
                        <th>I<sub>C</sub> (mA)</th>
                        <th>V<sub>EB_actual</sub> (V)</th>
                        <th>V<sub>EC_actual</sub> (V)</th>
                        <th>منطقة العمل</th>
                        <th>ملاحظات (هل V<sub>EC_actual</sub> صغير وثابت؟)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>0.8</td><td>0.5</td><td>100</td><td></td><td></td><td></td><td></td><td></td><td></td>
                    </tr>
                    <tr>
                        <td>0.85</td><td>0.2</td><td>100</td><td></td><td></td><td></td><td></td><td></td><td></td>
                    </tr>
                    <tr>
                        <td>0.9</td><td>0.1</td><td>100</td><td></td><td></td><td></td><td></td><td></td><td></td>
                    </tr>
                    <!-- أضف المزيد من الصفوف حسب الحاجة -->
                </tbody>
            </table>
        </section>

    </div>

    <script>
        const vccSlider = document.getElementById('vcc');
        const rcSlider = document.getElementById('rc');
        const rbSlider = document.getElementById('rb');
        const vinSlider = document.getElementById('vin');

        const vccValueSpan = document.getElementById('vccValue');
        const rcValueSpan = document.getElementById('rcValue');
        const rbValueSpan = document.getElementById('rbValue');
        const vinValueSpan = document.getElementById('vinValue');

        const ibValueDisplay = document.getElementById('ibValue');
        const icValueDisplay = document.getElementById('icValue');
        const vceValueDisplay = document.getElementById('vceValue');
        
        const bjtRegionBg = document.getElementById('bjtRegionBg');
        const regionIndicatorText = document.getElementById('bjtRegionIndicatorText');
        const regionTooltipText = document.getElementById('regionTooltipText');

        const graphCanvas = document.getElementById('transferCurveGraph');
        const ctx = graphCanvas.getContext('2d');

        const VBE_ON_PNP = 0.7; // Volts
        const VCE_SAT_PNP = 0.2; // Volts
        const BETA = 100;

        function formatResistance(value) {
            if (value >= 1000) {
                return (value / 1000).toFixed(value < 10000 ? 2 : 1) + ' kΩ';
            }
            return value + ' Ω';
        }
        
        function formatCurrent(value, unit = 'A') {
            if (Math.abs(value) < 1e-6) return (value * 1e9).toFixed(2) + ' nA'; // nanoAmps
            if (Math.abs(value) < 1e-3) return (value * 1e6).toFixed(2) + ' µA'; // microAmps
            if (Math.abs(value) < 1) return (value * 1e3).toFixed(2) + ' mA';   // milliAmps
            return value.toFixed(2) + ' A';
        }


        function calculateBJTState(vcc, rc, rb, vin) {
            let ib = 0, ic = 0, vce = 0;
            let region = "Cut-off";
            let tooltip = "";

            const ve = vcc;
            const vb = vin;
            const vbe_pnp = ve - vb;

            if (vbe_pnp <= VBE_ON_PNP || rb <= 0) { // Cut-off
                region = "Cut-off";
                ib = 0;
                ic = 0;
                vce = vcc; // For PNP, VE is VCC, VC is effectively 0 via RC if IC=0
                tooltip = `Transistor is OFF. VE - VB (${vbe_pnp.toFixed(2)}V) ≤ VBE_on (${VBE_ON_PNP}V). IB ≈ 0, IC ≈ 0. VCE ≈ VCC.`;
            } else { // Potentially Active or Saturation
                ib = (ve - vb - VBE_ON_PNP) / rb;
                if (ib < 0) ib = 0; // Should not happen if vbe_pnp > VBE_ON_PNP

                let ic_active = BETA * ib;
                let vce_if_active = vcc - (ic_active * rc); // VCE = VE - VC. VC = IC * RC (voltage drop from collector to ground)

                if (vce_if_active <= VCE_SAT_PNP) { // Saturation
                    region = "Saturation";
                    vce = VCE_SAT_PNP;
                    ic = (vcc - VCE_SAT_PNP) / rc;
                    if (ic < 0) ic = 0; // Prevent negative IC if VCC < VCE_SAT
                    // IB remains as calculated, it's what drives it into saturation.
                    // We can also note that the base current required for this IC_sat would be IC_sat / BETA.
                    // The actual IB is larger than this (or equal).
                    tooltip = `Transistor is fully ON. VCE (${vce.toFixed(2)}V) ≤ VCE_sat (${VCE_SAT_PNP}V). IC is limited by collector circuit.`;
                } else { // Active
                    region = "Active";
                    ic = ic_active;
                    vce = vce_if_active;
                    tooltip = `Transistor is ON (Amplifying). IC = β * IB. VCE (${vce.toFixed(2)}V) > VCE_sat (${VCE_SAT_PNP}V).`;
                }
            }
            
            // Ensure VCE is not out of bounds (e.g. VCE > VCC or VCE < 0 for PNP)
            if (vce > vcc) vce = vcc;
            if (vce < 0 && region !== "Saturation") vce = 0; // Should not happen unless VCE_SAT_PNP is negative
            if (vce < VCE_SAT_PNP && region === "Active") { // Should be caught by saturation check
                 vce = VCE_SAT_PNP; region = "Saturation";
                 ic = (vcc - VCE_SAT_PNP) / rc; if (ic < 0) ic = 0;
                 tooltip = `Transistor is fully ON. VCE (${vce.toFixed(2)}V) ≤ VCE_sat (${VCE_SAT_PNP}V). IC is limited by collector circuit.`;
            }


            return { ib, ic, vce, region, tooltip, vbe_pnp };
        }

        function updateSimulation() {
            const vcc = parseFloat(vccSlider.value);
            const rc = parseFloat(rcSlider.value);
            const rb = parseFloat(rbSlider.value);
            const vin = parseFloat(vinSlider.value);

            vccValueSpan.textContent = vcc.toFixed(1) + ' V';
            rcValueSpan.textContent = formatResistance(rc);
            rbValueSpan.textContent = formatResistance(rb);
            vinValueSpan.textContent = vin.toFixed(2) + ' V';

            // Update Vin slider max if VCC changes
            if (vinSlider.max !== vcc.toString()) {
                vinSlider.max = vcc;
                if (vin > vcc) {
                    vinSlider.value = vcc;
                    // vinValueSpan.textContent = vcc.toFixed(2) + ' V'; // update display if changed
                    // No need to recall updateSimulation, it will be called by the event
                }
            }
            
            const state = calculateBJTState(vcc, rc, rb, vin);

            ibValueDisplay.textContent = formatCurrent(state.ib);
            icValueDisplay.textContent = formatCurrent(state.ic);
            vceValueDisplay.textContent = state.vce.toFixed(2) + ' V';

            regionIndicatorText.textContent = 'Region: ' + state.region;
            regionTooltipText.textContent = state.tooltip;

            let regionColor = '#E0E0E0'; // Default/Unknown
            let textColor = '#333';
            let borderColor = '#ccc';

            if (state.region === "Cut-off") {
                regionColor = '#FFCCCC'; // Light Red
                borderColor = '#FF8080';
            } else if (state.region === "Active") {
                regionColor = '#CCFFCC'; // Light Green
                borderColor = '#80FF80';
            } else if (state.region === "Saturation") {
                regionColor = '#CCCCFF'; // Light Blue
                borderColor = '#8080FF';
            }
            
            bjtRegionBg.setAttribute('fill', regionColor);
            regionIndicatorText.style.backgroundColor = regionColor;
            regionIndicatorText.style.borderColor = borderColor;
            regionIndicatorText.style.color = textColor;


            drawGraph(vcc, rc, rb, vin, state.vce);
        }

        function drawGraph(vcc, rc, rb, currentVin, currentVce) {
            const width = graphCanvas.width;
            const height = graphCanvas.height;
            ctx.clearRect(0, 0, width, height);

            // Graph styling
            ctx.strokeStyle = '#ccc'; // Grid lines
            ctx.fillStyle = '#333'; // Text color
            ctx.font = '10px Arial';
            ctx.lineWidth = 1;

            const padding = 30;
            const plotWidth = width - 2 * padding;
            const plotHeight = height - 2 * padding;

            // Draw axes
            ctx.beginPath();
            ctx.moveTo(padding, padding);
            ctx.lineTo(padding, height - padding); // Y-axis (VCE)
            ctx.lineTo(width - padding, height - padding); // X-axis (Vin)
            ctx.stroke();

            // Axis labels
            ctx.save();
            ctx.translate(padding / 2 - 5, height / 2);
            ctx.rotate(-Math.PI / 2);
            ctx.textAlign = 'center';
            ctx.fillText('VCE (Vout)', 0, 0);
            ctx.restore();

            ctx.textAlign = 'center';
            ctx.fillText('Vin (V)', padding + plotWidth / 2, height - padding / 2 + 5);

            // Y-axis (VCE) ticks and labels (0 to VCC)
            const numYTicks = 5;
            for (let i = 0; i <= numYTicks; i++) {
                const val = (vcc / numYTicks) * i;
                const y = height - padding - (val / vcc) * plotHeight;
                ctx.fillText(val.toFixed(1), padding - 10, y + 3);
                ctx.beginPath();
                ctx.moveTo(padding - 5, y);
                ctx.lineTo(padding, y);
                ctx.stroke();
            }
            ctx.fillText('0.0', padding - 10, height - padding + 3); // Origin Y

            // X-axis (Vin) ticks and labels (0 to VCC)
            const numXTicks = 5;
             for (let i = 0; i <= numXTicks; i++) {
                const val = (vcc / numXTicks) * i; // X-axis max is VCC
                const x = padding + (val / vcc) * plotWidth;
                ctx.fillText(val.toFixed(1), x, height - padding + 15);
                ctx.beginPath();
                ctx.moveTo(x, height - padding);
                ctx.lineTo(x, height - padding + 5);
                ctx.stroke();
            }
            ctx.fillText('0.0', padding, height - padding + 15); // Origin X


            // Plot transfer curve
            ctx.beginPath();
            ctx.strokeStyle = 'blue';
            ctx.lineWidth = 2;

            const points = 200;
            for (let i = 0; i <= points; i++) {
                const vin_plot = (vcc / points) * i; // Iterate Vin from 0 to VCC
                const state_plot = calculateBJTState(vcc, rc, rb, vin_plot);
                let vce_plot = state_plot.vce;

                // Clamp VCE to be within 0 and VCC for plotting
                if (vce_plot > vcc) vce_plot = vcc;
                if (vce_plot < 0) vce_plot = 0;
                
                const x = padding + (vin_plot / vcc) * plotWidth;
                const y = height - padding - (vce_plot / vcc) * plotHeight;

                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            ctx.stroke();

            // Mark current operating point
            let clampedCurrentVce = currentVce;
            if (clampedCurrentVce > vcc) clampedCurrentVce = vcc;
            if (clampedCurrentVce < 0) clampedCurrentVce = 0;

            const currentX = padding + (currentVin / vcc) * plotWidth;
            const currentY = height - padding - (clampedCurrentVce / vcc) * plotHeight;
            
            ctx.beginPath();
            ctx.fillStyle = 'red';
            ctx.arc(currentX, currentY, 5, 0, 2 * Math.PI);
            ctx.fill();

            // Indicate region boundaries (approximate)
            // Cutoff to Active: Vin_ca = VCC - VBE_ON_PNP
            const vin_ca = vcc - VBE_ON_PNP;
            if (vin_ca > 0 && vin_ca < vcc) {
                const x_ca = padding + (vin_ca / vcc) * plotWidth;
                ctx.beginPath();
                ctx.strokeStyle = 'rgba(255, 0, 0, 0.5)'; // Reddish for cutoff boundary
                ctx.setLineDash([5, 5]);
                ctx.moveTo(x_ca, padding);
                ctx.lineTo(x_ca, height - padding);
                ctx.stroke();
                ctx.setLineDash([]);
            }

            // Active to Saturation: Vin_as
            // VCE_sat = VCC - BETA * ((VCC - Vin_as - VBE_ON_PNP) / RB) * RC
            // (VCC - Vin_as - VBE_ON_PNP) / RB = (VCC - VCE_SAT_PNP) / (BETA * RC)
            // Vin_as = VCC - VBE_ON_PNP - (RB * (VCC - VCE_SAT_PNP)) / (BETA * RC)
            let vin_as = vcc - VBE_ON_PNP - (rb * (vcc - VCE_SAT_PNP)) / (BETA * rc);
            if (vin_as < 0) vin_as = 0; // If calculated negative, saturation starts at Vin=0 or near
            if (vin_as > 0 && vin_as < vcc && vin_as < vin_ca) { // Ensure it's a valid point
                 const x_as = padding + (vin_as / vcc) * plotWidth;
                 ctx.beginPath();
                 ctx.strokeStyle = 'rgba(0, 0, 255, 0.5)'; // Bluish for saturation boundary
                 ctx.setLineDash([5, 5]);
                 ctx.moveTo(x_as, padding);
                 ctx.lineTo(x_as, height - padding);
                 ctx.stroke();
                 ctx.setLineDash([]);
            }
        }

        // Event Listeners
        vccSlider.addEventListener('input', updateSimulation);
        rcSlider.addEventListener('input', updateSimulation);
        rbSlider.addEventListener('input', updateSimulation);
        vinSlider.addEventListener('input', updateSimulation);
        
        // Ensure canvas has fixed internal size for crisp drawing
        graphCanvas.width = graphCanvas.offsetWidth;
        graphCanvas.height = graphCanvas.offsetHeight;
        window.addEventListener('resize', () => {
            graphCanvas.width = graphCanvas.offsetWidth;
            graphCanvas.height = graphCanvas.offsetHeight;
            updateSimulation(); // Redraw on resize
        });


        // Initial call
        updateSimulation();
    </script>
<script src="js/main.js" defer></script>
<script>
document.addEventListener('DOMContentLoaded', function () {
    // ... existing script ...

    const addButtonPnp = document.getElementById('addComponentButtonPnpRegions');
    const componentSelectPnp = document.getElementById('component-select-pnp');
    const interactiveAreaPnp = document.getElementById('interactive-circuit-drawing-area-pnp');

    if (addButtonPnp && componentSelectPnp && interactiveAreaPnp) {
        addButtonPnp.addEventListener('click', function() {
            const selectedComponent = componentSelectPnp.value;
            if (typeof addComponent === 'function') {
                // Temporarily modify the target for this specific page if needed
                const originalTargetId = 'interactive-circuit-drawing-area'; // ID used in main.js
                const targetElement = document.getElementById(originalTargetId);
                const pnpTargetElement = document.getElementById('interactive-circuit-drawing-area-pnp');
                
                // Swap IDs if they are different, call function, then swap back
                // This is a workaround if addComponent is hardcoded to one ID
                let idSwapped = false;
                if (targetElement && pnpTargetElement && targetElement.id !== pnpTargetElement.id) {
                    // To ensure addComponent targets the correct div on this page,
                    // we can temporarily give the PNP-specific div the generic ID.
                    // This assumes addComponent in main.js looks for 'interactive-circuit-drawing-area'.
                    pnpTargetElement.id = originalTargetId;
                    idSwapped = true;
                }

                addComponent(selectedComponent);

                if (idSwapped && pnpTargetElement) {
                    pnpTargetElement.id = 'interactive-circuit-drawing-area-pnp'; // Restore original ID
                }

            } else {
                console.error('addComponent function is not defined. Make sure js/main.js is loaded.');
            }
        });
    }
    // ... any other script for this page ...
});
</script>
</body>
</html>

