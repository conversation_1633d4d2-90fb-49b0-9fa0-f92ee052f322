<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="600" height="450" viewBox="0 0 600 450">
  <!-- Background -->
  <rect width="600" height="450" fill="#ffffff"/>
  
  <!-- Title -->
  <text x="300" y="30" font-family="Arial" font-size="20" text-anchor="middle" fill="#333333">Differential Amplifier Circuit</text>
  
  <!-- Power Supply (VCC) -->
  <text x="300" y="70" font-family="Arial" font-size="14" text-anchor="middle" fill="#333333">VCC (+12V)</text>
  <line x1="300" y1="80" x2="300" y2="100" stroke="#000000" stroke-width="2"/>
  
  <!-- Collector Resistors -->
  <!-- RC1 -->
  <line x1="200" y1="100" x2="200" y2="120" stroke="#000000" stroke-width="2"/>
  <rect x="190" y="120" width="20" height="40" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <text x="170" y="140" font-family="Arial" font-size="12" fill="#333333">RC1</text>
  <text x="170" y="155" font-family="Arial" font-size="10" fill="#666666">1kΩ</text>
  <line x1="200" y1="160" x2="200" y2="180" stroke="#000000" stroke-width="2"/>
  
  <!-- RC2 -->
  <line x1="400" y1="100" x2="400" y2="120" stroke="#000000" stroke-width="2"/>
  <rect x="390" y="120" width="20" height="40" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <text x="420" y="140" font-family="Arial" font-size="12" fill="#333333">RC2</text>
  <text x="420" y="155" font-family="Arial" font-size="10" fill="#666666">1kΩ</text>
  <line x1="400" y1="160" x2="400" y2="180" stroke="#000000" stroke-width="2"/>
  
  <!-- Connect VCC to RC1 and RC2 -->
  <line x1="200" y1="100" x2="400" y2="100" stroke="#000000" stroke-width="2"/>
  <line x1="300" y1="100" x2="300" y2="80" stroke="#000000" stroke-width="2"/>
  
  <!-- Transistors -->
  <!-- Q1 -->
  <!-- Base -->
  <line x1="150" y1="200" x2="180" y2="200" stroke="#000000" stroke-width="2"/>
  <!-- Collector -->
  <line x1="200" y1="180" x2="200" y2="190" stroke="#000000" stroke-width="2"/>
  <!-- Emitter -->
  <line x1="200" y1="210" x2="200" y2="230" stroke="#000000" stroke-width="2"/>
  <!-- Transistor Symbol -->
  <circle cx="190" cy="200" r="15" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <line x1="180" y1="200" x2="190" y2="190" stroke="#000000" stroke-width="2"/>
  <line x1="190" y1="190" x2="190" y2="210" stroke="#000000" stroke-width="2"/>
  <line x1="190" y1="210" x2="200" y2="220" stroke="#000000" stroke-width="2"/>
  <line x1="190" y1="190" x2="200" y2="180" stroke="#000000" stroke-width="2"/>
  <text x="170" y="200" font-family="Arial" font-size="12" fill="#333333">Q1</text>
  
  <!-- Q2 -->
  <!-- Base -->
  <line x1="450" y1="200" x2="420" y2="200" stroke="#000000" stroke-width="2"/>
  <!-- Collector -->
  <line x1="400" y1="180" x2="400" y2="190" stroke="#000000" stroke-width="2"/>
  <!-- Emitter -->
  <line x1="400" y1="210" x2="400" y2="230" stroke="#000000" stroke-width="2"/>
  <!-- Transistor Symbol -->
  <circle cx="410" cy="200" r="15" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <line x1="420" y1="200" x2="410" y2="190" stroke="#000000" stroke-width="2"/>
  <line x1="410" y1="190" x2="410" y2="210" stroke="#000000" stroke-width="2"/>
  <line x1="410" y1="210" x2="400" y2="220" stroke="#000000" stroke-width="2"/>
  <line x1="410" y1="190" x2="400" y2="180" stroke="#000000" stroke-width="2"/>
  <text x="430" y="200" font-family="Arial" font-size="12" fill="#333333">Q2</text>
  
  <!-- Connect Emitters -->
  <line x1="200" y1="230" x2="400" y2="230" stroke="#000000" stroke-width="2"/>
  <line x1="300" y1="230" x2="300" y2="250" stroke="#000000" stroke-width="2"/>
  
  <!-- Current Source (RE) -->
  <rect x="290" y="250" width="20" height="40" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <text x="320" y="270" font-family="Arial" font-size="12" fill="#333333">RE</text>
  <text x="320" y="285" font-family="Arial" font-size="10" fill="#666666">2kΩ</text>
  <line x1="300" y1="290" x2="300" y2="310" stroke="#000000" stroke-width="2"/>
  
  <!-- Ground -->
  <line x1="280" y1="310" x2="320" y2="310" stroke="#000000" stroke-width="2"/>
  <line x1="285" y1="315" x2="315" y2="315" stroke="#000000" stroke-width="2"/>
  <line x1="290" y1="320" x2="310" y2="320" stroke="#000000" stroke-width="2"/>
  <line x1="295" y1="325" x2="305" y2="325" stroke="#000000" stroke-width="2"/>
  
  <!-- Input 1 -->
  <line x1="100" y1="200" x2="120" y2="200" stroke="#000000" stroke-width="2"/>
  <line x1="120" y1="180" x2="120" y2="220" stroke="#000000" stroke-width="2"/>
  <line x1="130" y1="180" x2="130" y2="220" stroke="#000000" stroke-width="2"/>
  <line x1="130" y1="200" x2="150" y2="200" stroke="#000000" stroke-width="2"/>
  <text x="125" y="170" font-family="Arial" font-size="12" fill="#333333">C1</text>
  
  <!-- Input 2 -->
  <line x1="500" y1="200" x2="480" y2="200" stroke="#000000" stroke-width="2"/>
  <line x1="480" y1="180" x2="480" y2="220" stroke="#000000" stroke-width="2"/>
  <line x1="470" y1="180" x2="470" y2="220" stroke="#000000" stroke-width="2"/>
  <line x1="470" y1="200" x2="450" y2="200" stroke="#000000" stroke-width="2"/>
  <text x="475" y="170" font-family="Arial" font-size="12" fill="#333333">C2</text>
  
  <!-- Output -->
  <line x1="200" y1="180" x2="200" y2="150" stroke="#000000" stroke-width="2"/>
  <line x1="400" y1="180" x2="400" y2="150" stroke="#000000" stroke-width="2"/>
  <line x1="200" y1="150" x2="180" y2="150" stroke="#000000" stroke-width="2"/>
  <line x1="400" y1="150" x2="420" y2="150" stroke="#000000" stroke-width="2"/>
  <text x="170" y="140" font-family="Arial" font-size="12" fill="#333333">Out1</text>
  <text x="420" y="140" font-family="Arial" font-size="12" fill="#333333">Out2</text>
  
  <!-- Differential Output -->
  <line x1="180" y1="150" x2="180" y2="350" stroke="#000000" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="420" y1="150" x2="420" y2="350" stroke="#000000" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="180" y1="350" x2="280" y2="350" stroke="#000000" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="420" y1="350" x2="320" y2="350" stroke="#000000" stroke-width="2" stroke-dasharray="5,5"/>
  <rect x="280" y="340" width="40" height="20" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <text x="300" y="355" font-family="Arial" font-size="12" text-anchor="middle" fill="#333333">Diff</text>
  <line x1="300" y1="360" x2="300" y2="380" stroke="#000000" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="300" y="395" font-family="Arial" font-size="12" text-anchor="middle" fill="#333333">Vout = Out1 - Out2</text>
  
  <!-- Input and Output Labels -->
  <text x="100" y="180" font-family="Arial" font-size="12" fill="#333333">Vin1</text>
  <text x="500" y="180" font-family="Arial" font-size="12" fill="#333333">Vin2</text>
  
  <!-- Circuit Characteristics -->
  <text x="300" y="420" font-family="Arial" font-size="14" text-anchor="middle" fill="#333333">Characteristics: High CMRR, Low Drift, High Input Impedance</text>
</svg>
