@echo off
echo Electronic Circuits Lab - Build Script
echo ====================================
echo.

echo Step 1: Installing dependencies...
call npm install
if %ERRORLEVEL% neq 0 (
    echo Error installing dependencies!
    pause
    exit /b 1
)
echo Dependencies installed successfully.
echo.

echo Step 2: Copying web application files...
call copy-files.bat
echo.

echo Step 3: Checking for icon file...
if not exist build\icon.ico (
    echo Warning: icon.ico not found in build directory!
    echo Please create an icon file before building the executable.
    echo You can use the convert-icon.html tool to help with this.
    echo.
    set /p continue=Do you want to continue without an icon? (y/n): 
    if /i "%continue%" neq "y" (
        echo Build aborted.
        pause
        exit /b 1
    )
)

echo Step 4: Building the executable...
call npm run dist
if %ERRORLEVEL% neq 0 (
    echo Error building executable!
    pause
    exit /b 1
)
echo.

echo Build completed successfully!
echo The executable can be found in the dist directory.
echo.

pause
