<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NPN Transistor Interactive Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #333;
        }

        .app-container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            width: 90%;
            max-width: 600px;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-top: 0;
        }

        .transistor-area {
            text-align: center;
            margin-bottom: 20px;
        }

        #transistor-diagram {
            width: 100%;
            max-width: 200px; /* Increased max-width for better visibility */
            height: auto;
            display: block;
            margin: 0 auto 10px auto; /* Center SVG and add margin */
        }
        
        .diagram-labels p {
            font-size: 0.9em;
            color: #555;
            margin-top: 0;
        }

        .controls, .outputs, .explanation {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border: 1px solid #eee;
            border-radius: 5px;
        }

        .controls label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }

        .controls input[type="range"] {
            width: 100%;
            cursor: pointer;
        }
        
        .controls p {
            font-size: 0.9em;
            color: #555;
            margin-top: 5px;
        }

        .outputs p {
            margin: 8px 0;
            font-size: 1em;
        }

        .outputs span {
            font-weight: bold;
            color: #007bff;
        }
        
        #transistor-state {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.9em;
        }

        .state-cut-off { background-color: #ffebee; color: #c62828; }
        .state-active { background-color: #e8f5e9; color: #2e7d32; }
        .state-saturation { background-color: #e3f2fd; color: #1565c0; }


        .explanation h3 {
            margin-top: 0;
            color: #333;
        }
        .explanation ul {
            padding-left: 20px;
            font-size: 0.95em;
            line-height: 1.6;
        }
        .explanation li {
            margin-bottom: 8px;
        }

        /* Ensure all elements use border-box sizing */
        * {
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <h1>NPN Transistor Operation</h1>
        
        <div class="transistor-area">
            <svg id="transistor-diagram" viewBox="0 0 150 200">
                <!-- V_CC and R_C (conceptual) -->
                <text x="75" y="15" font-size="10" text-anchor="middle" fill="#333">V_CC (+5V)</text>
                <line x1="75" y1="20" x2="75" y2="30" stroke="#333" stroke-width="1"/>
                <rect x="65" y="30" width="20" height="10" stroke="#333" fill="#f0f0f0" stroke-width="1"/>
                <text x="90" y="38" font-size="9">R_C</text>
                
                <!-- Collector Wire -->
                <line x1="75" y1="40" x2="75" y2="70" stroke="#333" stroke-width="1"/>
                <text x="82" y="65" font-size="12" fill="#333">C</text>

                <!-- Base Wire -->
                <line x1="15" y1="100" x2="45" y2="100" stroke="#333" stroke-width="1"/>
                <text x="0" y="103" font-size="12" fill="#333">B</text>

                <!-- Emitter Wire -->
                <line x1="75" y1="130" x2="75" y2="170" stroke="#333" stroke-width="1"/>
                <text x="82" y="145" font-size="12" fill="#333">E</text>
                <line x1="75" y1="170" x2="75" y2="180" stroke="#333" stroke-width="1"/> <!-- To GND symbol -->
                <line x1="65" y1="180" x2="85" y2="180" stroke="#333" stroke-width="1.5"/>
                <line x1="68" y1="183" x2="82" y2="183" stroke="#333" stroke-width="1"/>
                <line x1="71" y1="186" x2="79" y2="186" stroke="#333" stroke-width="0.7"/>

                <!-- Current Flow Path (Collector to Emitter) - drawn under symbol parts -->
                <path id="current-path-ce" d="M 75 70 L 55 80 L 55 120 L 75 130" 
                      stroke="#007bff" stroke-opacity="0" stroke-width="1" fill="none" stroke-linecap="round"/>

                <!-- Transistor Symbol (standard non-circle) -->
                <!-- Vertical bar (Base plate) -->
                <line x1="45" y1="80" x2="45" y2="120" stroke="black" stroke-width="2.5"/>
                <!-- Base connection to bar -->
                <line x1="45" y1="100" x2="45" y2="100" stroke="black" stroke-width="2.5"/> <!-- Wire connects directly to bar -->

                <!-- Collector connection to bar top -->
                <line x1="75" y1="70" x2="45" y2="80" stroke="black" stroke-width="2"/>
                <!-- Emitter connection to bar bottom -->
                <line x1="45" y1="120" x2="75" y2="130" stroke="black" stroke-width="2"/>
                
                <!-- Emitter Arrow (NPN: points away from base, along emitter line) -->
                <!-- Line: (45,120) to (75,130). Arrow tip at (75,130) -->
                <!-- P_end = (75,130). Angle of line: atan((130-120)/(75-45)) = atan(10/30) = atan(1/3) = 18.43 deg -->
                <!-- P_base_arrow = (75 - 6*cos(18.43), 130 - 6*sin(18.43)) = (75 - 5.69, 130 - 1.90) = (69.31, 128.10) -->
                <!-- P_wing1 = (69.31 + 3*sin(18.43), 128.10 - 3*cos(18.43)) = (69.31+0.95, 128.10-2.85) = (70.26, 125.25) -->
                <!-- P_wing2 = (69.31 - 3*sin(18.43), 128.10 + 3*cos(18.43)) = (69.31-0.95, 128.10+2.85) = (68.36, 130.95) -->
                <polygon points="75,130 70.26,125.25 68.36,130.95" fill="black"/>
            </svg>
            <div class="diagram-labels">
                <p>Conceptual circuit: V_CC = 5V, R_C = 100&Omega;</p>
            </div>
        </div>

        <div class="controls">
            <label for="vb-slider">Base Voltage (V_B): <span id="vb-value">0.00</span> V</label>
            <input type="range" id="vb-slider" min="0" max="5" step="0.01" value="0">
            <p>NPN B-E Threshold (V_BE_ON): 0.7V</p>
        </div>

        <div class="outputs">
            <p>Collector-Emitter Current (I_CE): <span id="ice-value">0.0 mA</span></p>
            <p>Collector-Emitter Voltage (V_CE): <span id="vce-value">5.000 V</span></p>
            <p>Transistor State: <span id="transistor-state" class="state-cut-off">CUT-OFF</span></p>
        </div>

        <div class="explanation">
            <h3>How it works:</h3>
            <p>This NPN Bipolar Junction Transistor (BJT) model illustrates its core function as a current-controlled device, simplified here to be voltage-controlled at the base for learning purposes.</p>
            <ul>
                <li><strong>CUT-OFF:</strong> When Base Voltage (V_B) is below the threshold (~0.7V for silicon NPN), the base-emitter junction is not forward-biased enough. The transistor is effectively OFF. No significant current flows from Collector (C) to Emitter (E). I_CE is (near) zero, and V_CE is high (approximately V_CC).</li>
                <li><strong>ACTIVE REGION:</strong> When V_B rises above ~0.7V, the base-emitter junction becomes forward-biased. A small current would flow into the base (not explicitly modeled here). This allows a much larger current (I_CE) to flow from Collector to Emitter. In this region, I_CE is roughly proportional to the effective base drive (V_B - 0.7V). As I_CE increases, the voltage drop across the collector resistor (R_C) increases, so V_CE decreases (V_CE = V_CC - I_CE * R_C).</li>
                <li><strong>SATURATION:</strong> If V_B (and thus the base drive) increases further, I_CE continues to rise. However, I_CE cannot increase indefinitely. It becomes limited by the external circuit (V_CC and R_C). When V_CE drops to a minimum low value (V_CE_SAT, typically ~0.2V for a saturated BJT), the transistor is considered "fully ON" or saturated. Further increases in V_B will not significantly increase I_CE or further decrease V_CE.</li>
            </ul>
        </div>
    </div>

    <script>
        const V_BE_ON = 0.7; // Volts
        const V_CC = 5.0;    // Volts (Collector supply)
        const R_C = 100;     // Ohms (Collector resistor)
        const V_CE_SAT = 0.2; // Volts (Saturation V_CE)

        // Calculated maximum current at saturation based on the circuit
        const I_CE_MAX_SAT_CIRCUIT = (V_CC - V_CE_SAT) / R_C; // (5-0.2)/100 = 0.048 A = 48 mA

        // Gain factor: I_CE = GAIN_FACTOR * (V_B - V_BE_ON) in active region
        // Chosen so saturation is reached around V_B = 2.0V
        // 0.048 A = GAIN_FACTOR * (2.0V - 0.7V) => GAIN_FACTOR = 0.048 / 1.3
        const GAIN_FACTOR = 0.048 / 1.3; // Approx 0.0369 A/V

        // DOM Elements
        const vbSlider = document.getElementById('vb-slider');
        const vbValueDisplay = document.getElementById('vb-value');
        const iceValueDisplay = document.getElementById('ice-value');
        const vceValueDisplay = document.getElementById('vce-value');
        const transistorStateDisplay = document.getElementById('transistor-state');
        const currentPathCE = document.getElementById('current-path-ce');

        const MAX_STROKE_WIDTH_CURRENT = 8; // px for visualization
        const MIN_STROKE_WIDTH_CURRENT = 0; // px for visualization when current is zero

        function updateTransistorState() {
            const V_B = parseFloat(vbSlider.value);
            vbValueDisplay.textContent = V_B.toFixed(2);

            let I_CE = 0;
            let V_CE = V_CC;
            let state = "CUT-OFF";
            let stateClass = "state-cut-off";

            if (V_B < V_BE_ON) {
                // Cut-off
                I_CE = 0;
                V_CE = V_CC;
                state = "CUT-OFF";
                stateClass = "state-cut-off";
            } else {
                // Potentially Active or Saturated
                // Calculate I_CE as if it's in the active region
                let I_CE_active_calc = GAIN_FACTOR * (V_B - V_BE_ON);
                if (I_CE_active_calc < 0) I_CE_active_calc = 0; // Ensure non-negative

                // If this calculated I_CE would cause V_CE to drop below V_CE_SAT,
                // then the transistor is saturated and I_CE is limited by the circuit.
                // V_CE_potential = V_CC - I_CE_active_calc * R_C
                // Saturation if V_CE_potential <= V_CE_SAT
                // OR if I_CE_active_calc >= I_CE_MAX_SAT_CIRCUIT
                
                if (I_CE_active_calc >= I_CE_MAX_SAT_CIRCUIT) {
                    // Saturation
                    I_CE = I_CE_MAX_SAT_CIRCUIT;
                    V_CE = V_CE_SAT;
                    state = "SATURATION";
                    stateClass = "state-saturation";
                } else {
                    // Active
                    I_CE = I_CE_active_calc;
                    V_CE = V_CC - I_CE * R_C;
                    // Safety check, V_CE should not be below V_CE_SAT in active mode.
                    // If it is, it implies it should be in saturation, but the primary check above handles this.
                    // This can also prevent V_CE from going slightly below V_CE_SAT due to float precision.
                    if (V_CE < V_CE_SAT) { 
                        V_CE = V_CE_SAT; // Should ideally be caught by saturation logic
                        I_CE = I_CE_MAX_SAT_CIRCUIT; // Recalculate I_CE based on V_CE_SAT
                        state = "SATURATION"; // Correct state if this happens
                        stateClass = "state-saturation";
                    } else {
                        state = "ACTIVE";
                        stateClass = "state-active";
                    }
                }
            }

            iceValueDisplay.textContent = (I_CE * 1000).toFixed(1) + " mA";
            vceValueDisplay.textContent = V_CE.toFixed(3) + " V";
            transistorStateDisplay.textContent = state;
            transistorStateDisplay.className = ''; // Clear existing classes
            transistorStateDisplay.classList.add(stateClass);


            // Update visual representation of current
            if (I_CE <= 0) {
                currentPathCE.style.strokeWidth = "0";
                currentPathCE.style.strokeOpacity = "0";
            } else {
                // Normalize current: I_CE / I_CE_MAX_SAT_CIRCUIT (0 to 1)
                const normalizedCurrent = Math.min(I_CE / I_CE_MAX_SAT_CIRCUIT, 1.0);
                // Linearly map normalized current to stroke width
                const strokeWidth = MIN_STROKE_WIDTH_CURRENT + normalizedCurrent * (MAX_STROKE_WIDTH_CURRENT - MIN_STROKE_WIDTH_CURRENT);
                
                currentPathCE.style.strokeWidth = Math.max(strokeWidth, 0.5).toFixed(2) + "px"; // ensure a thin line is visible even for small currents
                currentPathCE.style.strokeOpacity = (0.3 + normalizedCurrent * 0.7).toFixed(2); // Opacity from 0.3 to 1.0
            }
        }

        // Event Listener
        vbSlider.addEventListener('input', updateTransistorState);

        // Initial call to set values based on default slider position
        updateTransistorState();
    </script>
</body>
</html>
