<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BJT Transistor Circuit Analyzer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
        }

        .container {
            max-width: 700px;
            margin: auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
        }

        h1, h2, h3 {
            color: #0056b3; /* A nice blue */
            margin-top: 0;
        }
        h1 { text-align: center; margin-bottom: 20px; }
        h2 { border-bottom: 1px solid #eee; padding-bottom: 10px; margin-top: 30px;}
        h3 { margin-top: 20px; }

        .main-layout {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .circuit-column {
            flex: 1 1 300px; /* Flex basis 300px, can grow and shrink */
            min-width: 280px;
        }

        .controls-column {
            flex: 1 1 300px;
            min-width: 280px;
        }
        
        .circuit-area svg {
            width: 100%;
            height: auto;
            display: block;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #fff; /* White background for the SVG */
        }

        .controls-area, .results-area, .parameters-info {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .controls-area label {
            display: inline-block; /* Changed from block for better alignment */
            margin-bottom: 5px;
            font-weight: bold;
        }

        .controls-area input[type="number"] {
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            width: 100px; /* Increased width */
            margin-right: 5px;
        }

        .controls-area button {
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
        }

        .controls-area button:hover {
            background-color: #0056b3;
        }

        .results-area p, .parameters-info p {
            margin: 8px 0;
        }

        .results-area span, .parameters-info span {
            font-weight: bold;
            color: #d9534f; /* A reddish color for values */
        }
        
        .parameters-info span {
            color: #5cb85c; /* Green for fixed parameters */
        }

        #modeDisplay {
            font-size: 1.2em;
        }

        /* SVG text styling */
        .svg-label {
            font-size: 10px;
            font-family: Arial, sans-serif;
        }
        .svg-component-label {
            font-size: 9px;
            font-family: Arial, sans-serif;
            fill: #333;
        }

        /* BJT specific styles for highlighting */
        .bjt-line {
            stroke: black;
            stroke-width: 2;
            transition: stroke 0.3s ease;
        }
        .bjt-arrow {
            fill: black;
            transition: fill 0.3s ease;
        }
        .ground-line {
            stroke: black;
            stroke-width:1.5;
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>BJT Common-Emitter Circuit Analyzer</h1>

        <div class="main-layout">
            <div class="circuit-column">
                <div class="circuit-area">
                    <svg id="circuitSvg" viewBox="0 0 300 260" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <!-- VCC -->
                        <text x="150" y="20" text-anchor="middle" class="svg-label">VCC</text>
                        <line x1="150" y1="25" x2="150" y2="40" stroke="black" stroke-width="1.5"/>
                        
                        <!-- RC -->
                        <rect x="140" y="40" width="20" height="30" fill="none" stroke="black" stroke-width="1.5"/>
                        <text x="170" y="55" class="svg-component-label">RC</text>
                        <line x1="150" y1="70" x2="150" y2="85" stroke="black" stroke-width="1.5"/> <!-- Wire to Collector -->

                        <!-- BJT NPN Symbol -->
                        <g id="bjtSymbol" transform="translate(150, 105)"> <!-- Centered BJT around this point -->
                            <!-- Vertical bar (Bulk) -->
                            <line x1="0" y1="-15" x2="0" y2="15" class="bjt-line"/>
                            <!-- Collector arm -->
                            <line x1="0" y1="-15" x2="0" y2="-20" class="bjt-line"/> <!-- Connects to wire from RC -->
                            <!-- Emitter arm -->
                            <line x1="0" y1="15" x2="0" y2="20" class="bjt-line"/> <!-- Connects to wire to Ground -->
                            <!-- Emitter arrow -->
                            <polygon points="0,15 5,10 -5,10" class="bjt-arrow"/> <!-- Arrow pointing out -->
                            <!-- Base arm -->
                            <line x1="-30" y1="0" x2="0" y2="0" class="bjt-line"/> <!-- Connects to wire from RB -->
                            <text x="5" y="0" class="svg-component-label" font-size="8px">B</text>
                            <text x="5" y="-15" class="svg-component-label" font-size="8px">C</text>
                            <text x="5" y="18" class="svg-component-label" font-size="8px">E</text>
                        </g>
                        
                        <!-- Wire from BJT Emitter to Ground -->
                        <line x1="150" y1="125" x2="150" y2="140" stroke="black" stroke-width="1.5"/>
                        
                        <!-- Ground Symbol -->
                        <line x1="135" y1="140" x2="165" y2="140" class="ground-line"/>
                        <line x1="140" y1="145" x2="160" y2="145" class="ground-line"/>
                        <line x1="145" y1="150" x2="155" y2="150" class="ground-line"/>

                        <!-- Wire from Base -->
                        <line x1="120" y1="105" x2="80" y2="105" stroke="black" stroke-width="1.5"/>
                        
                        <!-- RB -->
                        <rect x="50" y="95" width="30" height="20" fill="none" stroke="black" stroke-width="1.5"/>
                        <text x="35" y="102" class="svg-component-label" text-anchor="end">RB</text>
                        <line x1="50" y1="105" x2="30" y2="105" stroke="black" stroke-width="1.5"/> <!-- Wire to VB -->

                        <!-- VB -->
                        <text x="15" y="100" text-anchor="middle" class="svg-label">VB</text>
                        <circle cx="15" cy="105" r="3" stroke="black" fill="white" stroke-width="1"/> <!-- VB terminal -->
                        <line x1="18" y1="105" x2="30" y2="105" stroke="black" stroke-width="1.5"/>

                        <!-- Animation Paths (invisible by default) -->
                        <!-- IC Path: VCC -> RC -> Collector -> Emitter -> Ground -->
                        <path id="icPathDef" d="M150,25 L150,40 L150,70 L150,85 L150,90 L150,125 L150,140" stroke="none" fill="none"/>
                        <!-- IB Path: VB -> RB -> Base -> Emitter -> Ground -->
                        <path id="ibPathDef" d="M20,105 L50,105 L80,105 L120,105 L150,105 L150,125 L150,140" stroke="none" fill="none"/>
                        <!-- Note: The paths are simplified. For BJT internal current, it would be C->E and B->E.
                             The paths here go to the center of BJT then to emitter wire.
                             Corrected paths for animation:
                             IC: VCC -> RC -> C -> E -> GND
                             IB: VB -> RB -> B -> E -> GND
                        -->
                        <path id="icPathDefCorrected" d="M150,25 V85 M150,90 L150,125 V140" stroke="none" fill="none"/>
                        <path id="ibPathDefCorrected" d="M20,105 H120 M120,105 L150,105 L150,125 V140" stroke="none" fill="none"/>


                        <!-- Animated Dots (initially hidden) -->
                        <circle id="icDot" r="2.5" fill="rgba(255,0,0,0.8)" style="visibility:hidden;">
                            <animateMotion dur="1.5s" repeatCount="indefinite" path="M150,25 V85 L150,90 V100 L150,125 V140">
                                <!-- Path: VCC -> RC -> Collector -> Emitter junction -> Emitter wire -> Ground -->
                            </animateMotion>
                        </circle>
                        <circle id="ibDot" r="2.5" fill="rgba(0,0,255,0.8)" style="visibility:hidden;">
                             <animateMotion dur="2s" repeatCount="indefinite" path="M20,105 H50 H80 H120 L150,105 L150,120 L150,125 V140">
                                <!-- Path: VB -> RB -> Base -> Emitter junction -> Emitter wire -> Ground -->
                            </animateMotion>
                        </circle>
                    </svg>
                </div>
            </div>

            <div class="controls-column">
                <div class="controls-area">
                    <h3>Controls</h3>
                    <label for="vbInput">Input Voltage (V<sub>B</sub>):</label>
                    <input type="number" id="vbInput" step="0.1" value="0.0"> Volts
                    <br><br>
                    <button id="calculateButton">Calculate</button>
                </div>

                <div class="results-area">
                    <h3>Results</h3>
                    <p>Mode of Operation: <strong id="modeDisplay">-</strong></p>
                    <p>Base Current (I<sub>B</sub>): <span id="ibDisplay">-</span></p>
                    <p>Collector Current (I<sub>C</sub>): <span id="icDisplay">-</span></p>
                    <p>Collector-Emitter Voltage (V<sub>CE</sub>): <span id="vceDisplay">-</span></p>
                </div>
            </div>
        </div>
        
        <div class="parameters-info">
            <h3>Fixed Circuit Parameters:</h3>
            <p>V<sub>CC</sub>: <span id="vccVal"></span> V</p>
            <p>R<sub>C</sub>: <span id="rcVal"></span> &Omega;</p>
            <p>R<sub>B</sub>: <span id="rbVal"></span> &Omega;</p>
            <p>Beta (&beta;): <span id="betaVal"></span></p>
            <p>V<sub>BE (on)</sub>: <span id="vbeOnVal"></span> V (Forward Bias Voltage)</p>
            <p>V<sub>CE (sat)</sub>: <span id="vceSatVal"></span> V (Saturation Voltage)</p>
        </div>

    </div>

    <script>
        // Constants
        const VCC = 10.0;       // Volts
        const RC_VAL = 1000.0;  // Ohms (1kΩ)
        const RB_VAL = 10000.0; // Ohms (10kΩ)
        const BETA = 100.0;
        const VBE_ON = 0.7;     // Volts
        const VCE_SAT = 0.2;    // Volts

        // DOM Elements
        let vbInput, modeDisplay, ibDisplay, vceDisplay, icDisplay;
        let circuitSvg, bjtSymbolGroup, bjtLines, bjtArrow;
        let ibDot, icDot;

        window.onload = function() {
            // Initialize DOM element references
            vbInput = document.getElementById('vbInput');
            modeDisplay = document.getElementById('modeDisplay');
            ibDisplay = document.getElementById('ibDisplay');
            icDisplay = document.getElementById('icDisplay');
            vceDisplay = document.getElementById('vceDisplay');

            circuitSvg = document.getElementById('circuitSvg');
            bjtSymbolGroup = document.getElementById('bjtSymbol');
            bjtLines = bjtSymbolGroup.querySelectorAll('.bjt-line');
            bjtArrow = bjtSymbolGroup.querySelector('.bjt-arrow');
            
            ibDot = document.getElementById('ibDot');
            icDot = document.getElementById('icDot');

            // Display fixed parameters
            document.getElementById('vccVal').textContent = VCC.toFixed(1);
            document.getElementById('rcVal').textContent = RC_VAL.toFixed(0);
            document.getElementById('rbVal').textContent = RB_VAL.toFixed(0);
            document.getElementById('betaVal').textContent = BETA.toFixed(0);
            document.getElementById('vbeOnVal').textContent = VBE_ON.toFixed(1);
            document.getElementById('vceSatVal').textContent = VCE_SAT.toFixed(1);

            // Initial calculation
            vbInput.value = "0.0"; // Set initial VB
            calculateAndDisplay();

            // Add event listener for input changes and button click
            vbInput.addEventListener('input', calculateAndDisplay);
            document.getElementById('calculateButton').addEventListener('click', calculateAndDisplay);
        };

        function calculateAndDisplay() {
            const VB = parseFloat(vbInput.value);
            if (isNaN(VB)) {
                modeDisplay.textContent = "Invalid VB Input";
                ibDisplay.textContent = "-";
                icDisplay.textContent = "-";
                vceDisplay.textContent = "-";
                resetVisuals();
                return;
            }

            let IB_calc = 0;
            let IC_calc = 0;
            let VCE_calc = 0;
            let mode = "";

            if (VB <= VBE_ON) {
                IB_calc = 0;
            } else {
                IB_calc = (VB - VBE_ON) / RB_VAL;
            }
            IB_calc = Math.max(0, IB_calc); // Ensure IB is not negative

            if (IB_calc <= 0) {
                mode = "Cut-off";
                IC_calc = 0;
                VCE_calc = VCC; 
            } else {
                let IC_active = BETA * IB_calc;
                let VCE_active = VCC - (IC_active * RC_VAL);

                if (VCE_active <= VCE_SAT) {
                    mode = "Saturation";
                    VCE_calc = VCE_SAT;
                    // In saturation, IC is limited by the external circuit
                    IC_calc = (VCC - VCE_SAT) / RC_VAL;
                    if (RC_VAL === 0) IC_calc = Infinity; // Should not happen with fixed RC > 0
                } else {
                    mode = "Active";
                    VCE_calc = VCE_active;
                    IC_calc = IC_active;
                }
            }

            // Display results
            modeDisplay.textContent = mode;
            ibDisplay.textContent = (IB_calc * 1000).toFixed(3) + " mA"; // IB in mA
            icDisplay.textContent = (IC_calc * 1000).toFixed(3) + " mA"; // IC in mA
            vceDisplay.textContent = VCE_calc.toFixed(2) + " V";

            updateVisuals(mode);
        }

        function resetVisuals() {
            circuitSvg.style.opacity = '1';
            
            bjtLines.forEach(line => line.style.stroke = 'black');
            bjtArrow.style.fill = 'black';

            if (ibDot) ibDot.style.visibility = 'hidden';
            if (icDot) icDot.style.visibility = 'hidden';
        }

        function updateVisuals(mode) {
            resetVisuals(); 

            switch (mode) {
                case "Cut-off":
                    circuitSvg.style.opacity = '0.4';
                    break;
                case "Saturation":
                    bjtLines.forEach(line => line.style.stroke = 'green');
                    bjtArrow.style.fill = 'green';
                    // Optionally, make current dots visible but static or very slow for saturation
                    // For simplicity, keeping them hidden unless active.
                    break;
                case "Active":
                    if (ibDot) ibDot.style.visibility = 'visible';
                    if (icDot) icDot.style.visibility = 'visible';
                    break;
            }
        }
    </script>
</body>
</html>
