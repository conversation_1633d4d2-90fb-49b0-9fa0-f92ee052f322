/* Circuit Templates Styles */

/* Templates Container */
.templates-container {
    margin: 30px 0;
}

/* Templates Grid */
.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

/* Template Card */
.template-card {
    background-color: var(--card-bg);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px var(--shadow-color);
    transition: all 0.3s ease;
    position: relative;
}

.template-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-hover-shadow);
}

/* Template Badge */
.template-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--gradient-accent);
    color: var(--dark-color);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

/* Template Preview */
.template-preview {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.template-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.template-card:hover .template-preview img {
    transform: scale(1.05);
}

/* Template Overlay */
.template-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.template-card:hover .template-overlay {
    opacity: 1;
}

.btn-overlay {
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--primary-color);
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-overlay:hover {
    background-color: white;
    transform: translateY(-2px);
}

/* Template Info */
.template-info {
    padding: 20px;
}

.template-info h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1.2rem;
    color: var(--primary-color);
}

.template-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.template-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
    background-color: var(--light-color);
    padding: 5px 10px;
    border-radius: 15px;
    color: var(--primary-color);
}

.template-meta span i {
    color: var(--secondary-color);
}

.template-info p {
    margin-bottom: 15px;
    font-size: 0.95rem;
    color: var(--text-color);
    opacity: 0.9;
    line-height: 1.5;
}

.template-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Template Categories */
.templates-categories {
    background-color: var(--light-color);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px var(--shadow-color);
}

.templates-categories h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: var(--primary-color);
    font-size: 1.3rem;
    text-align: center;
    position: relative;
}

.templates-categories h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    right: 50%;
    transform: translateX(50%);
    width: 50px;
    height: 3px;
    background: var(--gradient-accent);
    border-radius: 3px;
}

.category-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 25px;
}

.category-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: var(--card-bg);
    padding: 15px;
    border-radius: 10px;
    transition: all 0.3s ease;
    text-align: center;
    box-shadow: 0 3px 8px var(--shadow-color);
}

.category-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px var(--shadow-color);
    background: var(--gradient-primary);
    color: white;
}

.category-button i {
    font-size: 2rem;
    margin-bottom: 10px;
    color: var(--secondary-color);
    transition: color 0.3s ease;
}

.category-button:hover i {
    color: white;
}

.category-button span {
    font-weight: 600;
}

/* Template Preview Modal */
.template-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.template-preview-modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: var(--card-bg);
    border-radius: 15px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    position: relative;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: var(--primary-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-color);
    cursor: pointer;
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: var(--accent-color);
}

.modal-body {
    padding: 20px;
}

.modal-image {
    width: 100%;
    border-radius: 10px;
    margin-bottom: 20px;
}

.modal-description {
    margin-bottom: 20px;
}

.modal-specs {
    background-color: var(--light-color);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.spec-item {
    display: flex;
    margin-bottom: 10px;
}

.spec-label {
    font-weight: 600;
    width: 150px;
    color: var(--primary-color);
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .templates-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
    
    .category-buttons {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
}

@media (max-width: 480px) {
    .templates-grid {
        grid-template-columns: 1fr;
    }
    
    .category-buttons {
        grid-template-columns: repeat(2, 1fr);
    }
}
