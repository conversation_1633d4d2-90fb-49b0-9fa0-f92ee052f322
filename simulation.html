<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام محاكاة الدوائر التفاعلي - معمل الدوائر الإلكترونية الافتراضي</title>
    <meta name="description" content="نظام محاكاة تفاعلي للدوائر الإلكترونية مع التركيز على دوائر الترانزستور. تطوير د. محمد يعقوب إسماعيل">
    <meta name="keywords" content="محاكاة دوائر، ترانزستور، دوائر إلكترونية، معمل افتراضي، تعليم إلكترونيات">
    <meta name="author" content="د. محمد يعقوب إسماعيل">
    <meta name="contact" content="<EMAIL>">
    <meta name="copyright" content="د. محمد يعقوب إسماعيل">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/theme.css">
    <link rel="stylesheet" href="css/enhanced.css">
    <link rel="stylesheet" href="css/simulation.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="icon" href="images/components/transistor-npn.png" type="image/png">
</head>
<body>
    <div class="top-bar">
        <div class="container">
            <div class="top-bar-content">
                <div class="top-bar-contact">
                    <span><i class="far fa-envelope"></i> <EMAIL></span>
                    <span><i class="fas fa-phone-alt"></i> +249912867327 / +966538076790</span>
                </div>
                <div class="top-bar-author">
                    <span><i class="fas fa-user-graduate"></i> د. محمد يعقوب إسماعيل</span>
                </div>
            </div>
        </div>
    </div>

    <header>
        <div class="container">
            <h1>نظام محاكاة الدوائر التفاعلي</h1>
            <div class="theme-toggle">
                <button id="theme-toggle-btn" type="button" title="تبديل الوضع المظلم/الفاتح">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul>
                <li><a href="index.html">الرئيسية</a></li>
                <li><a href="simulation.html" class="active">المحاكاة</a></li>
                <li><a href="workbench.html">مساحة العمل</a></li>
                <li><a href="lab-notes.html">ملاحظات المعمل</a></li>
                <li><a href="#" id="nav-menu-toggle" class="mobile-menu-toggle" title="القائمة"><i class="fas fa-bars"></i><span class="sr-only">القائمة</span></a></li>
            </ul>
        </div>
    </nav>

    <main>
        <div class="container">
            <div id="experiment-breadcrumb" class="breadcrumb">
                <a href="index.html">الرئيسية</a>
                <span class="breadcrumb-separator">/</span>
                <span>نظام محاكاة الدوائر التفاعلي</span>
            </div>

            <section id="simulation-workspace">
                <div class="simulation-header">
                    <h2>محاكاة الدوائر الإلكترونية</h2>
                    <div class="simulation-controls">
                        <button id="run-simulation" class="btn"><i class="fas fa-play"></i> تشغيل المحاكاة</button>
                        <button id="pause-simulation" class="btn" disabled><i class="fas fa-pause"></i> إيقاف مؤقت</button>
                        <button id="stop-simulation" class="btn" disabled><i class="fas fa-stop"></i> إيقاف</button>
                        <button id="reset-simulation" class="btn"><i class="fas fa-redo"></i> إعادة ضبط</button>
                        <button id="save-circuit" class="btn"><i class="fas fa-save"></i> حفظ الدائرة</button>
                        <button id="load-circuit" class="btn"><i class="fas fa-folder-open"></i> تحميل دائرة</button>
                    </div>
                </div>

                <div class="simulation-container">
                    <div class="components-panel">
                        <div class="components-header">
                            <h3>مكتبة العناصر</h3>
                            <div class="components-search">
                                <input type="text" id="component-search" placeholder="بحث عن عنصر...">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>

                        <div class="components-categories">
                            <button class="component-category active" data-category="all">الكل</button>
                            <button class="component-category" data-category="sources">مصادر</button>
                            <button class="component-category" data-category="passive">عناصر سلبية</button>
                            <button class="component-category" data-category="transistors">ترانزستورات</button>
                            <button class="component-category" data-category="diodes">ديودات</button>
                            <button class="component-category" data-category="integrated">دوائر متكاملة</button>
                            <button class="component-category" data-category="instruments">أدوات قياس</button>
                        </div>

                        <div class="components-list">
                            <!-- Sources -->
                            <div class="component-item" data-category="sources" draggable="true" data-component="dc_voltage">
                                <div class="component-icon"><img src="images/components/dc_voltage.svg" alt="مصدر جهد مستمر" onerror="this.src='images/placeholder_component.png'"></div>
                                <div class="component-name">مصدر جهد مستمر</div>
                            </div>
                            <div class="component-item" data-category="sources" draggable="true" data-component="ac_voltage">
                                <div class="component-icon"><img src="images/components/ac_voltage.svg" alt="مصدر جهد متردد" onerror="this.src='images/placeholder_component.png'"></div>
                                <div class="component-name">مصدر جهد متردد</div>
                            </div>
                            <div class="component-item" data-category="sources" draggable="true" data-component="dc_current">
                                <div class="component-icon"><img src="images/components/dc_current.svg" alt="مصدر تيار مستمر" onerror="this.src='images/placeholder_component.png'"></div>
                                <div class="component-name">مصدر تيار مستمر</div>
                            </div>

                            <!-- Passive Components -->
                            <div class="component-item" data-category="passive" draggable="true" data-component="resistor">
                                <div class="component-icon"><img src="images/components/resistor.svg" alt="مقاومة" onerror="this.src='images/placeholder_component.png'"></div>
                                <div class="component-name">مقاومة</div>
                            </div>
                            <div class="component-item" data-category="passive" draggable="true" data-component="capacitor">
                                <div class="component-icon"><img src="images/components/capacitor.svg" alt="مكثف" onerror="this.src='images/placeholder_component.png'"></div>
                                <div class="component-name">مكثف</div>
                            </div>
                            <div class="component-item" data-category="passive" draggable="true" data-component="inductor">
                                <div class="component-icon"><img src="images/components/inductor.svg" alt="ملف" onerror="this.src='images/placeholder_component.png'"></div>
                                <div class="component-name">ملف</div>
                            </div>

                            <!-- Transistors -->
                            <div class="component-item" data-category="transistors" draggable="true" data-component="npn">
                                <div class="component-icon"><img src="images/components/npn.svg" alt="ترانزستور NPN" onerror="this.src='images/components/transistor-npn.png'"></div>
                                <div class="component-name">ترانزستور NPN</div>
                            </div>
                            <div class="component-item" data-category="transistors" draggable="true" data-component="pnp">
                                <div class="component-icon"><img src="images/components/pnp.svg" alt="ترانزستور PNP" onerror="this.src='images/placeholder_component.png'"></div>
                                <div class="component-name">ترانزستور PNP</div>
                            </div>
                            <div class="component-item" data-category="transistors" draggable="true" data-component="mosfet_n">
                                <div class="component-icon"><img src="images/components/mosfet_n.svg" alt="MOSFET قناة N" onerror="this.src='images/placeholder_component.png'"></div>
                                <div class="component-name">MOSFET قناة N</div>
                            </div>
                            <div class="component-item" data-category="transistors" draggable="true" data-component="mosfet_p">
                                <div class="component-icon"><img src="images/components/mosfet_p.svg" alt="MOSFET قناة P" onerror="this.src='images/placeholder_component.png'"></div>
                                <div class="component-name">MOSFET قناة P</div>
                            </div>

                            <!-- Diodes -->
                            <div class="component-item" data-category="diodes" draggable="true" data-component="diode">
                                <div class="component-icon"><img src="images/components/diode.svg" alt="ديود" onerror="this.src='images/placeholder_component.png'"></div>
                                <div class="component-name">ديود</div>
                            </div>
                            <div class="component-item" data-category="diodes" draggable="true" data-component="zener">
                                <div class="component-icon"><img src="images/components/zener.svg" alt="ديود زينر" onerror="this.src='images/placeholder_component.png'"></div>
                                <div class="component-name">ديود زينر</div>
                            </div>
                            <div class="component-item" data-category="diodes" draggable="true" data-component="led">
                                <div class="component-icon"><img src="images/components/led.svg" alt="LED" onerror="this.src='images/placeholder_component.png'"></div>
                                <div class="component-name">LED</div>
                            </div>

                            <!-- Measurement Instruments -->
                            <div class="component-item" data-category="instruments" draggable="true" data-component="voltmeter">
                                <div class="component-icon"><img src="images/components/voltmeter.svg" alt="فولتميتر" onerror="this.src='images/placeholder_component.png'"></div>
                                <div class="component-name">فولتميتر</div>
                            </div>
                            <div class="component-item" data-category="instruments" draggable="true" data-component="ammeter">
                                <div class="component-icon"><img src="images/components/ammeter.svg" alt="أميتر" onerror="this.src='images/placeholder_component.png'"></div>
                                <div class="component-name">أميتر</div>
                            </div>
                            <div class="component-item" data-category="instruments" draggable="true" data-component="oscilloscope">
                                <div class="component-icon"><img src="images/components/oscilloscope.svg" alt="راسم إشارة" onerror="this.src='images/placeholder_component.png'"></div>
                                <div class="component-name">راسم إشارة</div>
                            </div>
                        </div>
                    </div>

                    <div class="circuit-workspace">
                        <div class="workspace-toolbar">
                            <button id="select-tool" class="tool-btn active" title="تحديد"><i class="fas fa-mouse-pointer"></i></button>
                            <button id="wire-tool" class="tool-btn" title="سلك توصيل"><i class="fas fa-project-diagram"></i></button>
                            <button id="delete-tool" class="tool-btn" title="حذف"><i class="fas fa-trash"></i></button>
                            <button id="rotate-tool" class="tool-btn" title="تدوير"><i class="fas fa-sync"></i></button>
                            <button id="zoom-in" class="tool-btn" title="تكبير"><i class="fas fa-search-plus"></i></button>
                            <button id="zoom-out" class="tool-btn" title="تصغير"><i class="fas fa-search-minus"></i></button>
                            <button id="zoom-fit" class="tool-btn" title="ملائمة للشاشة"><i class="fas fa-expand"></i></button>
                        </div>

                        <div id="circuit-canvas" class="circuit-canvas">
                            <!-- Canvas for drawing the circuit -->
                            <canvas id="main-canvas"></canvas>
                            <div id="grid-background"></div>
                        </div>
                    </div>

                    <div class="properties-panel">
                        <div class="properties-header">
                            <h3>خصائص العنصر</h3>
                            <button id="close-properties" class="btn-icon"><i class="fas fa-times"></i></button>
                        </div>

                        <div id="no-selection-message" class="properties-message">
                            <p>يرجى تحديد عنصر لعرض خصائصه</p>
                        </div>

                        <div id="component-properties" class="properties-content" style="display: none;">
                            <div class="property-group">
                                <h4 id="component-title">عنوان العنصر</h4>
                                <div class="property-item">
                                    <label for="component-id">المعرف:</label>
                                    <input type="text" id="component-id" placeholder="معرف العنصر">
                                </div>
                            </div>

                            <div id="component-specific-properties">
                                <!-- Dynamic properties will be loaded here based on component type -->
                            </div>

                            <div class="property-group">
                                <h4>الوظيفة في الدائرة</h4>
                                <div id="component-function" class="component-description">
                                    وصف وظيفة العنصر في الدائرة
                                </div>
                            </div>

                            <div class="property-actions">
                                <button id="apply-properties" class="btn">تطبيق</button>
                                <button id="reset-properties" class="btn">إعادة ضبط</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="analysis-panel">
                    <div class="analysis-tabs">
                        <button class="analysis-tab active" data-tab="simulation-results">نتائج المحاكاة</button>
                        <button class="analysis-tab" data-tab="waveform-analysis">تحليل الإشارة</button>
                        <button class="analysis-tab" data-tab="dc-analysis">تحليل DC</button>
                        <button class="analysis-tab" data-tab="ac-analysis">تحليل AC</button>
                        <button class="analysis-tab" data-tab="transient-analysis">تحليل عابر</button>
                    </div>

                    <div class="analysis-content">
                        <div id="simulation-results" class="analysis-tab-content active">
                            <div class="results-container">
                                <div class="results-header">
                                    <h4>نتائج المحاكاة</h4>
                                    <div class="results-controls">
                                        <button id="export-results" class="btn btn-sm"><i class="fas fa-file-export"></i> تصدير</button>
                                        <button id="clear-results" class="btn btn-sm"><i class="fas fa-eraser"></i> مسح</button>
                                    </div>
                                </div>
                                <div id="results-display" class="results-display">
                                    <p class="no-results">لم يتم تشغيل المحاكاة بعد. قم بتصميم الدائرة وتشغيل المحاكاة لعرض النتائج.</p>
                                </div>
                            </div>
                        </div>

                        <div id="waveform-analysis" class="analysis-tab-content">
                            <div class="waveform-container">
                                <div class="waveform-controls">
                                    <div class="control-group">
                                        <label for="waveform-node">نقطة القياس:</label>
                                        <select id="waveform-node">
                                            <option value="">-- اختر نقطة --</option>
                                        </select>
                                    </div>
                                    <div class="control-group">
                                        <label for="waveform-type">نوع القياس:</label>
                                        <select id="waveform-type">
                                            <option value="voltage">الجهد</option>
                                            <option value="current">التيار</option>
                                        </select>
                                    </div>
                                    <div class="control-group">
                                        <button id="add-waveform" class="btn btn-sm">إضافة إشارة</button>
                                        <button id="clear-waveforms" class="btn btn-sm">مسح الكل</button>
                                    </div>
                                </div>
                                <div id="waveform-display" class="waveform-display">
                                    <canvas id="waveform-canvas"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- Other analysis tabs content will be added here -->
                    </div>
                </div>
            </section>

            <section id="simulation-guide" class="card">
                <h2>دليل استخدام نظام المحاكاة</h2>
                <div class="guide-content">
                    <div class="guide-item">
                        <h3><i class="fas fa-drafting-compass"></i> تصميم الدائرة</h3>
                        <p>اسحب العناصر من مكتبة العناصر إلى مساحة العمل. استخدم أداة التوصيل لربط العناصر معًا.</p>
                    </div>
                    <div class="guide-item">
                        <h3><i class="fas fa-sliders-h"></i> ضبط الخصائص</h3>
                        <p>انقر على أي عنصر لعرض خصائصه في لوحة الخصائص. يمكنك تعديل قيم العناصر مثل الجهد والمقاومة والسعة.</p>
                    </div>
                    <div class="guide-item">
                        <h3><i class="fas fa-play"></i> تشغيل المحاكاة</h3>
                        <p>بعد تصميم الدائرة، انقر على زر "تشغيل المحاكاة" لبدء المحاكاة وعرض النتائج.</p>
                    </div>
                    <div class="guide-item">
                        <h3><i class="fas fa-chart-line"></i> تحليل النتائج</h3>
                        <p>استخدم أدوات التحليل المختلفة لفحص سلوك الدائرة، مثل تحليل الإشارة وتحليل DC وAC.</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <footer>
        <div class="footer-top">
            <div class="container">
                <div class="footer-columns">
                    <div class="footer-column">
                        <h3>معمل الدوائر الإلكترونية</h3>
                        <p>منصة تعليمية تفاعلية لفهم مبادئ عمل الترانزستور والدوائر الإلكترونية</p>
                        <div class="social-links">
                            <a href="#" title="فيسبوك"><i class="fab fa-facebook"></i></a>
                            <a href="#" title="تويتر"><i class="fab fa-twitter"></i></a>
                            <a href="#" title="يوتيوب"><i class="fab fa-youtube"></i></a>
                            <a href="#" title="لينكد إن"><i class="fab fa-linkedin"></i></a>
                        </div>
                    </div>

                    <div class="footer-column">
                        <h3>روابط سريعة</h3>
                        <ul class="footer-links">
                            <li><a href="index.html">الرئيسية</a></li>
                            <li><a href="simulation.html">المحاكاة</a></li>
                            <li><a href="workbench.html">مساحة العمل</a></li>
                            <li><a href="lab-notes.html">ملاحظات المعمل</a></li>
                        </ul>
                    </div>

                    <div class="footer-column">
                        <h3>الدعم</h3>
                        <ul class="footer-links">
                            <li><a href="#">دليل المستخدم</a></li>
                            <li><a href="#">الأسئلة الشائعة</a></li>
                            <li><a href="#">اتصل بنا</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer-bottom">
            <div class="container">
                <div class="author-info">
                    <p class="author-name"><strong>المؤلف:</strong> د. محمد يعقوب إسماعيل</p>
                    <p class="author-contact">
                        <span><i class="far fa-envelope"></i> <EMAIL></span>
                        <span><i class="fas fa-phone-alt"></i> +249912867327 / +966538076790</span>
                    </p>
                </div>
                <p>&copy; 2024 معمل الدوائر الإلكترونية الافتراضي. جميع الحقوق محفوظة.</p>
                <p>تم التطوير بواسطة د. محمد يعقوب إسماعيل</p>
            </div>
        </div>
    </footer>

    <div id="scroll-top" class="scroll-top" title="التمرير لأعلى">
        <i class="fas fa-chevron-up"></i>
    </div>

    <script src="js/main.js"></script>
    <script src="js/simulation.js"></script>
</body>
</html>