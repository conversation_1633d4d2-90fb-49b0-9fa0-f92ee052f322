<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NPN Transistor V_BE Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background-color: #f0f0f0;
            padding: 20px;
            box-sizing: border-box;
        }
        .app-container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.15);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        h1 {
            font-size: 1.4em; /* Adjusted for better fit on mobile */
            margin-top: 0;
            margin-bottom: 20px;
            color: #333;
        }

        .schematic-container {
            width: 300px; 
            height: 360px;
            border: 1px solid #ccc;
            margin: 20px auto;
            position: relative;
            background-color: #f9f9f9;
            max-width: 100%; /* Ensures it shrinks on small screens */
            overflow: hidden; /* Clips content if schematic is larger than container (e.g. due to max-width) */
        }

        .schematic .component {
            position: absolute;
            font-size: 11px;
            text-align: center;
            box-sizing: border-box;
        }

        .schematic .resistor-symbol {
            width: 50px;
            height: 18px;
            border: 1.5px solid black;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: white;
            z-index: 1; /* Above wires */
        }

        .schematic .led-symbol {
            width: 30px; /* Slightly larger LED */
            height: 30px;
            border-radius: 50%;
            border: 2px solid black;
            background-color: #dc3545; /* Default OFF state (Red) */
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 9px;
            font-weight: bold;
            color: white;
            z-index: 1; /* Above wires */
        }
        .schematic .led-symbol.on {
            background-color: #28a745; /* Green for ON */
            box-shadow: 0 0 10px #28a745, 0 0 15px #28a745;
        }
        .schematic .led-symbol.off {
            background-color: #dc3545; /* Red for OFF */
        }

        .schematic .npn-symbol {
            width: 50px; 
            height: 70px;
            z-index: 5; 
        }
        .schematic .npn-circle {
            width: 30px; 
            height: 30px;
            border: 2px solid black;
            border-radius: 50%;
            position: absolute;
            top: 20px; 
            left: 10px; 
            background-color: #f9f9f9; 
            z-index: 6; 
        }
        .schematic .npn-collector-line {
            position: absolute;
            width: 2px;
            height: 22px; 
            background-color: black;
            left: 24px; 
            top: 0px;
        }
        .schematic .npn-emitter-line {
            position: absolute;
            width: 2px;
            height: 22px; 
            background-color: black;
            left: 24px; 
            bottom: 0px;
        }
        .schematic .npn-base-line {
            position: absolute;
            width: 12px; 
            height: 2px;
            background-color: black;
            left: 0px;
            top: 34px; 
        }
        .schematic .npn-emitter-arrow {
            position: absolute;
            width: 0;
            height: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 7px solid black; 
            left: 20px; 
            bottom: 3px; 
            z-index: 7;
        }

        .schematic .wire {
            background-color: black;
            position: absolute;
            z-index: 0; 
        }
        .schematic .ground-symbol {
            position: absolute;
        }
        .schematic .ground-line {
            background-color: black;
            height: 2px;
            margin: 0 auto; 
            margin-bottom: 3px;
        }

        .schematic .voltage-display {
            position: absolute;
            font-size: 11px; /* Slightly smaller */
            background-color: rgba(255, 255, 255, 0.9);
            padding: 2px 4px;
            border-radius: 3px;
            border: 1px solid #ddd;
            z-index: 10; 
        }
        .schematic .terminal-label {
            position: absolute;
            font-weight: bold;
            font-size: 13px;
            z-index: 10;
        }
        .schematic .text-label {
            position: absolute;
            font-size: 12px;
        }

        .controls {
            margin-top: 25px;
            display: grid;
            grid-template-columns: auto 1fr; 
            gap: 12px 8px; /* Increased gap slightly */
            align-items: center;
        }
        .controls label {
            text-align: right;
            font-size: 0.95em;
        }
        .controls input[type="number"] {
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            width: 100%; 
            box-sizing: border-box;
            font-size: 1em;
        }
        .controls button {
            grid-column: 1 / -1; 
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1em;
            margin-top: 10px;
        }
        .controls button:hover {
            background-color: #0056b3;
        }

        .status {
            margin-top: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }
        .status.on { color: #28a745; }
        .status.off { color: #dc3545; }

        @media (max-width: 400px) {
            .app-container {
                padding: 15px;
            }
            h1 {
                font-size: 1.2em;
            }
            .controls {
                grid-template-columns: 1fr; 
            }
            .controls label {
                text-align: left; 
                margin-bottom: -5px; 
            }
            .schematic .voltage-display {
                font-size: 10px; /* Make voltage displays smaller on small screens */
                padding: 1px 3px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <h1>NPN Transistor Conduction (V<sub>BE</sub> &ge; 0.7V)</h1>

        <div class="schematic-container">
            <div class="schematic">
                <!-- VCC Text Label -->
                <div class="text-label component" style="top: 5px; left: 160px;">+V<sub>CC</sub></div>
                <!-- Wire from Vcc to RC -->
                <div class="wire" style="top: 20px; left: 149px; height: 15px; width: 2px;"></div>

                <!-- RC Resistor -->
                <div class="resistor-symbol component" style="top: 35px; left: 125px;" id="rc-symbol">R<sub>C</sub></div>
                <!-- Wire from RC to LED -->
                <div class="wire" style="top: 53px; left: 149px; height: 12px; width: 2px;"></div>

                <!-- LED -->
                <div class="led-symbol component" style="top: 65px; left: 135px;" id="status-led">OFF</div>
                <!-- Wire from LED to Collector -->
                <div class="wire" style="top: 95px; left: 149px; height: 10px; width: 2px;"></div>

                <!-- Transistor NPN Symbol -->
                <div class="npn-symbol component" style="top: 105px; left: 125px;">
                    <div class="npn-collector-line"></div>
                    <div class="npn-emitter-line"></div>
                    <div class="npn-base-line"></div>
                    <div class="npn-circle"></div>
                    <div class="npn-emitter-arrow"></div>
                </div>
                <div class="terminal-label" style="top: 95px; left: 165px;">C</div>
                <div class="terminal-label" style="top: 138px; left: 95px;">B</div>
                <div class="terminal-label" style="top: 180px; left: 165px;">E</div>
                
                <!-- RB Resistor -->
                <div class="wire" style="top: 154px; left: 40px; width: 40px; height: 2px;"></div> <!-- Input point to RB -->
                <div class="resistor-symbol component" style="top: 145px; left: 80px;" id="rb-symbol">R<sub>B</sub></div>
                <div class="wire" style="top: 154px; left: 130px; width: 7px; height: 2px;"></div> <!-- Connects RB to npn-base-line start -->

                <!-- Emitter to Ground -->
                <div class="wire" style="top: 175px; left: 149px; height: 20px; width: 2px;"></div>
                <div class="ground-symbol component" style="top: 195px; left: 135px;">
                    <div class="ground-line" style="width: 30px;"></div>
                    <div class="ground-line" style="width: 20px;"></div>
                    <div class="ground-line" style="width: 10px;"></div>
                </div>

                <!-- Voltage Displays on Schematic -->
                <div id="vb-display" class="voltage-display" style="top: 170px; left: 45px;">Vb=0.0V</div>
                <div id="ve-display" class="voltage-display" style="top: 215px; left: 130px;">Ve=0.0V</div>
            </div>
        </div>

        <div class="controls">
            <label for="vb-input">Base Voltage (V<sub>B</sub>):</label>
            <input type="number" id="vb-input" value="0.0" step="0.1" aria-label="Base Voltage">

            <label for="ve-input">Emitter Voltage (V<sub>E</sub>):</label>
            <input type="number" id="ve-input" value="0.0" step="0.1" aria-label="Emitter Voltage">

            <button id="simulate-button">Simulate</button>
        </div>

        <div id="status-message" class="status">Transistor is OFF</div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const vbInput = document.getElementById('vb-input');
        const veInput = document.getElementById('ve-input');
        const simulateButton = document.getElementById('simulate-button');
        const statusLed = document.getElementById('status-led');
        const statusMessage = document.getElementById('status-message');
        const vbDisplay = document.getElementById('vb-display');
        const veDisplay = document.getElementById('ve-display');

        const VBE_THRESHOLD = 0.7;

        function updateSchematicDisplays(vb, ve) {
            vbDisplay.textContent = `Vb=${vb.toFixed(1)}V`;
            veDisplay.textContent = `Ve=${ve.toFixed(1)}V`;
        }

        function enforceConstraintsAndDisplay() {
            let vb = parseFloat(vbInput.value);
            let ve = parseFloat(veInput.value);

            if (isNaN(vb)) {
                vb = 0.0;
                vbInput.value = vb.toFixed(1);
            }
            if (isNaN(ve)) {
                ve = 0.0;
                veInput.value = ve.toFixed(1);
            }

            // Constraint: Emitter voltage cannot be higher than base voltage.
            if (ve > vb) {
                veInput.value = vb.toFixed(1);
                ve = parseFloat(veInput.value); // Re-assign ve after correcting input
            }
            
            updateSchematicDisplays(vb, ve);
        }

        vbInput.addEventListener('input', enforceConstraintsAndDisplay);
        veInput.addEventListener('input', enforceConstraintsAndDisplay);

        function runSimulation() {
            enforceConstraintsAndDisplay(); 

            let vb = parseFloat(vbInput.value);
            let ve = parseFloat(veInput.value);

            const vbe = vb - ve;

            if (vbe >= VBE_THRESHOLD) {
                statusLed.classList.remove('off');
                statusLed.classList.add('on');
                statusLed.textContent = "ON";
                statusMessage.textContent = 'Transistor is ON';
                statusMessage.className = 'status on';
            } else {
                statusLed.classList.remove('on');
                statusLed.classList.add('off');
                statusLed.textContent = "OFF";
                statusMessage.textContent = 'Transistor is OFF';
                statusMessage.className = 'status off';
            }
        }

        simulateButton.addEventListener('click', runSimulation);

        // Initial setup on page load
        enforceConstraintsAndDisplay(); 
        runSimulation(); 
    });
    </script>
</body>
</html>
