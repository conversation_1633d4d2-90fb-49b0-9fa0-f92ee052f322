/* Theme CSS for Electronic Circuits Lab */

/* Theme Variables */
:root {
    /* Light Theme (Default) - تنسيق ألوان متناسق بدرجات الأزرق والذهبي */
    --primary-color: #1e4976;
    --secondary-color: #3498db;
    --accent-color: #f1c40f;
    --light-color: #f5f9fc;
    --dark-color: #2c3e50;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --text-color: #2c3e50;
    --bg-color: #f5f9fc;
    --card-bg: #ffffff;
    --header-bg: #1e4976;
    --footer-bg: #1e4976;
    --border-color: #e0e7ee;
    --input-bg: #ffffff;
    --shadow-color: rgba(30, 73, 118, 0.1);
    --gradient-primary: linear-gradient(135deg, #1e4976, #3498db);
    --gradient-accent: linear-gradient(135deg, #f1c40f, #f39c12);
    --card-hover-shadow: 0 10px 20px rgba(30, 73, 118, 0.15);
}

/* Dark Theme */
[data-theme="dark"] {
    --primary-color: #1e4976;
    --secondary-color: #3498db;
    --accent-color: #f1c40f;
    --light-color: #2c3e50;
    --dark-color: #1a2530;
    --text-color: #ecf0f1;
    --bg-color: #1a2530;
    --card-bg: #2c3e50;
    --header-bg: #162232;
    --footer-bg: #162232;
    --border-color: #34495e;
    --input-bg: #34495e;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --gradient-primary: linear-gradient(135deg, #162232, #1e4976);
    --gradient-accent: linear-gradient(135deg, #f39c12, #f1c40f);
    --card-hover-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

/* Apply Theme to Elements */
body {
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

header {
    background: var(--gradient-primary);
    box-shadow: 0 2px 10px var(--shadow-color);
}

footer {
    background: var(--gradient-primary);
}

.container {
    background-color: transparent;
}

section {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    box-shadow: 0 4px 10px var(--shadow-color);
    border-radius: 10px;
}

input, select, textarea {
    background-color: var(--input-bg);
    color: var(--text-color);
    border-color: var(--border-color);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Improved Headings */
h1, h2, h3, h4, h5, h6 {
    color: var(--primary-color);
}

h2 {
    border-bottom: 2px solid var(--secondary-color);
    position: relative;
}

h2::after {
    content: '';
    position: absolute;
    bottom: -2px;
    right: 0;
    width: 50px;
    height: 2px;
    background-color: var(--accent-color);
}

/* Theme Toggle Button */
.theme-toggle {
    position: absolute;
    top: 20px;
    left: 20px;
}

.theme-toggle button {
    background: transparent;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}

.theme-toggle button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Enhanced Header */
header .container {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    background-color: transparent;
}

/* Enhanced Navigation */
nav .container {
    background-color: transparent;
}

nav ul {
    position: relative;
}

.mobile-menu-toggle {
    display: none;
}

/* Hero Section Enhancements */
.hero-content {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.hero-text {
    flex: 1;
}

.hero-image {
    flex: 1;
    text-align: center;
}

.hero-image img {
    max-width: 100%;
    border-radius: 10px;
    box-shadow: 0 5px 15px var(--shadow-color);
}

.hero-features {
    display: flex;
    gap: 1rem;
    margin: 1.5rem 0;
    flex-wrap: wrap;
}

.feature {
    background-color: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.feature i {
    color: var(--secondary-color);
}

.hero-stats {
    display: flex;
    justify-content: space-around;
    margin-top: 2rem;
    text-align: center;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: 10px;
}

.stat {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--secondary-color);
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

/* Button Styles */
.button, button, input[type="submit"] {
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 30px;
    padding: 10px 25px;
    font-weight: 600;
    box-shadow: 0 4px 10px rgba(30, 73, 118, 0.2);
    transition: all 0.3s ease;
    text-transform: none;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.button:hover, button:hover, input[type="submit"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(30, 73, 118, 0.3);
}

.button:active, button:active, input[type="submit"]:active {
    transform: translateY(1px);
    box-shadow: 0 2px 5px rgba(30, 73, 118, 0.2);
}

.button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.7s ease;
    z-index: -1;
}

.button:hover::before {
    left: 100%;
}

.button-primary {
    background: var(--gradient-primary);
}

.button-secondary {
    background: var(--gradient-accent);
    color: var(--dark-color);
}

.button-secondary:hover {
    color: var(--dark-color);
}

.btn-sm {
    padding: 6px 15px;
    font-size: 0.9rem;
    border-radius: 20px;
}

.btn-icon {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--secondary-color);
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px var(--shadow-color);
}

.btn-icon:hover {
    background-color: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
}

.btn-icon.active {
    background-color: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

/* Section Header */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.view-all {
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Enhanced Experiment Cards */
.experiment-card {
    position: relative;
    transition: all 0.4s ease;
    border: none;
    border-radius: 15px;
    overflow: hidden;
    background-color: var(--card-bg);
    box-shadow: 0 5px 15px var(--shadow-color);
}

.experiment-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--card-hover-shadow);
}

.experiment-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--gradient-accent);
    color: var(--dark-color);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 1;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.new-badge {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
}

.experiment-thumbnail {
    transition: all 0.5s ease;
    height: 180px;
}

.experiment-card:hover .experiment-thumbnail {
    transform: scale(1.05);
}

.experiment-card h3 {
    background: var(--gradient-primary);
    margin: 0;
    padding: 1rem;
}

.experiment-card h3 a {
    color: white;
    text-decoration: none;
    position: relative;
    display: inline-block;
}

.experiment-card h3 a::after {
    content: '';
    position: absolute;
    bottom: -3px;
    right: 0;
    width: 0;
    height: 2px;
    background-color: var(--accent-color);
    transition: width 0.3s ease;
}

.experiment-card h3 a:hover::after {
    width: 100%;
    right: auto;
    left: 0;
}

.experiment-meta {
    display: flex;
    justify-content: space-between;
    padding: 0.8rem 1rem;
    font-size: 0.85rem;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid var(--border-color);
}

.experiment-level {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    font-weight: 600;
}

.beginner {
    color: var(--success-color);
}

.intermediate {
    color: var(--warning-color);
}

.advanced {
    color: var(--danger-color);
}

.experiment-duration {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    color: var(--text-color);
    opacity: 0.8;
}

.experiment-details {
    padding: 0 1rem;
    margin-bottom: 1rem;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    font-size: 0.9rem;
}

.detail-item {
    padding: 0.5rem 0;
    border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 8px;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item i {
    color: var(--secondary-color);
    width: 20px;
    text-align: center;
}

.experiment-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: rgba(0, 0, 0, 0.01);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Learning Path */
.learning-path-container {
    position: relative;
    padding: 30px 0;
    background-color: var(--light-color);
    border-radius: 15px;
    margin: 30px 0;
}

.learning-path-timeline {
    position: relative;
    padding-right: 40px;
}

.learning-path-timeline::before {
    content: '';
    position: absolute;
    top: 0;
    right: 20px;
    height: 100%;
    width: 3px;
    background: var(--gradient-primary);
    border-radius: 3px;
    opacity: 0.5;
}

.learning-path-item {
    position: relative;
    margin-bottom: 40px;
    padding-right: 55px;
    transition: all 0.3s ease;
}

.learning-path-item:hover {
    transform: translateX(-5px);
}

.path-item-number {
    position: absolute;
    top: 0;
    right: 0;
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border: 3px solid white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    z-index: 1;
    box-shadow: 0 3px 10px var(--shadow-color);
    transition: all 0.3s ease;
}

.learning-path-item:hover .path-item-number {
    transform: scale(1.1);
    box-shadow: 0 5px 15px var(--shadow-color);
}

.learning-path-item.active .path-item-number {
    background: var(--gradient-accent);
    color: var(--dark-color);
}

.path-item-content {
    background-color: var(--card-bg);
    border: none;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px var(--shadow-color);
    transition: all 0.3s ease;
}

.learning-path-item:hover .path-item-content {
    box-shadow: var(--card-hover-shadow);
}

.learning-path-item.active .path-item-content {
    border-right: 4px solid var(--accent-color);
}

.path-item-content h3 {
    color: var(--primary-color);
    margin-top: 0;
    font-size: 1.3rem;
}

.path-item-resources {
    margin: 15px 0;
    background-color: rgba(0, 0, 0, 0.02);
    padding: 10px;
    border-radius: 10px;
}

.path-resource {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
    color: var(--primary-color);
}

.path-resource:hover {
    background-color: rgba(52, 152, 219, 0.1);
    transform: translateX(5px);
}

.path-resource i {
    color: var(--secondary-color);
}

.path-completion {
    display: block;
    font-size: 0.9rem;
    color: var(--text-color);
    opacity: 0.7;
    font-style: italic;
    margin-top: 15px;
    text-align: center;
}

.learning-path-progress {
    margin-top: 30px;
    padding: 0 20px;
}

.progress-bar {
    height: 12px;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-fill {
    height: 100%;
    background: var(--gradient-accent);
    border-radius: 10px;
    width: 0;
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.1) 25%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.1) 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite linear;
}

@keyframes shimmer {
    0% { background-position: 100% 0; }
    100% { background-position: -100% 0; }
}

.progress-20 {
    width: 20%;
}

.progress-40 {
    width: 40%;
}

.progress-60 {
    width: 60%;
}

.progress-80 {
    width: 80%;
}

.progress-100 {
    width: 100%;
}

.progress-text {
    text-align: center;
    margin-top: 10px;
    font-size: 0.95rem;
    color: var(--primary-color);
    font-weight: 600;
}
