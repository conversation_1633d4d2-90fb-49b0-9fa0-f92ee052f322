<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محاكاة الترانزستور التفاعلية</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <header>
        <h1>محاكاة عمل الترانزستور</h1>
    </header>

    <nav>
        <ul>
            <li><a href="index.html">الرئيسية</a></li>
            <li><a href="simulation.html">بدء المحاكاة</a></li>
        </ul>
    </nav>

    <main>
        <section id="simulator-interface">
            <h2>واجهة المحاكاة التفاعلية</h2>
            <p>اختر تجربة من القائمة أدناه لعرضها في منطقة المحاكاة:</p>
            
            <div class="experiment-selector">
                <label for="experiment-select">اختر التجربة:</label>
                <select id="experiment-select" onchange="loadExperiment()">
                    <option value="">-- اختر تجربة --</option>
                    <option value="NPN BJT Operating Modes Explorer.html">مستكشف أوضاع تشغيل الترانزستور NPN</option>
                    <option value="PNP BJT Operating Regions Explorer.html">مستكشف أوضاع تشغيل الترانزستور PNP</option>
                    <option value="NPN Transistor Interactive Explorer.html">مستكشف الترانزستور NPN التفاعلي</option>
                    <option value="Common Emitter Amplifier.html">محاكاة مضخم الباعث المشترك (NPN)</option>
                    <option value="Transistor as a Switch.html">الترانزستور كمفتاح</option>
                    <!-- أضف المزيد من الخيارات هنا للتجارب الأخرى -->
                </select>
            </div>

            <div class="simulation-area">
                <iframe id="experiment-frame" src="about:blank" style="width:100%; height:600px; border:1px solid #ccc;"></iframe>
                <p id="simulation-placeholder" style="text-align: center; padding: 20px;">يرجى اختيار تجربة لعرضها هنا.</p>
            </div>
        </section>

        <section id="transistor-basics">
            <h2>أساسيات الترانزستور (للمبتدئين)</h2>
            <p>الترانزستور هو مفتاح إلكتروني ومضخم للإشارة. يتكون عادة من ثلاث طبقات من مادة شبه موصلة.</p>
            <ul>
                <li><strong>الباعث (Emitter):</strong> يبعث حاملات الشحنة.</li>
                <li><strong>القاعدة (Base):</strong> تتحكم في تدفق حاملات الشحنة.</li>
                <li><strong>المجمع (Collector):</strong> يجمع حاملات الشحنة.</li>
            </ul>
            <p>بتطبيق جهد صغير على القاعدة، يمكن التحكم في تيار كبير يمر بين المجمع والباعث.</p>
        </section>

        <section id="transistor-intermediate">
            <h2>مفاهيم متقدمة (للمستوى المتوسط)</h2>
            <p>للتعمق أكثر، يمكننا النظر في:</p>
            <ul>
                <li><strong>منحنيات الخواص:</strong> العلاقة بين التيارات والجهود المختلفة في الترانزستور.</li>
                <li><strong>نماذج الترانزستور:</strong> مثل نموذج إيبرس-مول.</li>
                <li><strong>دوائر التضخيم المختلفة:</strong> مثل دائرة الباعث المشترك، القاعدة المشتركة، والمجمع المشترك.</li>
            </ul>
        </section>
    </main>

    <footer>
        <p>&copy; 2024 معمل الترانزستور الافتراضي. جميع الحقوق محفوظة.</p>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>