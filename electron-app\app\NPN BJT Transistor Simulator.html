<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NPN BJT Transistor Simulator</title>
    <style>
        body {
            font-family: Arial, Helvetica, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh; /* Ensure footer (if any) or content is pushed down */
        }

        .container {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 600px;
        }

        h1 {
            color: #005A9C;
            text-align: center;
            margin-top: 0;
            margin-bottom: 25px;
        }

        .transistor-diagram {
            margin-bottom: 25px;
            display: flex;
            justify-content: center;
        }
        
        .control-item {
            margin-bottom: 20px;
        }

        .control-item label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            color: #555;
        }
        
        .slider-group {
            display: flex;
            align-items: center;
            gap: 15px; /* Space between slider and value */
        }

        .slider-group input[type="range"] {
            flex-grow: 1;
            cursor: pointer;
            -webkit-appearance: none;
            appearance: none;
            height: 10px;
            background: #ddd;
            outline: none;
            border-radius: 5px;
        }
        .slider-group input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            background: #007BFF;
            cursor: pointer;
            border-radius: 50%;
        }
        .slider-group input[type="range"]::-moz-range-thumb {
            width: 20px;
            height: 20px;
            background: #007BFF;
            cursor: pointer;
            border-radius: 50%;
            border: none;
        }

        .slider-group #ibValue {
            font-weight: bold;
            color: #007BFF;
            min-width: 65px; 
            text-align: right;
        }

        .outputs h2 {
            color: #005A9C;
            text-align: center;
            margin-top: 30px;
            margin-bottom: 15px;
            font-size: 1.4em;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }

        .outputs p {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 5px;
            margin: 5px 0;
            border-bottom: 1px solid #f0f0f0;
            line-height: 1.6;
        }
        .outputs p:last-child {
            border-bottom: none;
        }

        .outputs p .label-text {
            color: #444;
        }
        .outputs p .value-text {
            font-weight: bold;
        }
        
        #icValue { color: #28a745; } 
        #vceValue { color: #dc3545; }
        #powerValue { color: #fd7e14; } /* Orange */
        
        #regionValue.cutoff { color: #6c757d; } 
        #regionValue.active { color: #28a745; } 
        #regionValue.saturation { color: #dc3545; }

        @media (max-width: 480px) {
            body { padding: 10px; }
            .container { padding: 15px; }
            h1 { font-size: 1.5em; }
            .outputs h2 { font-size: 1.2em; }
            
            .slider-group { 
                flex-wrap: wrap; 
            }
            .slider-group input[type="range"] { 
                width: 100%; 
                margin-bottom: 10px; /* Space when value wraps below */
            }
            .slider-group #ibValue { 
                width: 100%; 
                text-align: center; 
                margin-bottom: 5px;
            }
            
            .outputs p { 
                flex-direction: column; 
                align-items: flex-start; 
            }
            .outputs p .value-text { 
                margin-top: 4px; 
                padding-left: 10px; /* Indent value slightly */
            }
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>NPN BJT Transistor Simulator</h1>

        <div class="transistor-diagram">
            <svg width="120" height="140" viewBox="0 0 100 120" xmlns="http://www.w3.org/2000/svg">
                <style>
                    .line { stroke: black; stroke-width: 2; }
                    .label { font-family: Arial, sans-serif; font-size: 14px; text-anchor: middle; dominant-baseline: middle; }
                    .arrow { fill: black; }
                </style>
                <!-- Base -->
                <line x1="10" y1="60" x2="45" y2="60" class="line" /> 
                <text x="25" y="60" class="label" text-anchor="end" >B</text>
                <!-- Vertical bar (part of transistor symbol) -->
                <line x1="45" y1="40" x2="45" y2="80" class="line" />
            
                <!-- Collector -->
                <line x1="45" y1="40" x2="65" y2="20" class="line" /> 
                <line x1="65" y1="20" x2="65" y2="0" class="line" />  
                <text x="65" y="8" class="label">C</text>
            
                <!-- Emitter -->
                <line x1="45" y1="80" x2="65" y2="100" class="line" /> 
                <line x1="65" y1="100" x2="65" y2="120" class="line" /> 
                <text x="65" y="112" class="label">E</text>
            
                <!-- Arrow on Emitter's slanted line (NPN: pointing outwards) -->
                <!-- Tip at (60,95), Barb1 (53,92), Barb2 (57,88) -->
                <polygon points="60,95 53,92 57,88" class="arrow"/>
            </svg>
        </div>

        <div class="control-item">
            <label for="ibSlider">Base Current (I<sub>b</sub>):</label>
            <div class="slider-group">
                <input type="range" id="ibSlider" min="0" max="100" step="1" value="0" aria-label="Base Current Slider">
                <span id="ibValue">0 µA</span>
            </div>
        </div>

        <div class="outputs">
            <h2>Calculated Values</h2>
            <p><span class="label-text">Collector Current (I<sub>c</sub>):</span> <span id="icValue" class="value-text">0.00 mA</span></p>
            <p><span class="label-text">Collector-Emitter Voltage (V<sub>ce</sub>):</span> <span id="vceValue" class="value-text">9.00 V</span></p>
            <p><span class="label-text">Power Dissipated (P<sub>d</sub>):</span> <span id="powerValue" class="value-text">0.00 mW</span></p>
            <p><span class="label-text">Region of Operation:</span> <strong id="regionValue" class="cutoff value-text">Cutoff</strong></p>
        </div>
    </div>

    <script>
        const BETA = 100;
        const VCC = 9.0;       // Volts
        const RC = 1000;     // Ohms (1kΩ)
        const VCE_SAT = 0.2; // Volts

        const ibSlider = document.getElementById('ibSlider');
        const ibValueSpan = document.getElementById('ibValue');
        const icValueSpan = document.getElementById('icValue');
        const vceValueSpan = document.getElementById('vceValue');
        const powerValueSpan = document.getElementById('powerValue');
        const regionValueStrong = document.getElementById('regionValue');

        function updateSimulation() {
            const ib_uA = parseFloat(ibSlider.value); 
            ibValueSpan.textContent = `${ib_uA} µA`;

            const ib_A = ib_uA * 1e-6; 

            let ic_A, vce_V, power_W, region;

            if (ib_A === 0) {
                ic_A = 0;
                vce_V = VCC;
                power_W = 0;
                region = "Cutoff";
            } else {
                const ic_active_A = BETA * ib_A;
                const vce_calculated_V = VCC - (ic_active_A * RC);

                if (vce_calculated_V < VCE_SAT) {
                    vce_V = VCE_SAT;
                    ic_A = (VCC - vce_V) / RC;
                    region = "Saturation";
                } else {
                    ic_A = ic_active_A;
                    vce_V = vce_calculated_V;
                    region = "Active";
                }
                power_W = vce_V * ic_A;
                // Prevent negative power due to floating point inaccuracies if Vce is extremely close to 0
                if (power_W < 0) power_W = 0; 
            }

            icValueSpan.textContent = `${(ic_A * 1000).toFixed(2)} mA`;
            vceValueSpan.textContent = `${vce_V.toFixed(2)} V`;
            powerValueSpan.textContent = `${(power_W * 1000).toFixed(2)} mW`;
            
            regionValueStrong.textContent = region;
            regionValueStrong.className = 'value-text'; // Clear previous classes, keep base class
            regionValueStrong.classList.add(region.toLowerCase());
        }

        ibSlider.addEventListener('input', updateSimulation);

        document.addEventListener('DOMContentLoaded', updateSimulation);
    </script>

</body>
</html>
