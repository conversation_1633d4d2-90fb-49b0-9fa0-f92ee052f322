document.addEventListener('DOMContentLoaded', () => {
    // Main canvas elements
    const canvas = document.getElementById('circuit-canvas');
    const ctx = canvas.getContext('2d');

    // UI elements
    const toolbar = document.getElementById('toolbar');
    const propertiesPanel = document.getElementById('properties-panel');
    const simulateButton = document.getElementById('simulate-button');
    const resetButton = document.getElementById('reset-button');
    const saveButton = document.getElementById('save-button');
    const loadButton = document.getElementById('load-button');
    const exportButton = document.getElementById('export-button');
    const loadCircuitFile = document.getElementById('load-circuit-file');

    // Grid and snapping controls
    const gridToggle = document.getElementById('grid-toggle');
    const snapToggle = document.getElementById('snap-toggle');

    // Oscilloscope elements
    const oscilloscopeCanvas = document.getElementById('oscilloscope-canvas');
    const oscilloscopeCtx = oscilloscopeCanvas ? oscilloscopeCanvas.getContext('2d') : null;
    const oscilloscopeStartBtn = document.getElementById('oscilloscope-start');
    const oscilloscopeStopBtn = document.getElementById('oscilloscope-stop');
    const oscilloscopeTimebase = document.getElementById('oscilloscope-timebase');
    const oscilloscopeVoltage = document.getElementById('oscilloscope-voltage');

    // Component library elements
    const componentSearch = document.getElementById('component-search');
    const categoryFilter = document.getElementById('category-filter');
    const libraryContent = document.querySelector('.library-content');

    // State variables
    let selectedTool = null;
    let drawing = false;
    let components = [];
    let selectedComponent = null;
    let startX, startY;
    let rotation = 0; // Current rotation angle in degrees

    // Grid settings
    const gridSize = 20; // Size of grid cells in pixels
    let showGrid = true;
    let snapToGrid = true;

    // Oscilloscope settings
    let oscilloscopeRunning = false;
    let oscilloscopeData = [];
    let oscilloscopeAnimationFrame = null;

    // --- Canvas Setup ---
    function resizeCanvas() {
        // Optional: Adjust canvas size if needed, e.g., based on container
        // For now, using fixed size from HTML
    }
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Initialize grid and snapping controls
    gridToggle.addEventListener('change', (e) => {
        showGrid = e.target.checked;
        redrawCanvas();
    });

    snapToggle.addEventListener('change', (e) => {
        snapToGrid = e.target.checked;
    });

    // Function to draw the grid
    function drawGrid() {
        if (!showGrid) return;

        const width = canvas.width;
        const height = canvas.height;

        ctx.save();
        ctx.strokeStyle = '#ddd';
        ctx.lineWidth = 0.5;

        // Draw vertical lines
        for (let x = 0; x <= width; x += gridSize) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, height);
            ctx.stroke();
        }

        // Draw horizontal lines
        for (let y = 0; y <= height; y += gridSize) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(width, y);
            ctx.stroke();
        }

        ctx.restore();
    }

    // Function to snap coordinates to grid
    function snapToGridPoint(x, y) {
        if (!snapToGrid) return { x, y };

        const snappedX = Math.round(x / gridSize) * gridSize;
        const snappedY = Math.round(y / gridSize) * gridSize;

        return { x: snappedX, y: snappedY };
    }

    // --- Tool Selection Logic ---
    toolbar.addEventListener('click', (e) => {
        if (e.target.tagName === 'BUTTON') {
            const tool = e.target.id;
            // Deselect previous tool button
            const activeButton = toolbar.querySelector('.active');
            if (activeButton) {
                activeButton.classList.remove('active');
            }

            // Select new tool
            if (selectedTool === tool) {
                selectedTool = null; // Toggle off
                canvas.style.cursor = 'crosshair';
            } else {
                selectedTool = tool;
                e.target.classList.add('active');
                // Change cursor based on tool
                if (tool === 'tool-wire') {
                    canvas.style.cursor = 'pointer'; // Or a specific wire cursor
                } else if (tool === 'tool-select') {
                    canvas.style.cursor = 'default';
                } else if (tool === 'tool-delete') {
                    canvas.style.cursor = 'not-allowed'; // Or a specific delete cursor
                } else if (tool === 'tool-rotate') {
                    canvas.style.cursor = 'url("images/rotate-cursor.png"), auto';
                } else {
                    canvas.style.cursor = 'cell'; // For placing components
                }
            }
            console.log('Selected tool:', selectedTool);
            clearSelection();
        }
    });

    // --- Drawing Logic ---
    canvas.addEventListener('mousedown', (e) => {
        const rect = canvas.getBoundingClientRect();
        startX = e.clientX - rect.left;
        startY = e.clientY - rect.top;

        // Apply grid snapping if enabled
        if (snapToGrid) {
            const snapped = snapToGridPoint(startX, startY);
            startX = snapped.x;
            startY = snapped.y;
        }

        drawing = true;

        if (selectedTool && selectedTool.startsWith('tool-') &&
            selectedTool !== 'tool-select' &&
            selectedTool !== 'tool-wire' &&
            selectedTool !== 'tool-delete' &&
            selectedTool !== 'tool-rotate') {
            // Place component
            const componentType = selectedTool.replace('tool-', '');
            addComponent(componentType, startX, startY);
        } else if (selectedTool === 'tool-select') {
            selectComponent(startX, startY);
        } else if (selectedTool === 'tool-delete') {
            deleteComponent(startX, startY);
        } else if (selectedTool === 'tool-rotate' && selectedComponent) {
            // Rotate the selected component by 90 degrees
            if (!selectedComponent.rotation) {
                selectedComponent.rotation = 0;
            }
            selectedComponent.rotation = (selectedComponent.rotation + 90) % 360;
            redrawCanvas();
        }
        // Wire drawing will be more complex (mousedown, mousemove, mouseup)
    });

    canvas.addEventListener('mousemove', (e) => {
        if (!drawing) return;
        const rect = canvas.getBoundingClientRect();
        let currentX = e.clientX - rect.left;
        let currentY = e.clientY - rect.top;

        // Apply grid snapping if enabled
        if (snapToGrid) {
            const snapped = snapToGridPoint(currentX, currentY);
            currentX = snapped.x;
            currentY = snapped.y;
        }

        // Placeholder for wire drawing or component dragging
        if (selectedTool === 'tool-wire' && drawing) {
            // Preview wire drawing
            redrawCanvas();
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(currentX, currentY);
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 2;
            ctx.stroke();
        } else if (selectedTool === 'tool-select' && selectedComponent && drawing) {
            // Drag the selected component
            selectedComponent.x = currentX;
            selectedComponent.y = currentY;
            redrawCanvas();
        }
    });

    canvas.addEventListener('mouseup', (e) => {
        drawing = false;
        const rect = canvas.getBoundingClientRect();
        let endX = e.clientX - rect.left;
        let endY = e.clientY - rect.top;

        // Apply grid snapping if enabled
        if (snapToGrid) {
            const snapped = snapToGridPoint(endX, endY);
            endX = snapped.x;
            endY = snapped.y;
        }

        if (selectedTool === 'tool-wire' && startX !== endX && startY !== endY) {
            addComponent('wire', startX, startY, endX, endY);
        }
        // Finalize component placement or selection if needed
    });

    // --- Component Management ---
    function addComponent(type, x, y, x2, y2) {
        const component = {
            id: Date.now(), // Simple unique ID
            type: type,
            x: x,
            y: y,
            rotation: 0, // Default rotation angle
            properties: getDefaultProperties(type)
        };
        if (type === 'wire') {
            component.x2 = x2;
            component.y2 = y2;
        }
        components.push(component);
        console.log('Added component:', component);
        redrawCanvas();

        // Select the newly added component
        selectedComponent = component;
        updatePropertiesPanel();
    }

    function selectComponent(x, y) {
        selectedComponent = null;
        // Find the topmost component at (x,y)
        for (let i = components.length - 1; i >= 0; i--) {
            const comp = components[i];
            // Basic bounding box check (can be more precise for different shapes)
            const size = getComponentSize(comp.type);
            if (comp.type === 'wire') {
                // Simplified wire selection: check distance to line segment
                if (isPointOnLine(x, y, comp.x, comp.y, comp.x2, comp.y2, 5)) {
                    selectedComponent = comp;
                    break;
                }
            } else {
                if (x >= comp.x - size.width / 2 && x <= comp.x + size.width / 2 &&
                    y >= comp.y - size.height / 2 && y <= comp.y + size.height / 2) {
                    selectedComponent = comp;
                    break;
                }
            }
        }
        updatePropertiesPanel();
        redrawCanvas(); // To highlight selected component
    }

    function deleteComponent(x,y){
        let componentToDelete = null;
        let indexToDelete = -1;
        for (let i = components.length - 1; i >= 0; i--) {
            const comp = components[i];
            const size = getComponentSize(comp.type);
            if (comp.type === 'wire') {
                if (isPointOnLine(x, y, comp.x, comp.y, comp.x2, comp.y2, 5)) {
                    componentToDelete = comp;
                    indexToDelete = i;
                    break;
                }
            } else {
                if (x >= comp.x - size.width / 2 && x <= comp.x + size.width / 2 &&
                    y >= comp.y - size.height / 2 && y <= comp.y + size.height / 2) {
                    componentToDelete = comp;
                    indexToDelete = i;
                    break;
                }
            }
        }
        if(componentToDelete){
            components.splice(indexToDelete, 1);
            clearSelection();
            redrawCanvas();
        }
    }

    function clearSelection(){
        selectedComponent = null;
        updatePropertiesPanel();
        redrawCanvas();
    }

    function getComponentSize(type) {
        // Component sizes
        switch (type) {
            case 'resistor': return { width: 60, height: 20 };
            case 'capacitor': return { width: 40, height: 40 };
            case 'inductor': return { width: 50, height: 20 };
            case 'transistor-npn':
            case 'transistor-pnp': return { width: 50, height: 60 };
            case 'voltage-source': return { width: 40, height: 40 };
            case 'ground': return { width: 30, height: 20 };
            case 'diode': return { width: 40, height: 20 };
            case 'led': return { width: 40, height: 30 };
            case 'switch': return { width: 50, height: 20 };
            case 'op-amp': return { width: 60, height: 50 };
            case 'logic-and':
            case 'logic-or':
            case 'logic-not': return { width: 60, height: 40 };
            case 'current-source': return { width: 40, height: 40 };
            case 'ammeter': return { width: 40, height: 40 };
            case 'voltmeter': return { width: 40, height: 40 };
            default: return { width: 20, height: 20 };
        }
    }

    function getDefaultProperties(type) {
        switch (type) {
            case 'resistor': return { resistance: '1kΩ', power: '0.25W' };
            case 'capacitor': return { capacitance: '1µF', voltage_rating: '50V' };
            case 'inductor': return { inductance: '1mH', current_rating: '100mA' };
            case 'voltage-source': return { voltage: '5V', internal_resistance: '0.1Ω' };
            case 'transistor-npn':
            case 'transistor-pnp': return { model: 'Generic', beta: 100, vce_max: '40V', ic_max: '100mA' };
            case 'diode': return { model: '1N4148', forward_voltage: '0.7V', max_current: '200mA' };
            case 'led': return { color: 'red', forward_voltage: '1.8V', max_current: '20mA' };
            case 'switch': return { state: 'open', type: 'SPST' };
            case 'op-amp': return { model: 'LM741', gain: '100000', input_impedance: '2MΩ', output_impedance: '75Ω' };
            case 'logic-and':
            case 'logic-or':
            case 'logic-not': return { family: 'TTL', inputs: 2 };
            case 'current-source': return { current: '1mA' };
            case 'ammeter': return { range: 'auto', internal_resistance: '0.1Ω' };
            case 'voltmeter': return { range: 'auto', internal_resistance: '10MΩ' };
            case 'wire': return { resistance: '0Ω' };
            case 'ground': return { type: 'earth' };
            default: return {};
        }
    }

    // --- Drawing Components on Canvas ---
    function redrawCanvas() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Draw grid first (if enabled)
        drawGrid();

        // Draw all components
        components.forEach(comp => {
            drawComponent(comp);
        });

        // Highlight selected component
        if (selectedComponent) {
            highlightComponent(selectedComponent);
        }
    }

    function drawComponent(comp) {
        ctx.save();
        ctx.translate(comp.x, comp.y);

        // Apply rotation if component has rotation property
        if (comp.rotation) {
            const rotationRad = (comp.rotation * Math.PI) / 180;
            ctx.rotate(rotationRad);
        }

        // Component drawing
        const size = getComponentSize(comp.type);
        ctx.fillStyle = '#ddd';
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 2;

        switch (comp.type) {
            case 'resistor':
                // Draw resistor (zigzag symbol)
                ctx.beginPath();
                const zigWidth = size.width / 6;
                ctx.moveTo(-size.width / 2, 0);
                ctx.lineTo(-size.width / 2 + zigWidth, 0);

                // Draw zigzag pattern
                for (let i = 0; i < 5; i++) {
                    const x = -size.width / 2 + zigWidth + i * zigWidth;
                    ctx.lineTo(x, (i % 2 === 0) ? -size.height / 2 : size.height / 2);
                }

                ctx.lineTo(size.width / 2, 0);
                ctx.stroke();
                ctx.fillStyle = '#000';
                ctx.font = '10px Arial';
                ctx.fillText('R', -5, -10);
                break;

            case 'capacitor':
                // Draw capacitor (two parallel plates)
                ctx.beginPath();
                // Left wire
                ctx.moveTo(-size.width / 2, 0);
                ctx.lineTo(-5, 0);
                // Left plate
                ctx.moveTo(-5, -size.height / 3);
                ctx.lineTo(-5, size.height / 3);
                // Right plate
                ctx.moveTo(5, -size.height / 3);
                ctx.lineTo(5, size.height / 3);
                // Right wire
                ctx.moveTo(5, 0);
                ctx.lineTo(size.width / 2, 0);
                ctx.stroke();
                ctx.fillStyle = '#000';
                ctx.font = '10px Arial';
                ctx.fillText('C', -5, -10);
                break;

            case 'inductor':
                // Draw inductor (coil symbol)
                ctx.beginPath();
                // Left wire
                ctx.moveTo(-size.width / 2, 0);
                ctx.lineTo(-size.width / 3, 0);

                // Draw coil loops
                const loopWidth = size.width / 6;
                const loopHeight = size.height / 2;
                const loops = 3;

                for (let i = 0; i < loops; i++) {
                    const x = -size.width / 3 + i * loopWidth;
                    ctx.arc(x, 0, loopHeight / 2, Math.PI, 0, false);
                }

                // Right wire
                ctx.moveTo(-size.width / 3 + loops * loopWidth, 0);
                ctx.lineTo(size.width / 2, 0);
                ctx.stroke();
                ctx.fillStyle = '#000';
                ctx.font = '10px Arial';
                ctx.fillText('L', -5, -10);
                break;

            case 'transistor-npn':
                drawNPN(ctx, size);
                break;

            case 'transistor-pnp':
                drawPNP(ctx, size);
                break;

            case 'voltage-source':
                // Draw voltage source (circle with + and - symbols)
                ctx.beginPath();
                ctx.arc(0, 0, size.width / 3, 0, 2 * Math.PI);
                // Draw + and - symbols
                ctx.moveTo(-size.width / 8, 0);
                ctx.lineTo(size.width / 8, 0);
                ctx.moveTo(0, -size.height / 8);
                ctx.lineTo(0, size.height / 8);
                // Draw connecting wires
                ctx.moveTo(0, -size.width / 3);
                ctx.lineTo(0, -size.height / 2);
                ctx.moveTo(0, size.width / 3);
                ctx.lineTo(0, size.height / 2);
                ctx.stroke();
                ctx.fillStyle = '#000';
                ctx.font = '10px Arial';
                ctx.fillText('V', -15, 0);
                break;

            case 'ground':
                // Draw ground symbol
                ctx.beginPath();
                ctx.moveTo(0, -size.height / 2);
                ctx.lineTo(0, 0);
                ctx.moveTo(-size.width/2, 0);
                ctx.lineTo(size.width/2, 0);
                ctx.moveTo(-size.width/2 + 5, 5);
                ctx.lineTo(size.width/2 - 5, 5);
                ctx.moveTo(-size.width/2 + 10, 10);
                ctx.lineTo(size.width/2 - 10, 10);
                ctx.stroke();
                break;

            case 'diode':
                // Draw diode (triangle with line)
                ctx.beginPath();
                // Left wire
                ctx.moveTo(-size.width / 2, 0);
                ctx.lineTo(-size.width / 4, 0);
                // Triangle
                ctx.lineTo(-size.width / 4, 0);
                ctx.lineTo(size.width / 4, -size.height / 2);
                ctx.lineTo(size.width / 4, size.height / 2);
                ctx.lineTo(-size.width / 4, 0);
                ctx.fill();
                // Line (cathode)
                ctx.beginPath();
                ctx.moveTo(size.width / 4, -size.height / 2);
                ctx.lineTo(size.width / 4, size.height / 2);
                ctx.stroke();
                // Right wire
                ctx.beginPath();
                ctx.moveTo(size.width / 4, 0);
                ctx.lineTo(size.width / 2, 0);
                ctx.stroke();
                break;

            case 'led':
                // Draw LED (diode with arrows for light)
                ctx.beginPath();
                // Left wire
                ctx.moveTo(-size.width / 2, 0);
                ctx.lineTo(-size.width / 4, 0);
                // Triangle
                ctx.lineTo(-size.width / 4, 0);
                ctx.lineTo(size.width / 4, -size.height / 2);
                ctx.lineTo(size.width / 4, size.height / 2);
                ctx.lineTo(-size.width / 4, 0);
                ctx.fill();
                // Line (cathode)
                ctx.beginPath();
                ctx.moveTo(size.width / 4, -size.height / 2);
                ctx.lineTo(size.width / 4, size.height / 2);
                ctx.stroke();
                // Right wire
                ctx.beginPath();
                ctx.moveTo(size.width / 4, 0);
                ctx.lineTo(size.width / 2, 0);
                ctx.stroke();

                // Draw light arrows
                ctx.beginPath();
                // First arrow
                ctx.moveTo(0, -size.height / 2 - 5);
                ctx.lineTo(size.width / 4, -size.height / 2 - 15);
                ctx.lineTo(size.width / 4 - 5, -size.height / 2 - 15);
                ctx.moveTo(size.width / 4, -size.height / 2 - 15);
                ctx.lineTo(size.width / 4 - 3, -size.height / 2 - 10);

                // Second arrow
                ctx.moveTo(size.width / 4 + 5, -size.height / 2 - 5);
                ctx.lineTo(size.width / 4 + 15, -size.height / 2 - 15);
                ctx.lineTo(size.width / 4 + 10, -size.height / 2 - 15);
                ctx.moveTo(size.width / 4 + 15, -size.height / 2 - 15);
                ctx.lineTo(size.width / 4 + 12, -size.height / 2 - 10);
                ctx.stroke();
                break;

            case 'switch':
                // Draw switch
                ctx.beginPath();
                // Left terminal
                ctx.arc(-size.width / 3, 0, 3, 0, 2 * Math.PI);
                ctx.fill();
                // Right terminal
                ctx.arc(size.width / 3, 0, 3, 0, 2 * Math.PI);
                ctx.fill();

                // Switch lever (open position by default)
                ctx.beginPath();
                if (comp.properties && comp.properties.state === 'closed') {
                    // Closed position
                    ctx.moveTo(-size.width / 3, 0);
                    ctx.lineTo(size.width / 3, 0);
                } else {
                    // Open position
                    ctx.moveTo(-size.width / 3, 0);
                    ctx.lineTo(size.width / 5, -size.height / 2);
                }
                ctx.stroke();

                // Connecting wires
                ctx.beginPath();
                ctx.moveTo(-size.width / 2, 0);
                ctx.lineTo(-size.width / 3, 0);
                ctx.moveTo(size.width / 3, 0);
                ctx.lineTo(size.width / 2, 0);
                ctx.stroke();
                break;

            case 'op-amp':
                // Draw op-amp (triangle)
                ctx.beginPath();
                ctx.moveTo(-size.width / 2, -size.height / 2);
                ctx.lineTo(-size.width / 2, size.height / 2);
                ctx.lineTo(size.width / 2, 0);
                ctx.closePath();
                ctx.stroke();

                // Input terminals
                ctx.beginPath();
                // Non-inverting input (+)
                ctx.moveTo(-size.width / 2, -size.height / 4);
                ctx.lineTo(-size.width / 2 - 10, -size.height / 4);
                // + symbol
                ctx.moveTo(-size.width / 2 + 10, -size.height / 4);
                ctx.lineTo(-size.width / 2 + 20, -size.height / 4);
                ctx.moveTo(-size.width / 2 + 15, -size.height / 4 - 5);
                ctx.lineTo(-size.width / 2 + 15, -size.height / 4 + 5);

                // Inverting input (-)
                ctx.moveTo(-size.width / 2, size.height / 4);
                ctx.lineTo(-size.width / 2 - 10, size.height / 4);
                // - symbol
                ctx.moveTo(-size.width / 2 + 10, size.height / 4);
                ctx.lineTo(-size.width / 2 + 20, size.height / 4);

                // Output
                ctx.moveTo(size.width / 2, 0);
                ctx.lineTo(size.width / 2 + 10, 0);
                ctx.stroke();
                break;

            case 'logic-and':
                // Draw AND gate
                ctx.beginPath();
                ctx.moveTo(-size.width / 2, -size.height / 2);
                ctx.lineTo(-size.width / 4, -size.height / 2);
                ctx.arc(0, 0, size.width / 3, -Math.PI / 2, Math.PI / 2, false);
                ctx.lineTo(-size.width / 4, size.height / 2);
                ctx.lineTo(-size.width / 2, size.height / 2);
                ctx.closePath();
                ctx.stroke();

                // Input terminals
                ctx.beginPath();
                ctx.moveTo(-size.width / 2, -size.height / 4);
                ctx.lineTo(-size.width / 2 - 10, -size.height / 4);
                ctx.moveTo(-size.width / 2, size.height / 4);
                ctx.lineTo(-size.width / 2 - 10, size.height / 4);

                // Output
                ctx.moveTo(size.width / 3, 0);
                ctx.lineTo(size.width / 2 + 10, 0);
                ctx.stroke();

                ctx.fillStyle = '#000';
                ctx.font = '12px Arial';
                ctx.fillText('&', -size.width / 4, 5);
                break;

            case 'logic-or':
                // Draw OR gate
                ctx.beginPath();
                ctx.moveTo(-size.width / 2, -size.height / 2);
                ctx.quadraticCurveTo(0, 0, -size.width / 2, size.height / 2);
                ctx.quadraticCurveTo(size.width / 4, 0, size.width / 3, 0);
                ctx.quadraticCurveTo(size.width / 4, 0, -size.width / 2, -size.height / 2);
                ctx.stroke();

                // Input terminals
                ctx.beginPath();
                ctx.moveTo(-size.width / 2, -size.height / 4);
                ctx.lineTo(-size.width / 2 - 10, -size.height / 4);
                ctx.moveTo(-size.width / 2, size.height / 4);
                ctx.lineTo(-size.width / 2 - 10, size.height / 4);

                // Output
                ctx.moveTo(size.width / 3, 0);
                ctx.lineTo(size.width / 2 + 10, 0);
                ctx.stroke();

                ctx.fillStyle = '#000';
                ctx.font = '12px Arial';
                ctx.fillText('≥1', -size.width / 4, 5);
                break;

            case 'logic-not':
                // Draw NOT gate (inverter)
                ctx.beginPath();
                ctx.moveTo(-size.width / 2, -size.height / 2);
                ctx.lineTo(-size.width / 2, size.height / 2);
                ctx.lineTo(size.width / 3 - 10, 0);
                ctx.closePath();
                ctx.stroke();

                // Bubble at output
                ctx.beginPath();
                ctx.arc(size.width / 3, 0, 5, 0, 2 * Math.PI);
                ctx.stroke();

                // Input terminal
                ctx.beginPath();
                ctx.moveTo(-size.width / 2, 0);
                ctx.lineTo(-size.width / 2 - 10, 0);

                // Output
                ctx.moveTo(size.width / 3 + 5, 0);
                ctx.lineTo(size.width / 2 + 10, 0);
                ctx.stroke();

                ctx.fillStyle = '#000';
                ctx.font = '12px Arial';
                ctx.fillText('1', -size.width / 4, 5);
                break;

            case 'current-source':
                // Draw current source (circle with arrow)
                ctx.beginPath();
                ctx.arc(0, 0, size.width / 3, 0, 2 * Math.PI);
                ctx.stroke();

                // Draw arrow inside
                ctx.beginPath();
                ctx.moveTo(0, -size.height / 6);
                ctx.lineTo(0, size.height / 6);
                ctx.lineTo(-5, size.height / 6 - 5);
                ctx.moveTo(0, size.height / 6);
                ctx.lineTo(5, size.height / 6 - 5);
                ctx.stroke();

                // Draw connecting wires
                ctx.beginPath();
                ctx.moveTo(0, -size.width / 3);
                ctx.lineTo(0, -size.height / 2);
                ctx.moveTo(0, size.width / 3);
                ctx.lineTo(0, size.height / 2);
                ctx.stroke();

                ctx.fillStyle = '#000';
                ctx.font = '10px Arial';
                ctx.fillText('I', -15, 0);
                break;

            case 'ammeter':
                // Draw ammeter (circle with 'A')
                ctx.beginPath();
                ctx.arc(0, 0, size.width / 3, 0, 2 * Math.PI);
                ctx.stroke();

                // Draw connecting wires
                ctx.beginPath();
                ctx.moveTo(0, -size.width / 3);
                ctx.lineTo(0, -size.height / 2);
                ctx.moveTo(0, size.width / 3);
                ctx.lineTo(0, size.height / 2);
                ctx.stroke();

                ctx.fillStyle = '#000';
                ctx.font = '14px Arial';
                ctx.fillText('A', -5, 5);
                break;

            case 'voltmeter':
                // Draw voltmeter (circle with 'V')
                ctx.beginPath();
                ctx.arc(0, 0, size.width / 3, 0, 2 * Math.PI);
                ctx.stroke();

                // Draw connecting wires
                ctx.beginPath();
                ctx.moveTo(0, -size.width / 3);
                ctx.lineTo(0, -size.height / 2);
                ctx.moveTo(0, size.width / 3);
                ctx.lineTo(0, size.height / 2);
                ctx.stroke();

                ctx.fillStyle = '#000';
                ctx.font = '14px Arial';
                ctx.fillText('V', -5, 5);
                break;

            case 'wire':
                ctx.restore(); // Wires are drawn in absolute coords
                ctx.save();
                ctx.beginPath();
                ctx.moveTo(comp.x, comp.y);
                ctx.lineTo(comp.x2, comp.y2);
                ctx.strokeStyle = selectedComponent === comp ? '#007bff' : '#000';
                ctx.lineWidth = 2;
                ctx.stroke();
                break;

            default:
                ctx.fillRect(-size.width / 2, -size.height / 2, size.width, size.height);
                ctx.strokeRect(-size.width / 2, -size.height / 2, size.width, size.height);
                ctx.fillStyle = '#000';
                ctx.fillText(comp.type.substring(0,1).toUpperCase(), -5, 5);
        }
        ctx.restore();
    }

    function drawNPN(ctx, size) {
        const baseRelX = 0;
        const baseRelY = 0;
        const collectorRelX = 0;
        const collectorRelY = -size.height / 2;
        const emitterRelX = 0;
        const emitterRelY = size.height / 2;

        ctx.beginPath();
        // Base wire
        ctx.moveTo(baseRelX - size.width / 4, baseRelY);
        ctx.lineTo(baseRelX, baseRelY);
        // Collector wire
        ctx.moveTo(baseRelX, baseRelY);
        ctx.lineTo(collectorRelX, collectorRelY);
        // Emitter wire
        ctx.moveTo(baseRelX, baseRelY);
        ctx.lineTo(emitterRelX, emitterRelY);
        // Vertical bar for B-C-E junction
        ctx.moveTo(baseRelX, baseRelY - size.height/4);
        ctx.lineTo(baseRelX, baseRelY + size.height/4);
        ctx.stroke();
        // Arrow for NPN (on emitter, pointing out)
        const arrowLength = 10;
        const angle = Math.atan2(emitterRelY - baseRelY, emitterRelX - baseRelX);
        ctx.moveTo(emitterRelX - arrowLength * Math.cos(angle - Math.PI / 6), emitterRelY - arrowLength * Math.sin(angle - Math.PI / 6));
        ctx.lineTo(emitterRelX, emitterRelY);
        ctx.lineTo(emitterRelX - arrowLength * Math.cos(angle + Math.PI / 6), emitterRelY - arrowLength * Math.sin(angle + Math.PI / 6));
        ctx.stroke();
        ctx.fillText('NPN', -15, baseRelY + size.height/2 + 15);
    }

    function drawPNP(ctx, size) {
        const baseRelX = 0;
        const baseRelY = 0;
        const collectorRelX = 0;
        const collectorRelY = -size.height / 2;
        const emitterRelX = 0;
        const emitterRelY = size.height / 2;

        ctx.beginPath();
        // Base wire
        ctx.moveTo(baseRelX - size.width / 4, baseRelY);
        ctx.lineTo(baseRelX, baseRelY);
        // Collector wire
        ctx.moveTo(baseRelX, baseRelY);
        ctx.lineTo(collectorRelX, collectorRelY);
        // Emitter wire
        ctx.moveTo(baseRelX, baseRelY);
        ctx.lineTo(emitterRelX, emitterRelY);
        // Vertical bar for B-C-E junction
        ctx.moveTo(baseRelX, baseRelY - size.height/4);
        ctx.lineTo(baseRelX, baseRelY + size.height/4);
        ctx.stroke();
        // Arrow for PNP (on emitter, pointing in)
        const arrowLength = 10;
        const midEmitterX = (baseRelX + emitterRelX) / 2 + 2;
        const midEmitterY = (baseRelY + emitterRelY) / 2 + 2;
        const angle = Math.atan2(baseRelY - emitterRelY, baseRelX - emitterRelX);
        ctx.moveTo(midEmitterX - arrowLength * Math.cos(angle - Math.PI / 6), midEmitterY - arrowLength * Math.sin(angle - Math.PI / 6));
        ctx.lineTo(midEmitterX, midEmitterY);
        ctx.lineTo(midEmitterX - arrowLength * Math.cos(angle + Math.PI / 6), midEmitterY - arrowLength * Math.sin(angle + Math.PI / 6));
        ctx.stroke();
        ctx.fillText('PNP', -15, baseRelY + size.height/2 + 15);
    }

    function highlightComponent(comp) {
        ctx.save();
        ctx.strokeStyle = '#007bff'; // Blue highlight
        ctx.lineWidth = 3;
        const size = getComponentSize(comp.type);
        if (comp.type === 'wire') {
            ctx.beginPath();
            ctx.moveTo(comp.x, comp.y);
            ctx.lineTo(comp.x2, comp.y2);
            ctx.stroke();
        } else {
            ctx.strokeRect(comp.x - size.width / 2 - 2, comp.y - size.height / 2 - 2, size.width + 4, size.height + 4);
        }
        ctx.restore();
    }

    // --- Properties Panel Logic ---
    function updatePropertiesPanel() {
        propertiesPanel.innerHTML = '<h3>خصائص العنصر</h3>';
        if (selectedComponent) {
            const props = selectedComponent.properties;
            for (const key in props) {
                const propDiv = document.createElement('div');
                propDiv.classList.add('property-item');
                const label = document.createElement('label');
                label.textContent = `${key}: `;
                const input = document.createElement('input');
                input.type = 'text';
                input.value = props[key];
                input.dataset.key = key;
                input.addEventListener('change', (e) => {
                    selectedComponent.properties[e.target.dataset.key] = e.target.value;
                    console.log('Updated properties:', selectedComponent.properties);
                    // Potentially redraw or re-simulate if properties affect appearance/behavior
                    redrawCanvas();
                });
                propDiv.appendChild(label);
                propDiv.appendChild(input);
                propertiesPanel.appendChild(propDiv);
            }
        } else {
            propertiesPanel.innerHTML += '<p>لم يتم تحديد أي عنصر.</p>';
        }
    }

    // --- Simulation Logic ---
    simulateButton.addEventListener('click', () => {
        if (components.length === 0) {
            alert('لا توجد عناصر في الدائرة للمحاكاة.');
            return;
        }

        // Validate circuit before simulation
        const validationResult = validateCircuit();
        if (!validationResult.valid) {
            alert('خطأ في الدائرة: ' + validationResult.message);
            return;
        }

        // Perform basic simulation
        const simulationResults = simulateCircuit();
        displaySimulationResults(simulationResults);
    });

    function validateCircuit() {
        // Basic circuit validation

        // Check if there are any components
        if (components.length === 0) {
            return { valid: false, message: 'لا توجد عناصر في الدائرة.' };
        }

        // Check if there's at least one source (voltage or current)
        const hasPowerSource = components.some(comp =>
            comp.type === 'voltage-source' || comp.type === 'current-source');

        if (!hasPowerSource) {
            return { valid: false, message: 'الدائرة تحتاج إلى مصدر طاقة (جهد أو تيار).' };
        }

        // Check if there's at least one ground
        const hasGround = components.some(comp => comp.type === 'ground');

        if (!hasGround) {
            return { valid: false, message: 'الدائرة تحتاج إلى نقطة أرضي.' };
        }

        // Check for floating components (not connected to anything)
        const connectedPoints = new Set();

        // Add all wire endpoints to the set of connected points
        components.filter(comp => comp.type === 'wire').forEach(wire => {
            connectedPoints.add(`${Math.round(wire.x)},${Math.round(wire.y)}`);
            connectedPoints.add(`${Math.round(wire.x2)},${Math.round(wire.y2)}`);
        });

        // Check if each component has at least one connection
        for (const comp of components) {
            if (comp.type === 'wire') continue; // Skip wires

            const compX = Math.round(comp.x);
            const compY = Math.round(comp.y);
            const size = getComponentSize(comp.type);

            // Check if any wire connects to this component
            const isConnected = Array.from(connectedPoints).some(point => {
                const [x, y] = point.split(',').map(Number);
                return (
                    x >= compX - size.width / 2 && x <= compX + size.width / 2 &&
                    y >= compY - size.height / 2 && y <= compY + size.height / 2
                );
            });

            if (!isConnected) {
                return {
                    valid: false,
                    message: `العنصر من نوع ${comp.type} في الموقع (${compX}, ${compY}) غير متصل بأي سلك.`
                };
            }
        }

        return { valid: true, message: 'الدائرة صالحة للمحاكاة.' };
    }

    function simulateCircuit() {
        // This is a simplified simulation that calculates basic values
        // A real circuit simulator would use more complex algorithms like SPICE

        const results = {
            voltages: {},
            currents: {},
            powers: {}
        };

        // Find voltage sources
        const voltageSources = components.filter(comp => comp.type === 'voltage-source');

        // Find resistors
        const resistors = components.filter(comp => comp.type === 'resistor');

        // Simple voltage divider calculation for demonstration
        // This is a very simplified model and only works for basic circuits
        if (voltageSources.length === 1 && resistors.length > 0) {
            const source = voltageSources[0];
            const sourceVoltage = parseFloat(source.properties.voltage) || 5; // Default to 5V

            // Calculate total resistance
            let totalResistance = 0;
            resistors.forEach(resistor => {
                // Parse resistance value (remove units like kΩ)
                let resistance = resistor.properties.resistance;
                if (typeof resistance === 'string') {
                    resistance = resistance.replace(/[^\d.]/g, '');
                    if (resistor.properties.resistance.includes('k')) {
                        resistance *= 1000;
                    } else if (resistor.properties.resistance.includes('M')) {
                        resistance *= 1000000;
                    }
                }
                totalResistance += parseFloat(resistance) || 1000; // Default to 1kΩ
            });

            // Calculate current through the circuit (I = V/R)
            const totalCurrent = sourceVoltage / totalResistance;

            // Calculate voltage across each resistor (V = IR)
            resistors.forEach((resistor, index) => {
                let resistance = resistor.properties.resistance;
                if (typeof resistance === 'string') {
                    resistance = resistance.replace(/[^\d.]/g, '');
                    if (resistor.properties.resistance.includes('k')) {
                        resistance *= 1000;
                    } else if (resistor.properties.resistance.includes('M')) {
                        resistance *= 1000000;
                    }
                }
                resistance = parseFloat(resistance) || 1000;

                const voltage = totalCurrent * resistance;
                const power = voltage * totalCurrent;

                results.voltages[`resistor_${index}`] = voltage.toFixed(2) + 'V';
                results.currents[`resistor_${index}`] = totalCurrent.toFixed(3) + 'A';
                results.powers[`resistor_${index}`] = power.toFixed(2) + 'W';
            });

            // Source current and power
            results.currents['source'] = totalCurrent.toFixed(3) + 'A';
            results.powers['source'] = (sourceVoltage * totalCurrent).toFixed(2) + 'W';
        }

        return results;
    }

    function displaySimulationResults(results) {
        // Create a modal to display simulation results
        const modal = document.createElement('div');
        modal.className = 'simulation-modal';

        const modalContent = document.createElement('div');
        modalContent.className = 'simulation-modal-content';

        const closeBtn = document.createElement('span');
        closeBtn.className = 'close-button';
        closeBtn.innerHTML = '&times;';
        closeBtn.onclick = function() {
            document.body.removeChild(modal);
        };

        const title = document.createElement('h2');
        title.textContent = 'نتائج المحاكاة';

        const resultsDiv = document.createElement('div');
        resultsDiv.className = 'simulation-results';

        // Add voltage results
        if (Object.keys(results.voltages).length > 0) {
            const voltagesTitle = document.createElement('h3');
            voltagesTitle.textContent = 'الجهود';
            resultsDiv.appendChild(voltagesTitle);

            const voltagesList = document.createElement('ul');
            for (const [component, voltage] of Object.entries(results.voltages)) {
                const item = document.createElement('li');
                item.textContent = `${component}: ${voltage}`;
                voltagesList.appendChild(item);
            }
            resultsDiv.appendChild(voltagesList);
        }

        // Add current results
        if (Object.keys(results.currents).length > 0) {
            const currentsTitle = document.createElement('h3');
            currentsTitle.textContent = 'التيارات';
            resultsDiv.appendChild(currentsTitle);

            const currentsList = document.createElement('ul');
            for (const [component, current] of Object.entries(results.currents)) {
                const item = document.createElement('li');
                item.textContent = `${component}: ${current}`;
                currentsList.appendChild(item);
            }
            resultsDiv.appendChild(currentsList);
        }

        // Add power results
        if (Object.keys(results.powers).length > 0) {
            const powersTitle = document.createElement('h3');
            powersTitle.textContent = 'القدرات';
            resultsDiv.appendChild(powersTitle);

            const powersList = document.createElement('ul');
            for (const [component, power] of Object.entries(results.powers)) {
                const item = document.createElement('li');
                item.textContent = `${component}: ${power}`;
                powersList.appendChild(item);
            }
            resultsDiv.appendChild(powersList);
        }

        // Add a note about the simulation limitations
        const note = document.createElement('p');
        note.className = 'simulation-note';
        note.textContent = 'ملاحظة: هذه محاكاة مبسطة للدائرة. للحصول على نتائج أكثر دقة، يرجى استخدام برامج محاكاة متخصصة.';

        modalContent.appendChild(closeBtn);
        modalContent.appendChild(title);
        modalContent.appendChild(resultsDiv);
        modalContent.appendChild(note);
        modal.appendChild(modalContent);

        document.body.appendChild(modal);
    }

    resetButton.addEventListener('click', () => {
        components = [];
        selectedComponent = null;
        selectedTool = null;
        const activeButton = toolbar.querySelector('.active');
        if (activeButton) activeButton.classList.remove('active');
        canvas.style.cursor = 'crosshair';
        redrawCanvas();
        updatePropertiesPanel();
        alert('تمت إعادة تعيين مساحة العمل.');
    });

    // --- Save/Load/Export Functionality ---

    // Save circuit to JSON file
    saveButton.addEventListener('click', () => {
        if (components.length === 0) {
            alert('لا توجد عناصر للحفظ. قم بإنشاء دائرة أولاً.');
            return;
        }

        const circuitData = {
            components: components,
            canvasWidth: canvas.width,
            canvasHeight: canvas.height,
            timestamp: new Date().toISOString()
        };

        const dataStr = JSON.stringify(circuitData, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

        const exportName = 'circuit_' + new Date().toISOString().slice(0, 10) + '.json';

        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportName);
        linkElement.style.display = 'none';
        document.body.appendChild(linkElement);
        linkElement.click();
        document.body.removeChild(linkElement);
    });

    // Load circuit from JSON file
    loadButton.addEventListener('click', () => {
        loadCircuitFile.click();
    });

    loadCircuitFile.addEventListener('change', (event) => {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const circuitData = JSON.parse(e.target.result);

                // Validate the loaded data
                if (!circuitData.components || !Array.isArray(circuitData.components)) {
                    throw new Error('Invalid circuit data format');
                }

                // Load the components
                components = circuitData.components;

                // Reset selection
                selectedComponent = null;
                selectedTool = null;
                const activeButton = toolbar.querySelector('.active');
                if (activeButton) activeButton.classList.remove('active');

                // Redraw the canvas
                redrawCanvas();
                updatePropertiesPanel();

                alert('تم تحميل الدائرة بنجاح.');
            } catch (error) {
                alert('خطأ في تحميل الملف: ' + error.message);
                console.error('Error loading circuit:', error);
            }
        };
        reader.readAsText(file);

        // Reset the file input so the same file can be loaded again
        event.target.value = '';
    });

    // Export circuit as image
    exportButton.addEventListener('click', () => {
        if (components.length === 0) {
            alert('لا توجد عناصر لتصديرها. قم بإنشاء دائرة أولاً.');
            return;
        }

        // Create a temporary canvas with white background
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = canvas.width;
        tempCanvas.height = canvas.height;
        const tempCtx = tempCanvas.getContext('2d');

        // Fill with white background
        tempCtx.fillStyle = '#ffffff';
        tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

        // Draw the circuit
        tempCtx.drawImage(canvas, 0, 0);

        // Convert to image
        try {
            const dataUrl = tempCanvas.toDataURL('image/png');

            const linkElement = document.createElement('a');
            linkElement.setAttribute('href', dataUrl);
            linkElement.setAttribute('download', 'circuit_' + new Date().toISOString().slice(0, 10) + '.png');
            linkElement.style.display = 'none';
            document.body.appendChild(linkElement);
            linkElement.click();
            document.body.removeChild(linkElement);
        } catch (error) {
            alert('خطأ في تصدير الصورة: ' + error.message);
            console.error('Error exporting image:', error);
        }
    });

    // --- Utility Functions ---
    function isPointOnLine(px, py, x1, y1, x2, y2, tolerance = 5) {
        const d1 = Math.sqrt((px - x1) ** 2 + (py - y1) ** 2);
        const d2 = Math.sqrt((px - x2) ** 2 + (py - y2) ** 2);
        const lineLen = Math.sqrt((x1 - x2) ** 2 + (y1 - y2) ** 2);
        return d1 + d2 >= lineLen - tolerance && d1 + d2 <= lineLen + tolerance;
    }

    // --- Component Library Panel ---
    function initializeComponentLibrary() {
        // Define component categories
        const componentCategories = [
            {
                name: 'العناصر الأساسية',
                category: 'basic',
                components: [
                    { id: 'resistor', name: 'مقاومة', icon: 'images/components/resistor.png' },
                    { id: 'capacitor', name: 'مكثف', icon: 'images/components/capacitor.png' },
                    { id: 'inductor', name: 'ملف', icon: 'images/components/inductor.png' },
                    { id: 'diode', name: 'ثنائي', icon: 'images/components/diode.png' },
                    { id: 'led', name: 'LED', icon: 'images/components/led.png' },
                    { id: 'switch', name: 'مفتاح', icon: 'images/components/switch.png' }
                ]
            },
            {
                name: 'أشباه الموصلات',
                category: 'semiconductor',
                components: [
                    { id: 'transistor-npn', name: 'ترانزستور NPN', icon: 'images/components/transistor-npn.png' },
                    { id: 'transistor-pnp', name: 'ترانزستور PNP', icon: 'images/components/transistor-pnp.png' },
                    { id: 'op-amp', name: 'مضخم عمليات', icon: 'images/components/op-amp.png' }
                ]
            },
            {
                name: 'البوابات المنطقية',
                category: 'logic',
                components: [
                    { id: 'logic-and', name: 'AND', icon: 'images/components/logic-and.png' },
                    { id: 'logic-or', name: 'OR', icon: 'images/components/logic-or.png' },
                    { id: 'logic-not', name: 'NOT', icon: 'images/components/logic-not.png' }
                ]
            },
            {
                name: 'المصادر والقياس',
                category: 'sources',
                components: [
                    { id: 'voltage-source', name: 'مصدر جهد', icon: 'images/components/voltage-source.png' },
                    { id: 'current-source', name: 'مصدر تيار', icon: 'images/components/current-source.png' },
                    { id: 'ground', name: 'أرضي', icon: 'images/components/ground.png' },
                    { id: 'voltmeter', name: 'فولتميتر', icon: 'images/components/voltmeter.png' },
                    { id: 'ammeter', name: 'أميتر', icon: 'images/components/ammeter.png' }
                ]
            }
        ];

        // Clear existing content
        libraryContent.innerHTML = '';

        // Create component categories and items
        componentCategories.forEach(category => {
            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'component-category';
            categoryDiv.dataset.category = category.category;

            const categoryTitle = document.createElement('h4');
            categoryTitle.textContent = category.name;
            categoryDiv.appendChild(categoryTitle);

            const componentsGrid = document.createElement('div');
            componentsGrid.className = 'components-grid';

            category.components.forEach(component => {
                const componentItem = document.createElement('div');
                componentItem.className = 'component-item';
                componentItem.dataset.componentId = component.id;

                // Create component icon (fallback to text if image not available)
                const componentIcon = document.createElement('div');
                componentIcon.className = 'component-icon';

                try {
                    const img = document.createElement('img');
                    img.src = component.icon;
                    img.alt = component.name;
                    img.onerror = function() {
                        this.style.display = 'none';
                        componentIcon.textContent = component.name.substring(0, 1);
                    };
                    componentIcon.appendChild(img);
                } catch (e) {
                    componentIcon.textContent = component.name.substring(0, 1);
                }

                const componentName = document.createElement('div');
                componentName.className = 'component-name';
                componentName.textContent = component.name;

                componentItem.appendChild(componentIcon);
                componentItem.appendChild(componentName);
                componentsGrid.appendChild(componentItem);

                // Add click event to select the component
                componentItem.addEventListener('click', () => {
                    // Deselect previous tool button
                    const activeButton = toolbar.querySelector('.active');
                    if (activeButton) {
                        activeButton.classList.remove('active');
                    }

                    // Select the corresponding tool button
                    const toolButton = document.getElementById(`tool-${component.id}`);
                    if (toolButton) {
                        toolButton.classList.add('active');
                        selectedTool = `tool-${component.id}`;
                        canvas.style.cursor = 'cell';
                    }
                });
            });

            categoryDiv.appendChild(componentsGrid);
            libraryContent.appendChild(categoryDiv);
        });

        // Add search functionality
        componentSearch.addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase();
            const componentItems = document.querySelectorAll('.component-item');

            componentItems.forEach(item => {
                const componentName = item.querySelector('.component-name').textContent.toLowerCase();
                if (componentName.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        });

        // Add category filter functionality
        categoryFilter.addEventListener('change', (e) => {
            const selectedCategory = e.target.value;
            const categories = document.querySelectorAll('.component-category');

            categories.forEach(category => {
                if (selectedCategory === 'all' || category.dataset.category === selectedCategory) {
                    category.style.display = 'block';
                } else {
                    category.style.display = 'none';
                }
            });
        });
    }

    // --- Circuit Templates ---
    function initializeTemplates() {
        const templateButtons = document.querySelectorAll('.load-template-btn');

        templateButtons.forEach(button => {
            button.addEventListener('click', () => {
                const templateCard = button.closest('.template-card');
                const templateType = templateCard.dataset.template;

                loadCircuitTemplate(templateType);
            });
        });
    }

    function loadCircuitTemplate(templateType) {
        // Clear existing components
        components = [];

        // Load the selected template
        switch (templateType) {
            case 'voltage-divider':
                // Create a voltage divider circuit
                const source = addComponent('voltage-source', 400, 100);
                const resistor1 = addComponent('resistor', 400, 200);
                const resistor2 = addComponent('resistor', 400, 300);
                const ground = addComponent('ground', 400, 400);

                // Connect components with wires
                addComponent('wire', 400, 150, 400, 180);
                addComponent('wire', 400, 220, 400, 280);
                addComponent('wire', 400, 320, 400, 380);

                // Set properties
                source.properties.voltage = '12V';
                resistor1.properties.resistance = '10kΩ';
                resistor2.properties.resistance = '10kΩ';

                break;

            case 'common-emitter':
                // Create a common emitter amplifier circuit
                const vcc = addComponent('voltage-source', 400, 100);
                const rc = addComponent('resistor', 400, 200);
                const transistor = addComponent('transistor-npn', 400, 300);
                const re = addComponent('resistor', 400, 400);
                const gnd = addComponent('ground', 400, 500);

                // Add base resistors
                const rb1 = addComponent('resistor', 300, 200);
                const rb2 = addComponent('resistor', 300, 400);

                // Add input and output capacitors
                const cin = addComponent('capacitor', 200, 300);
                const cout = addComponent('capacitor', 500, 300);

                // Connect components with wires
                // (simplified connections)
                addComponent('wire', 400, 150, 400, 180);
                addComponent('wire', 400, 220, 400, 280);
                addComponent('wire', 400, 320, 400, 380);
                addComponent('wire', 400, 420, 400, 480);

                // Set properties
                vcc.properties.voltage = '12V';
                rc.properties.resistance = '1kΩ';
                re.properties.resistance = '100Ω';
                rb1.properties.resistance = '10kΩ';
                rb2.properties.resistance = '2.2kΩ';
                cin.properties.capacitance = '10µF';
                cout.properties.capacitance = '10µF';

                break;

            case 'astable-multivibrator':
                // Create an astable multivibrator circuit
                const vcc1 = addComponent('voltage-source', 400, 100);
                const r1 = addComponent('resistor', 300, 200);
                const r2 = addComponent('resistor', 500, 200);
                const c1 = addComponent('capacitor', 350, 300);
                const c2 = addComponent('capacitor', 450, 300);
                const q1 = addComponent('transistor-npn', 300, 300);
                const q2 = addComponent('transistor-npn', 500, 300);
                const gnd1 = addComponent('ground', 300, 400);
                const gnd2 = addComponent('ground', 500, 400);

                // Set properties
                vcc1.properties.voltage = '9V';
                r1.properties.resistance = '10kΩ';
                r2.properties.resistance = '10kΩ';
                c1.properties.capacitance = '10µF';
                c2.properties.capacitance = '10µF';

                break;

            default:
                alert('قالب غير معروف!');
                return;
        }

        // Redraw the canvas
        redrawCanvas();
        updatePropertiesPanel();

        alert(`تم تحميل قالب ${templateType} بنجاح.`);
    }

    // --- Oscilloscope ---
    function initializeOscilloscope() {
        if (!oscilloscopeCanvas || !oscilloscopeCtx) return;

        // Draw oscilloscope grid
        function drawOscilloscopeGrid() {
            const width = oscilloscopeCanvas.width;
            const height = oscilloscopeCanvas.height;

            oscilloscopeCtx.clearRect(0, 0, width, height);

            // Draw background
            oscilloscopeCtx.fillStyle = '#000';
            oscilloscopeCtx.fillRect(0, 0, width, height);

            // Draw grid
            oscilloscopeCtx.strokeStyle = '#333';
            oscilloscopeCtx.lineWidth = 1;

            // Vertical lines
            for (let x = 0; x <= width; x += 30) {
                oscilloscopeCtx.beginPath();
                oscilloscopeCtx.moveTo(x, 0);
                oscilloscopeCtx.lineTo(x, height);
                oscilloscopeCtx.stroke();
            }

            // Horizontal lines
            for (let y = 0; y <= height; y += 30) {
                oscilloscopeCtx.beginPath();
                oscilloscopeCtx.moveTo(0, y);
                oscilloscopeCtx.lineTo(width, y);
                oscilloscopeCtx.stroke();
            }

            // Draw center lines
            oscilloscopeCtx.strokeStyle = '#555';
            oscilloscopeCtx.lineWidth = 1;

            // Vertical center line
            oscilloscopeCtx.beginPath();
            oscilloscopeCtx.moveTo(width / 2, 0);
            oscilloscopeCtx.lineTo(width / 2, height);
            oscilloscopeCtx.stroke();

            // Horizontal center line
            oscilloscopeCtx.beginPath();
            oscilloscopeCtx.moveTo(0, height / 2);
            oscilloscopeCtx.lineTo(width, height / 2);
            oscilloscopeCtx.stroke();
        }

        // Draw initial oscilloscope
        drawOscilloscopeGrid();

        // Start oscilloscope
        oscilloscopeStartBtn.addEventListener('click', () => {
            if (oscilloscopeRunning) return;

            oscilloscopeRunning = true;
            oscilloscopeData = [];

            // Generate sample data (sine wave)
            const timebase = parseFloat(oscilloscopeTimebase.value);
            const voltageScale = parseFloat(oscilloscopeVoltage.value);

            function updateOscilloscope() {
                if (!oscilloscopeRunning) return;

                // Generate new data point
                const time = Date.now() / 1000;
                const value = Math.sin(time * 5) * 50 * voltageScale + oscilloscopeCanvas.height / 2;

                oscilloscopeData.push(value);

                // Keep only the last 300 points
                if (oscilloscopeData.length > 300) {
                    oscilloscopeData.shift();
                }

                // Draw oscilloscope
                drawOscilloscopeGrid();

                // Draw waveform
                oscilloscopeCtx.strokeStyle = '#0f0';
                oscilloscopeCtx.lineWidth = 2;
                oscilloscopeCtx.beginPath();

                for (let i = 0; i < oscilloscopeData.length; i++) {
                    const x = i * (oscilloscopeCanvas.width / 300);
                    const y = oscilloscopeData[i];

                    if (i === 0) {
                        oscilloscopeCtx.moveTo(x, y);
                    } else {
                        oscilloscopeCtx.lineTo(x, y);
                    }
                }

                oscilloscopeCtx.stroke();

                oscilloscopeAnimationFrame = requestAnimationFrame(updateOscilloscope);
            }

            updateOscilloscope();
        });

        // Stop oscilloscope
        oscilloscopeStopBtn.addEventListener('click', () => {
            oscilloscopeRunning = false;
            if (oscilloscopeAnimationFrame) {
                cancelAnimationFrame(oscilloscopeAnimationFrame);
                oscilloscopeAnimationFrame = null;
            }
        });

        // Update oscilloscope settings
        oscilloscopeTimebase.addEventListener('change', () => {
            if (oscilloscopeRunning) {
                oscilloscopeStopBtn.click();
                oscilloscopeStartBtn.click();
            }
        });

        oscilloscopeVoltage.addEventListener('change', () => {
            if (oscilloscopeRunning) {
                oscilloscopeStopBtn.click();
                oscilloscopeStartBtn.click();
            }
        });
    }

    // Initialize all components
    initializeComponentLibrary();
    initializeTemplates();
    initializeOscilloscope();

    // Initial draw
    redrawCanvas();
    updatePropertiesPanel();
});