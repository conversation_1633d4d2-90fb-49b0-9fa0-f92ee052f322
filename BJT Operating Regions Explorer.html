<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BJT Operating Regions Explorer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f2f5; /* Softer background */
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            line-height: 1.6;
        }
        .container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 700px;
        }
        h1 {
            text-align: center;
            color: #2c3e50; /* Darker blue */
            margin-bottom: 25px;
        }
        .controls, .schematic-container, .results, .explanation {
            margin-bottom: 25px;
        }
        .controls label, .results p, .explanation p {
            display: block;
            margin-bottom: 8px;
            color: #555;
        }
        .controls input[type="range"] {
            width: 100%;
            cursor: pointer;
        }
        .schematic {
            width: 100%;
            max-width: 450px; 
            height: auto; 
            border: 1px solid #ddd; /* Lighter border */
            margin: 10px auto; 
            display: block;
            background-color: #fdfdfd;
        }
        .transistor-symbol path, .transistor-symbol line, .transistor-symbol polyline, .transistor-symbol circle {
            stroke: #333;
            stroke-width: 1.5; /* Slightly thinner lines */
            fill: none;
        }
        .transistor-symbol .emitter-arrow {
            fill: #333;
        }
        .label {
            font-family: "Courier New", Courier, monospace;
            font-size: 13px;
            fill: #2c3e50;
        }
        .component-label {
            font-family: "Arial Narrow", Arial, sans-serif;
            font-size: 11px;
            fill: #444;
        }

        .region-indicator {
            padding: 8px 12px;
            border-radius: 4px;
            text-align: center;
            font-weight: bold;
            margin-top: 5px;
            display: inline-block; /* Allow it to sit nicely */
            font-size: 0.95em;
        }
        .cutoff { background-color: #ffebee; color: #c62828; border: 1px solid #ef9a9a; }
        .active { background-color: #e8f5e9; color: #2e7d32; border: 1px solid #a5d6a7; }
        .saturation { background-color: #e3f2fd; color: #1565c0; border: 1px solid #90caf9; }

        .toggle-switch-container { /* Renamed for clarity */
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            justify-content: center; /* Center toggle */
        }
        .toggle-switch-container label {
            margin-right: 10px;
            margin-bottom: 0; 
            font-weight: bold;
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 50px; /* Smaller switch */
            height: 28px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider-track {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 28px;
        }
        .slider-track:before {
            position: absolute;
            content: "";
            height: 20px; /* Smaller knob */
            width: 20px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }
        input:checked + .slider-track {
            background-color: #2196F3;
        }
        input:checked + .slider-track:before {
            transform: translateX(22px); /* Adjusted for smaller switch */
        }
        .param-input {
            width: 60px;
            margin-left: 8px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .param-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px 20px; /* Row gap, column gap */
            margin-bottom: 20px;
            justify-content: center;
        }
        .param-controls div {
            display: flex;
            align-items: center;
        }
        .results p {
            font-size: 1.05em;
        }
        .results span {
            font-weight: bold;
            color: #2c3e50;
        }
        .explanation h3 {
            margin-top: 0;
            color: #2c3e50;
        }

        @media (max-width: 600px) {
            body { padding: 10px; }
            .container { padding: 15px; }
            h1 { font-size: 1.5em; }
            .param-controls {
                flex-direction: column;
                align-items: stretch; /* Make items take full width */
                gap: 10px;
            }
            .param-controls div {
                justify-content: space-between; /* Align label and input */
            }
            .param-input { width: 80px; } /* Slightly wider for touch */
            .toggle-switch-container label { font-size: 0.9em; }
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>BJT Operating Regions Explorer</h1>

        <div class="controls">
            <div class="toggle-switch-container">
                <label for="transistorTypeToggle">NPN</label>
                <label class="switch">
                    <input type="checkbox" id="transistorTypeToggle">
                    <span class="slider-track"></span>
                </label>
                <label for="transistorTypeToggle">PNP</label>
            </div>

            <div class="param-controls">
                <div>
                    <label for="vcc">V<sub>CC</sub> (V):</label>
                    <input type="number" id="vcc" class="param-input" value="10" step="0.1" min="1">
                </div>
                <div>
                    <label for="rc">R<sub>C</sub> (kΩ):</label>
                    <input type="number" id="rc" class="param-input" value="2.2" step="0.1" min="0.1">
                </div>
                <div>
                    <label for="rb">R<sub>B</sub> (kΩ):</label>
                    <input type="number" id="rb" class="param-input" value="220" step="1" min="1">
                </div>
                 <div>
                    <label for="beta">β (h<sub>FE</sub>):</label>
                    <input type="number" id="beta" class="param-input" value="100" step="1" min="10">
                </div>
            </div>
            
            <div>
                <label for="vinSlider">V<sub>IN</sub>: <span id="vinValue">0</span> V</label>
                <input type="range" id="vinSlider" min="0" max="10" step="0.01" value="5.7">
            </div>
        </div>

        <div class="schematic-container">
            <svg id="circuitDiagram" class="schematic" viewBox="0 0 400 300">
                <!-- VCC Source Symbol -->
                <line x1="200" y1="20" x2="200" y2="40" stroke="#333" stroke-width="1.5"/>
                <text x="210" y="30" class="component-label" id="vccLabel">VCC</text>
                <line x1="190" y1="20" x2="210" y2="20" stroke="#333" stroke-width="1.5"/>

                <!-- RC Resistor -->
                <line x1="200" y1="40" x2="200" y2="60" stroke="#333" stroke-width="1.5"/>
                <rect x="192" y="60" width="16" height="30" stroke="#333" stroke-width="1.5" fill="#fff"/>
                <text x="215" y="80" class="component-label">RC</text>
                <line x1="200" y1="90" x2="200" y2="110" stroke="#333" stroke-width="1.5"/>

                <!-- Transistor -->
                <g id="transistorSymbol" class="transistor-symbol" transform="translate(200, 150)">
                    <circle cx="0" cy="0" r="18" stroke-width="1.5" fill="white"/>
                    <line x1="0" y1="-18" x2="0" y2="-30" id="collectorLead"/> <!-- Collector lead -->
                    <line x1="-18" y1="0" x2="-35" y2="0" id="baseLead"/>    <!-- Base lead -->
                    <line x1="0" y1="18" x2="0" y2="35" id="emitterLead"/>   <!-- Emitter lead -->
                    <line x1="-13" y1="-13" x2="0" y2="-18" /> <!-- C line inside -->
                    <line x1="-13" y1="13" x2="0" y2="18" />   <!-- E line inside -->
                    <line x1="-13" y1="-13" x2="-13" y2="13" /> <!-- Base flat line -->
                    <polygon id="emitterArrow" points="0,28 4,19 -4,19" class="emitter-arrow"/> <!-- Arrow adjusted for new E lead -->
                    <text x="8" y="-28" class="label">C</text>
                    <text x="-45" y="3" class="label">B</text>
                    <text x="8" y="42" class="label">E</text>
                </g>
                
                <!-- Connection C to RC -->
                <line x1="200" y1="110" x2="200" y2="120" stroke="#333" stroke-width="1.5"/>

                <!-- Connection E to Ground/VCC -->
                <line x1="200" y1="185" x2="200" y2="210" stroke="#333" stroke-width="1.5" id="emitterConnection"/>
                <line x1="185" y1="210" x2="215" y2="210" stroke="#333" stroke-width="1.5" id="gndVccLine1"/>
                <line x1="190" y1="215" x2="210" y2="215" stroke="#333" stroke-width="1.5" id="gndVccLine2" style="display:none;"/>
                <line x1="195" y1="220" x2="205" y2="220" stroke="#333" stroke-width="1.5" id="gndVccLine3" style="display:none;"/>
                <text x="220" y="215" class="component-label" id="emitterGndVccLabel">GND</text>

                <!-- RB Resistor -->
                <line x1="165" y1="150" x2="130" y2="150" stroke="#333" stroke-width="1.5"/> <!-- Base to RB -->
                <rect x="114" y="142" width="16" height="16" stroke="#333" stroke-width="1.5" fill="#fff"/>
                <text x="105" y="138" class="component-label" text-anchor="end">RB</text>
                <line x1="114" y1="150" x2="70" y2="150" stroke="#333" stroke-width="1.5"/>

                <!-- VIN Source -->
                <circle cx="45" cy="150" r="12" stroke="#333" stroke-width="1.5" fill="#fff"/>
                <text x="45" y="154" text-anchor="middle" font-size="14px" font-weight="bold" id="vinSourcePolarityPlus">+</text>
                <text x="45" y="154" text-anchor="middle" font-size="14px" font-weight="bold" id="vinSourcePolarityMinus" style="display:none;">-</text>
                <line x1="57" y1="150" x2="70" y2="150" stroke="#333" stroke-width="1.5"/> <!-- Vin to RB connection -->
                <line x1="33" y1="150" x2="10" y2="150" stroke="#333" stroke-width="1.5" id="vinNegativeTerminalLine"/>
                <text x="40" y="175" class="component-label">V<sub>IN</sub></text>
                
                <!-- VIN "other side" connection -->
                <line x1="10" y1="150" x2="10" y2="210" stroke="#333" stroke-width="1.5" id="vinRefLineToGndVcc"/>
                <line x1="-5" y1="210" x2="25" y2="210" stroke="#333" stroke-width="1.5" id="vinRefGndLine1"/>
                <line x1="0" y1="215" x2="20" y2="215" stroke="#333" stroke-width="1.5" id="vinRefGndLine2" style="display:none;"/>
                <line x1="5" y1="220" x2="15" y2="220" stroke="#333" stroke-width="1.5" id="vinRefGndLine3" style="display:none;"/>
                <text x="-10" y="215" class="component-label" id="vinRefLabel">GND</text>
            </svg>
        </div>

        <div class="results">
            <p>Operating Region: <span id="regionName" class="region-indicator">Calculating...</span></p>
            <p>I<sub>B</sub> (Base Current): <span id="ibValue">0</span> µA</p>
            <p>V<sub>CE</sub> (Collector-Emitter Voltage): <span id="vceValue">0</span> V</p>
        </div>

        <div class="explanation">
            <h3>Region Explanation:</h3>
            <p id="regionExplanationText"></p>
        </div>
    </div>

    <script>
        const vinSlider = document.getElementById('vinSlider');
        const vinValueDisplay = document.getElementById('vinValue');
        const transistorTypeToggle = document.getElementById('transistorTypeToggle');
        
        const vccInput = document.getElementById('vcc');
        const rcInput = document.getElementById('rc');
        const rbInput = document.getElementById('rb');
        const betaInput = document.getElementById('beta');

        const regionNameDisplay = document.getElementById('regionName');
        const ibValueDisplay = document.getElementById('ibValue');
        const vceValueDisplay = document.getElementById('vceValue');
        const regionExplanationText = document.getElementById('regionExplanationText');

        const emitterArrow = document.getElementById('emitterArrow');
        const emitterGndVccLabel = document.getElementById('emitterGndVccLabel');
        const vccLabel = document.getElementById('vccLabel');
        
        const vinSourcePolarityPlus = document.getElementById('vinSourcePolarityPlus');
        const vinSourcePolarityMinus = document.getElementById('vinSourcePolarityMinus');
        const vinNegativeTerminalLine = document.getElementById('vinNegativeTerminalLine'); // Line from VIN circle to its reference
        const vinRefLineToGndVcc = document.getElementById('vinRefLineToGndVcc'); // Vertical line from VIN ref connection point
        const vinRefLabel = document.getElementById('vinRefLabel'); // GND/VCC label for VIN ref
        
        // Ground/VCC symbol lines
        const gndVccLine1 = document.getElementById('gndVccLine1');
        const gndVccLine2 = document.getElementById('gndVccLine2');
        const gndVccLine3 = document.getElementById('gndVccLine3');
        const vinRefGndLine1 = document.getElementById('vinRefGndLine1');
        const vinRefGndLine2 = document.getElementById('vinRefGndLine2');
        const vinRefGndLine3 = document.getElementById('vinRefGndLine3');


        const VBE_ON_NPN = 0.7; 
        const VCE_SAT_NPN = 0.2; 
        const VEB_ON_PNP = 0.7; // Magnitude
        const VCE_SAT_PNP = -0.2; 

        let currentSettings = {
            vcc: parseFloat(vccInput.value),
            rcKOhms: parseFloat(rcInput.value),
            rbKOhms: parseFloat(rbInput.value),
            beta: parseInt(betaInput.value),
            vin: parseFloat(vinSlider.value),
            isNPN: !transistorTypeToggle.checked
        };
        
        function updateVinSliderRange() {
            vinSlider.max = currentSettings.vcc;
            if (parseFloat(vinSlider.value) > currentSettings.vcc) {
                vinSlider.value = currentSettings.vcc;
            }
            currentSettings.vin = parseFloat(vinSlider.value);
            vinValueDisplay.textContent = currentSettings.vin.toFixed(2);
        }

        function updateSchematicVisuals() {
            const isNPN = currentSettings.isNPN;
            if (isNPN) {
                emitterArrow.setAttribute('points', "0,28 4,19 -4,19"); 
                emitterGndVccLabel.textContent = "GND";
                vccLabel.textContent = "VCC";

                vinSourcePolarityPlus.style.display = 'block';
                vinSourcePolarityMinus.style.display = 'none';
                
                vinNegativeTerminalLine.setAttribute('x1','33'); // Standard connection for NPN VIN source
                vinRefLineToGndVcc.setAttribute('y2', '210'); // VIN ref to GND line
                vinRefLabel.textContent = "GND";

                // Show ground symbols
                [gndVccLine1, gndVccLine2, gndVccLine3, vinRefGndLine1, vinRefGndLine2, vinRefGndLine3].forEach(el => {
                    el.style.display = el.id.endsWith('Line1') ? 'block' : 'none'; // Simplified ground symbol
                });
                 gndVccLine2.style.display = 'block'; // Show 2 lines for ground
                 gndVccLine3.style.display = 'block';
                 vinRefGndLine2.style.display = 'block';
                 vinRefGndLine3.style.display = 'block';


            } else { // PNP
                emitterArrow.setAttribute('points', "0,19 4,28 -4,28"); 
                emitterGndVccLabel.textContent = "VCC"; // Emitter connects to VCC line
                vccLabel.textContent = "VCC"; // Top rail is VCC

                // For PNP, VIN source is effectively (VCC - VIN_slider_value) driving RB.
                // Visually, this means VIN source is between VCC and RB input.
                // The circle represents VIN_slider_value.
                // Positive terminal of this source is at VCC, negative to RB.
                vinSourcePolarityPlus.style.display = 'block'; // Plus at top of circle (VCC side)
                vinSourcePolarityMinus.style.display = 'block'; // Minus at bottom of circle (RB side)
                vinSourcePolarityPlus.setAttribute('y', '146'); // Move + up
                vinSourcePolarityMinus.setAttribute('y', '162'); // Move - down

                // Line from top of VIN circle (positive) to VCC rail
                vinNegativeTerminalLine.setAttribute('x1','45'); // Make it vertical from center top
                vinNegativeTerminalLine.setAttribute('x2','45');
                vinNegativeTerminalLine.setAttribute('y1','138'); // From top of circle
                vinNegativeTerminalLine.setAttribute('y2','30'); // To VCC connection point (approx)
                
                vinRefLineToGndVcc.style.display = 'none'; // This vertical line not needed as VIN ref is now VCC via vinNegativeTerminalLine
                vinRefLabel.textContent = ""; // No separate label, implied by connection to VCC

                // Hide ground symbols for emitter and VIN reference
                [gndVccLine1, gndVccLine2, gndVccLine3, vinRefGndLine1, vinRefGndLine2, vinRefGndLine3].forEach(el => {
                    el.style.display = 'none';
                });
                 // Emitter connects to VCC line directly, no ground symbol
                gndVccLine1.setAttribute('y1', '210'); // Main VCC line for emitter
                gndVccLine1.setAttribute('y2', '210');
                gndVccLine1.setAttribute('x1', '185');
                gndVccLine1.setAttribute('x2', '215');
                gndVccLine1.style.display = 'block'; // Show as solid line for VCC rail
            }
            calculateAndDisplay();
        }


        function calculateAndDisplay() {
            const vcc = parseFloat(vccInput.value);
            const rcKOhms = parseFloat(rcInput.value);
            const rbKOhms = parseFloat(rbInput.value);
            const beta = parseInt(betaInput.value);
            const vin_slider_value = parseFloat(vinSlider.value); // Value from the slider
            const isNPN = !transistorTypeToggle.checked;

            // Update global settings cache if needed, or just use local consts
            currentSettings.vcc = vcc;
            currentSettings.rcKOhms = rcKOhms;
            currentSettings.rbKOhms = rbKOhms;
            currentSettings.beta = beta;
            currentSettings.vin = vin_slider_value;
            currentSettings.isNPN = isNPN;


            const rc = rcKOhms * 1000; 
            const rb = rbKOhms * 1000; 

            let ib = 0, ic = 0, vce = 0;
            let region = "";
            let explanation = "";
            
            // For both NPN and PNP, we've designed the schematic and interpretation
            // so that `vin_slider_value` is directly compared to VBE_on/VEB_on for turn-on.
            // The effective voltage that overcomes VBE_on/VEB_on is (vin_slider_value - VBE_on/VEB_on).
            // This leads to IB = (vin_slider_value - V_on_threshold) / RB.

            if (isNPN) {
                if (vin_slider_value <= VBE_ON_NPN) {
                    region = "Cutoff";
                    ib = 0;
                    ic = 0;
                    vce = vcc;
                    explanation = "Transistor is OFF. No significant current flows from collector to emitter (I_C ≈ 0). This occurs when V_IN (driving the base resistor) is less than or equal to V_BE(on) (typically ~0.7V).";
                } else {
                    ib = (vin_slider_value - VBE_ON_NPN) / rb;
                    let ic_active = beta * ib;
                    let vce_if_active = vcc - ic_active * rc;

                    if (vce_if_active <= VCE_SAT_NPN) {
                        region = "Saturation";
                        vce = VCE_SAT_NPN;
                        ic = (vcc - VCE_SAT_NPN) / rc;
                        if (ic < 0) ic = 0; 
                        explanation = "Transistor is fully ON, acting like a closed switch. Maximum current (limited by R_C and V_CC) flows. V_CE is at its minimum (V_CEsat, typically ~0.2V). Further increases in base current (I_B) won't significantly increase I_C.";
                    } else {
                        region = "Active";
                        ic = ic_active;
                        vce = vce_if_active;
                        explanation = "Transistor acts as an amplifier. Collector current (I_C) is proportional to base current (I_B) by a factor of β (I_C = β * I_B). V_CE is between V_CEsat and V_CC.";
                    }
                }
            } else { // PNP
                if (vin_slider_value <= VEB_ON_PNP) { 
                    region = "Cutoff";
                    ib = 0;
                    ic = 0;
                    vce = -vcc; 
                    explanation = "Transistor is OFF. No significant current flows from emitter to collector (I_C ≈ 0). This occurs when V_IN (which effectively determines how much the base is pulled 'up' from its 'off' state towards V_CC - V_EB(on)) is less than or equal to V_EB(on) (typically ~0.7V). For PNP, V_CE is typically negative.";
                } else {
                    ib = (vin_slider_value - VEB_ON_PNP) / rb; 
                    let ic_active = beta * ib;
                    // For PNP CE with RC to ground: Vc = Ic*Rc. Ve = Vcc. Vce = Vc - Ve = Ic*Rc - Vcc.
                    let vce_if_active = ic_active * rc - vcc;

                    if (vce_if_active >= VCE_SAT_PNP) { 
                        region = "Saturation";
                        vce = VCE_SAT_PNP;
                        ic = (vce + vcc) / rc; 
                        if (ic < 0) ic = 0; 
                        explanation = "Transistor is fully ON, acting like a closed switch. Maximum current flows. V_CE is at its most 'positive' saturated value (e.g., -0.2V, meaning V_EC is ~0.2V). Further increases in I_B won't significantly increase I_C.";
                    } else {
                        region = "Active";
                        ic = ic_active;
                        vce = vce_if_active;
                        explanation = "Transistor acts as an amplifier. Collector current (I_C) is proportional to base current (I_B) by a factor of β (I_C = β * I_B). V_CE is between V_CEsat (e.g., -0.2V) and -V_CC.";
                    }
                }
            }
            
            if (ib < 0) ib = 0;
            if (ic < 0) ic = 0;

            regionNameDisplay.textContent = region;
            regionNameDisplay.className = 'region-indicator ' + region.toLowerCase();
            ibValueDisplay.textContent = (ib * 1e6).toFixed(2); 
            vceValueDisplay.textContent = vce.toFixed(2);
            regionExplanationText.textContent = explanation;
        }

        vinSlider.addEventListener('input', (e) => {
            currentSettings.vin = parseFloat(e.target.value); // Update global state
            vinValueDisplay.textContent = currentSettings.vin.toFixed(2);
            calculateAndDisplay();
        });

        transistorTypeToggle.addEventListener('change', (e) => {
            currentSettings.isNPN = !e.target.checked; // Update global state
            updateSchematicVisuals(); 
        });
        
        [vccInput, rcInput, rbInput, betaInput].forEach(input => {
            input.addEventListener('change', () => { // No event arg needed if reading from currentSettings
                // Read directly from input fields to ensure latest values before updating currentSettings
                currentSettings.vcc = Math.max(1, parseFloat(vccInput.value)); // Enforce min
                currentSettings.rcKOhms = Math.max(0.1, parseFloat(rcInput.value));
                currentSettings.rbKOhms = Math.max(1, parseFloat(rbInput.value));
                currentSettings.beta = Math.max(10, parseInt(betaInput.value));

                // Update input fields if values were clamped
                vccInput.value = currentSettings.vcc;
                rcInput.value = currentSettings.rcKOhms;
                rbInput.value = currentSettings.rbKOhms;
                betaInput.value = currentSettings.beta;

                updateVinSliderRange(); 
                calculateAndDisplay();
            });
        });
        
        // Initial setup
        updateVinSliderRange(); 
        vinValueDisplay.textContent = parseFloat(vinSlider.value).toFixed(2); 
        updateSchematicVisuals(); // Initial draw and calculation

    </script>
</body>
</html>
