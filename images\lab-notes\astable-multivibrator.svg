<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="600" height="450" viewBox="0 0 600 450">
  <!-- Background -->
  <rect width="600" height="450" fill="#ffffff"/>
  
  <!-- Title -->
  <text x="300" y="30" font-family="Arial" font-size="20" text-anchor="middle" fill="#333333">Astable Multivibrator Circuit</text>
  
  <!-- Power Supply (VCC) -->
  <text x="300" y="70" font-family="Arial" font-size="14" text-anchor="middle" fill="#333333">VCC (+9V)</text>
  <line x1="300" y1="80" x2="300" y2="100" stroke="#000000" stroke-width="2"/>
  
  <!-- Collector Resistors -->
  <!-- RC1 -->
  <line x1="200" y1="100" x2="200" y2="120" stroke="#000000" stroke-width="2"/>
  <rect x="190" y="120" width="20" height="40" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <text x="170" y="140" font-family="Arial" font-size="12" fill="#333333">RC1</text>
  <text x="170" y="155" font-family="Arial" font-size="10" fill="#666666">10kΩ</text>
  <line x1="200" y1="160" x2="200" y2="180" stroke="#000000" stroke-width="2"/>
  
  <!-- RC2 -->
  <line x1="400" y1="100" x2="400" y2="120" stroke="#000000" stroke-width="2"/>
  <rect x="390" y="120" width="20" height="40" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <text x="420" y="140" font-family="Arial" font-size="12" fill="#333333">RC2</text>
  <text x="420" y="155" font-family="Arial" font-size="10" fill="#666666">10kΩ</text>
  <line x1="400" y1="160" x2="400" y2="180" stroke="#000000" stroke-width="2"/>
  
  <!-- Connect VCC to RC1 and RC2 -->
  <line x1="200" y1="100" x2="400" y2="100" stroke="#000000" stroke-width="2"/>
  <line x1="300" y1="100" x2="300" y2="80" stroke="#000000" stroke-width="2"/>
  
  <!-- Transistors -->
  <!-- Q1 -->
  <!-- Base -->
  <line x1="150" y1="200" x2="180" y2="200" stroke="#000000" stroke-width="2"/>
  <!-- Collector -->
  <line x1="200" y1="180" x2="200" y2="190" stroke="#000000" stroke-width="2"/>
  <!-- Emitter -->
  <line x1="200" y1="210" x2="200" y2="230" stroke="#000000" stroke-width="2"/>
  <!-- Transistor Symbol -->
  <circle cx="190" cy="200" r="15" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <line x1="180" y1="200" x2="190" y2="190" stroke="#000000" stroke-width="2"/>
  <line x1="190" y1="190" x2="190" y2="210" stroke="#000000" stroke-width="2"/>
  <line x1="190" y1="210" x2="200" y2="220" stroke="#000000" stroke-width="2"/>
  <line x1="190" y1="190" x2="200" y2="180" stroke="#000000" stroke-width="2"/>
  <text x="170" y="200" font-family="Arial" font-size="12" fill="#333333">Q1</text>
  
  <!-- Q2 -->
  <!-- Base -->
  <line x1="450" y1="200" x2="420" y2="200" stroke="#000000" stroke-width="2"/>
  <!-- Collector -->
  <line x1="400" y1="180" x2="400" y2="190" stroke="#000000" stroke-width="2"/>
  <!-- Emitter -->
  <line x1="400" y1="210" x2="400" y2="230" stroke="#000000" stroke-width="2"/>
  <!-- Transistor Symbol -->
  <circle cx="410" cy="200" r="15" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <line x1="420" y1="200" x2="410" y2="190" stroke="#000000" stroke-width="2"/>
  <line x1="410" y1="190" x2="410" y2="210" stroke="#000000" stroke-width="2"/>
  <line x1="410" y1="210" x2="400" y2="220" stroke="#000000" stroke-width="2"/>
  <line x1="410" y1="190" x2="400" y2="180" stroke="#000000" stroke-width="2"/>
  <text x="430" y="200" font-family="Arial" font-size="12" fill="#333333">Q2</text>
  
  <!-- Ground -->
  <line x1="200" y1="230" x2="200" y2="250" stroke="#000000" stroke-width="2"/>
  <line x1="400" y1="230" x2="400" y2="250" stroke="#000000" stroke-width="2"/>
  <line x1="180" y1="250" x2="420" y2="250" stroke="#000000" stroke-width="2"/>
  <line x1="295" y1="250" x2="295" y2="270" stroke="#000000" stroke-width="2"/>
  <line x1="305" y1="250" x2="305" y2="270" stroke="#000000" stroke-width="2"/>
  <line x1="280" y1="270" x2="320" y2="270" stroke="#000000" stroke-width="2"/>
  <line x1="285" y1="275" x2="315" y2="275" stroke="#000000" stroke-width="2"/>
  <line x1="290" y1="280" x2="310" y2="280" stroke="#000000" stroke-width="2"/>
  <line x1="295" y1="285" x2="305" y2="285" stroke="#000000" stroke-width="2"/>
  
  <!-- Coupling Capacitors -->
  <!-- C1 -->
  <line x1="200" y1="180" x2="250" y2="180" stroke="#000000" stroke-width="2"/>
  <line x1="250" y1="180" x2="250" y2="200" stroke="#000000" stroke-width="2"/>
  <line x1="250" y1="200" x2="350" y2="200" stroke="#000000" stroke-width="2"/>
  <line x1="350" y1="180" x2="350" y2="220" stroke="#000000" stroke-width="2"/>
  <line x1="360" y1="180" x2="360" y2="220" stroke="#000000" stroke-width="2"/>
  <line x1="360" y1="200" x2="420" y2="200" stroke="#000000" stroke-width="2"/>
  <text x="355" y="170" font-family="Arial" font-size="12" fill="#333333">C1</text>
  <text x="355" y="240" font-family="Arial" font-size="10" fill="#666666">10µF</text>
  
  <!-- C2 -->
  <line x1="400" y1="180" x2="450" y2="180" stroke="#000000" stroke-width="2"/>
  <line x1="450" y1="180" x2="450" y2="200" stroke="#000000" stroke-width="2"/>
  <line x1="150" y1="200" x2="50" y2="200" stroke="#000000" stroke-width="2"/>
  <line x1="50" y1="180" x2="50" y2="220" stroke="#000000" stroke-width="2"/>
  <line x1="40" y1="180" x2="40" y2="220" stroke="#000000" stroke-width="2"/>
  <line x1="40" y1="200" x2="20" y2="200" stroke="#000000" stroke-width="2"/>
  <line x1="20" y1="200" x2="20" y2="180" stroke="#000000" stroke-width="2"/>
  <line x1="20" y1="180" x2="150" y2="180" stroke="#000000" stroke-width="2"/>
  <text x="45" y="170" font-family="Arial" font-size="12" fill="#333333">C2</text>
  <text x="45" y="240" font-family="Arial" font-size="10" fill="#666666">10µF</text>
  
  <!-- Base Resistors -->
  <!-- RB1 -->
  <line x1="150" y1="180" x2="150" y2="120" stroke="#000000" stroke-width="2"/>
  <rect x="140" y="120" width="20" height="40" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <text x="120" y="140" font-family="Arial" font-size="12" fill="#333333">RB1</text>
  <text x="120" y="155" font-family="Arial" font-size="10" fill="#666666">100kΩ</text>
  <line x1="150" y1="120" x2="150" y2="100" stroke="#000000" stroke-width="2"/>
  <line x1="150" y1="100" x2="200" y2="100" stroke="#000000" stroke-width="2"/>
  
  <!-- RB2 -->
  <line x1="450" y1="180" x2="450" y2="120" stroke="#000000" stroke-width="2"/>
  <rect x="440" y="120" width="20" height="40" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <text x="470" y="140" font-family="Arial" font-size="12" fill="#333333">RB2</text>
  <text x="470" y="155" font-family="Arial" font-size="10" fill="#666666">100kΩ</text>
  <line x1="450" y1="120" x2="450" y2="100" stroke="#000000" stroke-width="2"/>
  <line x1="450" y1="100" x2="400" y2="100" stroke="#000000" stroke-width="2"/>
  
  <!-- Output LEDs -->
  <!-- LED1 -->
  <line x1="200" y1="180" x2="200" y2="160" stroke="#000000" stroke-width="2"/>
  <polygon points="190,140 210,140 200,160" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <line x1="190" y1="130" x2="210" y2="130" stroke="#000000" stroke-width="2"/>
  <line x1="200" y1="130" x2="200" y2="120" stroke="#000000" stroke-width="2"/>
  <text x="180" y="120" font-family="Arial" font-size="12" fill="#333333">LED1</text>
  
  <!-- LED2 -->
  <line x1="400" y1="180" x2="400" y2="160" stroke="#000000" stroke-width="2"/>
  <polygon points="390,140 410,140 400,160" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <line x1="390" y1="130" x2="410" y2="130" stroke="#000000" stroke-width="2"/>
  <line x1="400" y1="130" x2="400" y2="120" stroke="#000000" stroke-width="2"/>
  <text x="420" y="120" font-family="Arial" font-size="12" fill="#333333">LED2</text>
  
  <!-- Output Waveforms -->
  <text x="150" y="320" font-family="Arial" font-size="14" fill="#333333">Output Waveforms:</text>
  
  <!-- Q1 Collector Waveform -->
  <line x1="100" y1="350" x2="250" y2="350" stroke="#666666" stroke-width="1"/>
  <line x1="100" y1="330" x2="100" y2="370" stroke="#666666" stroke-width="1"/>
  <path d="M 100,370 H 150 V 330 H 200 V 370 H 250" fill="none" stroke="#007bff" stroke-width="2"/>
  <text x="175" y="390" font-family="Arial" font-size="12" fill="#007bff">Q1 Collector</text>
  
  <!-- Q2 Collector Waveform -->
  <line x1="300" y1="350" x2="450" y2="350" stroke="#666666" stroke-width="1"/>
  <line x1="300" y1="330" x2="300" y2="370" stroke="#666666" stroke-width="1"/>
  <path d="M 300,330 H 350 V 370 H 400 V 330 H 450" fill="none" stroke="#dc3545" stroke-width="2"/>
  <text x="375" y="390" font-family="Arial" font-size="12" fill="#dc3545">Q2 Collector</text>
  
  <!-- Frequency Formula -->
  <text x="300" y="420" font-family="Arial" font-size="14" text-anchor="middle" fill="#333333">f = 1 / (0.693 × (R1×C1 + R2×C2))</text>
</svg>
