/* Simulation System Styles */

/* Main Layout */
.simulation-container {
    display: grid;
    grid-template-columns: 250px 1fr 300px;
    gap: 15px;
    margin-bottom: 30px;
    height: 70vh;
    min-height: 600px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px var(--shadow-color);
}

@media (max-width: 1200px) {
    .simulation-container {
        grid-template-columns: 200px 1fr 250px;
    }
}

@media (max-width: 992px) {
    .simulation-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
        height: auto;
    }
}

/* Simulation Header */
.simulation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.simulation-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Components Panel */
.components-panel {
    background-color: var(--card-bg);
    border-radius: 10px 0 0 10px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.components-header {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--primary-color);
    color: white;
}

.components-header h3 {
    margin: 0 0 10px 0;
    font-size: 1.1rem;
}

.components-search {
    position: relative;
}

.components-search input {
    width: 100%;
    padding: 8px 30px 8px 10px;
    border: none;
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.components-search input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.components-search i {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
}

.components-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    padding: 10px;
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
}

.component-category {
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.85rem;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.2s ease;
}

.component-category:hover {
    background-color: var(--secondary-color);
    color: white;
}

.component-category.active {
    background-color: var(--primary-color);
    color: white;
}

.components-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
    align-content: start;
}

.component-item {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: grab;
    transition: all 0.2s ease;
}

.component-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 10px var(--shadow-color);
    border-color: var(--secondary-color);
}

.component-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
}

.component-icon img {
    max-width: 100%;
    max-height: 100%;
}

.component-name {
    font-size: 0.85rem;
    text-align: center;
    color: var(--text-color);
}

/* Circuit Workspace */
.circuit-workspace {
    background-color: var(--card-bg);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
}

.workspace-toolbar {
    display: flex;
    gap: 5px;
    padding: 10px;
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
}

.tool-btn {
    width: 36px;
    height: 36px;
    border-radius: 5px;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tool-btn:hover {
    background-color: var(--secondary-color);
    color: white;
}

.tool-btn.active {
    background-color: var(--primary-color);
    color: white;
}

/* قائمة التدوير المنسدلة */
.tool-dropdown {
    position: relative;
    display: inline-block;
}

.tool-dropdown-content {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--card-bg);
    min-width: 150px;
    box-shadow: 0 3px 10px var(--shadow-color);
    border-radius: 5px;
    border: 1px solid var(--border-color);
    z-index: 100;
    margin-top: 5px;
}

.tool-dropdown:hover .tool-dropdown-content,
.tool-dropdown.active .tool-dropdown-content {
    display: block;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    width: 100%;
    text-align: right;
    border: none;
    background: none;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-color);
}

.dropdown-item:hover {
    background-color: var(--light-color);
}

.dropdown-item i {
    width: 16px;
    text-align: center;
    color: var(--secondary-color);
}

/* مربع حوار التدوير المخصص */
.rotation-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 5px 15px var(--shadow-color);
    padding: 20px;
    z-index: 1000;
    min-width: 300px;
    display: none;
}

.rotation-dialog.active {
    display: block;
}

.rotation-dialog h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.rotation-input {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.rotation-input input {
    flex: 1;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.rotation-input span {
    font-weight: bold;
}

.rotation-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.rotation-actions button {
    padding: 8px 15px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.rotation-actions .apply-btn {
    background-color: var(--primary-color);
    color: white;
}

.rotation-actions .cancel-btn {
    background-color: var(--light-color);
    color: var(--text-color);
}

.rotation-actions button:hover {
    opacity: 0.9;
}

.circuit-canvas {
    flex: 1;
    position: relative;
    overflow: hidden;
}

#grid-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(to right, rgba(0, 0, 0, 0.1) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    z-index: 0;
}

#main-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

/* Properties Panel */
.properties-panel {
    background-color: var(--card-bg);
    border-radius: 0 10px 10px 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.properties-header {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--primary-color);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.properties-header h3 {
    margin: 0;
    font-size: 1.1rem;
}

.properties-message {
    padding: 20px;
    text-align: center;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.properties-content {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
}

.property-group {
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 15px;
}

.property-group:last-child {
    border-bottom: none;
}

.property-group h4 {
    margin: 0 0 10px 0;
    font-size: 1rem;
    color: var(--primary-color);
}

.property-item {
    margin-bottom: 10px;
}

.property-item label {
    display: block;
    margin-bottom: 5px;
    font-size: 0.9rem;
    color: var(--text-color);
}

.property-item input,
.property-item select {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background-color: var(--input-bg);
}

.component-description {
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--text-color);
}

.property-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

/* Analysis Panel */
.analysis-panel {
    margin-top: 20px;
    background-color: var(--card-bg);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px var(--shadow-color);
}

.analysis-tabs {
    display: flex;
    overflow-x: auto;
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
}

.analysis-tab {
    padding: 12px 20px;
    white-space: nowrap;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 600;
    color: var(--text-color);
}

.analysis-tab:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--primary-color);
}

.analysis-tab.active {
    border-bottom-color: var(--primary-color);
    color: var(--primary-color);
}

.analysis-tab-content {
    display: none;
    padding: 20px;
}

.analysis-tab-content.active {
    display: block;
}

.results-container {
    border: 1px solid var(--border-color);
    border-radius: 5px;
    overflow: hidden;
}

.results-header {
    padding: 10px 15px;
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.results-header h4 {
    margin: 0;
    font-size: 1rem;
}

.results-controls {
    display: flex;
    gap: 10px;
}

.results-display {
    padding: 15px;
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
}

.no-results {
    color: var(--text-muted);
    text-align: center;
    padding: 20px;
}

.waveform-container {
    border: 1px solid var(--border-color);
    border-radius: 5px;
    overflow: hidden;
}

.waveform-controls {
    padding: 15px;
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.control-group label {
    font-size: 0.9rem;
}

.waveform-display {
    height: 300px;
    position: relative;
}

#waveform-canvas {
    width: 100%;
    height: 100%;
}

/* Guide Section */
.guide-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.guide-item {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 5px 15px var(--shadow-color);
}

.guide-item h3 {
    margin-top: 0;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.guide-item h3 i {
    color: var(--secondary-color);
}

.guide-item p {
    margin-bottom: 0;
    line-height: 1.5;
}

/* Buttons and Controls */
.btn {
    padding: 8px 15px;
    border-radius: 5px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.btn:hover {
    background-color: var(--primary-dark);
}

.btn:disabled {
    background-color: var(--text-muted);
    cursor: not-allowed;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 0.85rem;
}

.btn-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: transparent;
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    background-color: rgba(255, 255, 255, 0.2);
}
