<!DOCTYPE html>
<html>
<head>
    <title>SVG to ICO Converter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        canvas {
            border: 1px solid #ddd;
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>SVG to ICO Converter</h1>
    <p>This tool converts the SVG icon to multiple PNG sizes for ICO creation.</p>
    
    <div>
        <canvas id="canvas" width="256" height="256"></canvas>
    </div>
    
    <button id="convertButton">Convert and Download</button>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        const sizes = [16, 32, 48, 64, 128, 256];
        
        // Load the SVG
        const img = new Image();
        img.onload = function() {
            ctx.drawImage(img, 0, 0, 256, 256);
        };
        img.src = 'build/icon.svg';
        
        document.getElementById('convertButton').addEventListener('click', function() {
            // Create a zip file with all the PNG sizes
            const zip = new JSZip();
            const folder = zip.folder("icons");
            
            // Create a promise for each size
            const promises = sizes.map(size => {
                return new Promise((resolve, reject) => {
                    const tempCanvas = document.createElement('canvas');
                    tempCanvas.width = size;
                    tempCanvas.height = size;
                    const tempCtx = tempCanvas.getContext('2d');
                    tempCtx.drawImage(img, 0, 0, size, size);
                    
                    tempCanvas.toBlob(function(blob) {
                        folder.file(`icon-${size}x${size}.png`, blob);
                        resolve();
                    }, 'image/png');
                });
            });
            
            // When all PNGs are created, generate the zip file
            Promise.all(promises).then(() => {
                zip.generateAsync({type:"blob"}).then(function(content) {
                    const a = document.createElement('a');
                    a.href = URL.createObjectURL(content);
                    a.download = "icon-files.zip";
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                });
            });
        });
    </script>
    
    <!-- Include JSZip library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
</body>
</html>
