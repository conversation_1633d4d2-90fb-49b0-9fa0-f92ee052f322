<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Transistor Explorer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            margin: 0;
            padding: 20px;
            background-color: #f0f2f5;
            color: #333;
            line-height: 1.6;
        }

        .app-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #1a73e8; /* A pleasant blue */
            margin-bottom: 30px;
        }
        h2 {
            text-align: center;
            color: #3c4043; /* Dark Gray */
            margin-top: 0;
            margin-bottom: 15px;
        }

        .transistor-section-container {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            justify-content: space-around;
        }

        .transistor-card {
            flex: 1;
            min-width: 320px; /* Ensures cards don't get too narrow */
            max-width: 500px; /* Prevents cards from becoming too wide on large screens */
            background-color: #fff;
            border: 1px solid #dadce0; /* Google Grey for borders */
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.04), 0 1px 2px rgba(0,0,0,0.08);
            display: flex;
            flex-direction: column;
            align-items: center; /* Center content within the card */
        }

        .symbol-and-layers {
            display: flex;
            align-items: center; /* Vertically align symbol and layers */
            justify-content: center; /* Center them horizontally */
            gap: 25px; /* Space between symbol and layers */
            margin-bottom: 20px;
            width: 100%;
        }

        .symbol-container {
            cursor: pointer;
            padding: 10px;
            border: 2px dashed #a0c3ff; /* Lighter blue for dashed border */
            border-radius: 8px;
            transition: background-color 0.3s, border-color 0.3s;
            background-color: #f8f9fa; /* Very light grey */
        }
        .symbol-container:hover {
            background-color: #e8f0fe; /* Light blue hover */
            border-color: #1a73e8; /* Main blue for border on hover */
        }

        .transistor-svg {
            width: 130px; 
            height: 170px;
            display: block; 
            margin: 0 auto; 
        }

        .transistor-svg .terminal-line { stroke: #3c4043; stroke-width: 2.5; }
        .transistor-svg .arrow { stroke: #3c4043; stroke-width: 2.5; fill: none; }
        .transistor-svg .label {
            font-size: 16px;
            font-family: "Roboto Mono", monospace; /* Monospace for terminal labels */
            font-weight: bold;
            fill: #202124; /* Even darker gray for labels */
            user-select: none; /* Prevent text selection on labels */
        }

        .layers-diagram {
            display: flex;
            flex-direction: column;
            align-items: center;
            border: 1px solid #ccc;
            padding: 8px;
            border-radius: 6px;
            background-color: #f8f9fa;
        }

        .layer {
            width: 60px;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            color: white;
            margin: 3px 0;
            border-radius: 4px;
            font-size: 1.1em;
        }

        .n-layer { background-color: #34a853; /* Google Green */ }
        .p-layer { background-color: #ea4335; /* Google Red */ }

        .description {
            font-size: 0.95em;
            width: 100%; /* Take full width of card */
            color: #5f6368; /* Medium Gray for text */
        }
        .description h3 {
            margin-top: 15px;
            margin-bottom: 8px;
            font-size: 1.15em;
            color: #1a73e8; /* Blue for subheadings */
        }
        .description p {
            margin: 8px 0;
        }
        .description strong {
            color: #3c4043; /* Darker text for strong elements */
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            body { padding: 10px; }
            .app-container { padding: 15px; }
            .transistor-section-container {
                flex-direction: column; /* Stack cards vertically */
                align-items: center; /* Center cards when stacked */
            }
            .transistor-card {
                width: 95%; /* Allow cards to take more width */
                max-width: 450px; 
                margin-bottom: 20px;
            }
            .symbol-and-layers {
                flex-direction: column; /* Stack symbol and layers diagram */
                gap: 15px;
            }
            .transistor-svg {
                width: 110px;
                height: 150px;
            }
            .layer { width: 50px; padding: 10px 5px; font-size: 1em; }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <h1>Interactive Transistor Explorer</h1>

        <div class="transistor-section-container">
            <!-- NPN Transistor -->
            <div class="transistor-card" id="npn-card">
                <h2>NPN Transistor</h2>
                <div class="symbol-and-layers">
                    <div class="symbol-container" id="npn-symbol-container" title="Click to animate NPN current flow">
                        <svg viewBox="0 0 150 200" class="transistor-svg" id="npn-svg">
                            <!-- Symbol lines -->
                            <line x1="75" y1="20" x2="75" y2="100" class="terminal-line" /> <!-- Collector to junction -->
                            <line x1="75" y1="100" x2="75" y2="180" class="terminal-line" /> <!-- Junction to Emitter -->
                            <line x1="30" y1="100" x2="75" y2="100" class="terminal-line" /> <!-- Base to junction -->

                            <!-- NPN Emitter Arrow (points OUT from junction) -->
                            <polyline points="85,115 75,125 65,115" class="arrow"/>

                            <!-- Labels -->
                            <text x="80" y="30" class="label">C</text>
                            <text x="10" y="105" class="label">B</text>
                            <text x="80" y="170" class="label">E</text>

                            <!-- Animation Elements -->
                            <!-- Base Current Path (visual guide) -->
                            <path id="npn-base-current-path-visual" d="M10,100 H75" stroke="#007bff" stroke-width="2" visibility="hidden"/>
                            <!-- Collector-Emitter Current Path (visual guide) -->
                            <path id="npn-ce-current-path-visual" d="M75,20 V180" stroke="#dc3545" stroke-width="4" visibility="hidden"/>

                            <!-- Animated "Arrows" (circles representing current carriers) -->
                            <circle id="npn-base-arrow" r="3.5" fill="#007bff" visibility="hidden">
                                <animateMotion dur="1.5s" repeatCount="indefinite" path="M10,100 H75" />
                            </circle>
                            <circle id="npn-ce-arrow1" r="5" fill="#dc3545" visibility="hidden">
                                <animateMotion dur="1.2s" repeatCount="indefinite" path="M75,20 V100 V180" />
                            </circle>
                            <circle id="npn-ce-arrow2" r="5" fill="#dc3545" visibility="hidden">
                                <animateMotion dur="1.2s" begin="0.4s" repeatCount="indefinite" path="M75,20 V100 V180" />
                            </circle>
                            <circle id="npn-ce-arrow3" r="5" fill="#dc3545" visibility="hidden">
                                <animateMotion dur="1.2s" begin="0.8s" repeatCount="indefinite" path="M75,20 V100 V180" />
                            </circle>
                        </svg>
                    </div>
                    <div class="layers-diagram">
                        <div class="layer n-layer">N</div>
                        <div class="layer p-layer">P</div>
                        <div class="layer n-layer">N</div>
                    </div>
                </div>
                <div class="description">
                    <h3>Operating Modes (NPN):</h3>
                    <p><strong>Cut-off:</strong> No (or very little) Base current (I<sub>B</sub> ≈ 0). The transistor acts like an open switch, and no significant Collector current (I<sub>C</sub> ≈ 0) flows from Collector to Emitter.</p>
                    <p><strong>Active:</strong> A small conventional current flows into the Base (I<sub>B</sub> > 0). This controls a much larger conventional current (I<sub>C</sub> = β * I<sub>B</sub>) flowing from Collector to Emitter. The transistor acts as an amplifier.</p>
                    <p><strong>Saturation:</strong> The Base current (I<sub>B</sub>) is high enough that the Collector current (I<sub>C</sub>) reaches its maximum, limited by the external circuit, not by I<sub>B</sub>. The transistor acts like a closed switch (fully ON) between Collector and Emitter.</p>
                </div>
            </div>

            <!-- PNP Transistor -->
            <div class="transistor-card" id="pnp-card">
                <h2>PNP Transistor</h2>
                <div class="symbol-and-layers">
                    <div class="symbol-container" id="pnp-symbol-container" title="Click to animate PNP current flow">
                        <svg viewBox="0 0 150 200" class="transistor-svg" id="pnp-svg">
                            <!-- Symbol lines -->
                            <line x1="75" y1="20" x2="75" y2="100" class="terminal-line" /> <!-- Emitter to junction -->
                            <line x1="75" y1="100" x2="75" y2="180" class="terminal-line" /> <!-- Junction to Collector -->
                            <line x1="30" y1="100" x2="75" y2="100" class="terminal-line" /> <!-- Base to junction -->

                            <!-- PNP Emitter Arrow (points IN towards junction) -->
                            <polyline points="65,35 75,25 85,35" class="arrow"/>

                            <!-- Labels -->
                            <text x="80" y="30" class="label">E</text>
                            <text x="10" y="105" class="label">B</text>
                            <text x="80" y="170" class="label">C</text>

                            <!-- Animation Elements -->
                            <!-- Base Current Path (visual guide, current flows OUT of base) -->
                            <path id="pnp-base-current-path-visual" d="M75,100 H10" stroke="#007bff" stroke-width="2" visibility="hidden"/>
                            <!-- Emitter-Collector Current Path (visual guide) -->
                            <path id="pnp-ec-current-path-visual" d="M75,20 V180" stroke="#dc3545" stroke-width="4" visibility="hidden"/>

                            <!-- Animated "Arrows" (circles representing current carriers) -->
                            <circle id="pnp-base-arrow" r="3.5" fill="#007bff" visibility="hidden">
                                <animateMotion dur="1.5s" repeatCount="indefinite" path="M75,100 H10" />
                            </circle>
                            <circle id="pnp-ec-arrow1" r="5" fill="#dc3545" visibility="hidden">
                                <animateMotion dur="1.2s" repeatCount="indefinite" path="M75,20 V100 V180" />
                            </circle>
                             <circle id="pnp-ec-arrow2" r="5" fill="#dc3545" visibility="hidden">
                                <animateMotion dur="1.2s" begin="0.4s" repeatCount="indefinite" path="M75,20 V100 V180" />
                            </circle>
                             <circle id="pnp-ec-arrow3" r="5" fill="#dc3545" visibility="hidden">
                                <animateMotion dur="1.2s" begin="0.8s" repeatCount="indefinite" path="M75,20 V100 V180" />
                            </circle>
                        </svg>
                    </div>
                    <div class="layers-diagram">
                        <div class="layer p-layer">P</div>
                        <div class="layer n-layer">N</div>
                        <div class="layer p-layer">P</div>
                    </div>
                </div>
                <div class="description">
                    <h3>Operating Modes (PNP):</h3>
                    <p><strong>Cut-off:</strong> No (or very little) conventional current flows out of the Base (I<sub>B</sub> ≈ 0). The transistor acts like an open switch, and no significant current flows from Emitter to Collector (I<sub>C</sub> ≈ 0).</p>
                    <p><strong>Active:</strong> A small conventional current flows out of the Base (I<sub>B</sub> > 0). This controls a much larger conventional current (I<sub>C</sub> = β * I<sub>B</sub>) flowing from Emitter to Collector. The transistor acts as an amplifier.</p>
                    <p><strong>Saturation:</strong> The conventional current flowing out of the Base (I<sub>B</sub>) is high enough that the Emitter-Collector current (I<sub>C</sub>) reaches its maximum, limited by the external circuit. The transistor acts like a closed switch (fully ON) between Emitter and Collector.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const npnSymbolContainer = document.getElementById('npn-symbol-container');
            const pnpSymbolContainer = document.getElementById('pnp-symbol-container');

            // NPN Animation Elements
            const npnBasePathVisual = document.getElementById('npn-base-current-path-visual');
            const npnCePathVisual = document.getElementById('npn-ce-current-path-visual');
            const npnBaseArrow = document.getElementById('npn-base-arrow');
            const npnCeArrow1 = document.getElementById('npn-ce-arrow1');
            const npnCeArrow2 = document.getElementById('npn-ce-arrow2');
            const npnCeArrow3 = document.getElementById('npn-ce-arrow3');
            const npnAnimationElements = [npnBasePathVisual, npnCePathVisual, npnBaseArrow, npnCeArrow1, npnCeArrow2, npnCeArrow3];

            // PNP Animation Elements
            const pnpBasePathVisual = document.getElementById('pnp-base-current-path-visual');
            const pnpEcPathVisual = document.getElementById('pnp-ec-current-path-visual');
            const pnpBaseArrow = document.getElementById('pnp-base-arrow');
            const pnpEcArrow1 = document.getElementById('pnp-ec-arrow1');
            const pnpEcArrow2 = document.getElementById('pnp-ec-arrow2');
            const pnpEcArrow3 = document.getElementById('pnp-ec-arrow3');
            const pnpAnimationElements = [pnpBasePathVisual, pnpEcPathVisual, pnpBaseArrow, pnpEcArrow1, pnpEcArrow2, pnpEcArrow3];

            let npnAnimating = false;
            let pnpAnimating = false;

            function setAnimationVisibility(elements, isVisible) {
                elements.forEach(el => {
                    el.setAttribute('visibility', isVisible ? 'visible' : 'hidden');
                    // SMIL animations typically restart when their visibility changes or their parent's does.
                    // Explicitly calling beginElement() can sometimes be problematic or unnecessary.
                });
            }

            npnSymbolContainer.addEventListener('click', () => {
                npnAnimating = !npnAnimating; // Toggle state
                setAnimationVisibility(npnAnimationElements, npnAnimating);
                
                // If NPN animation is started and PNP is also animating, stop PNP.
                if (npnAnimating && pnpAnimating) {
                    pnpAnimating = false;
                    setAnimationVisibility(pnpAnimationElements, false);
                }
            });

            pnpSymbolContainer.addEventListener('click', () => {
                pnpAnimating = !pnpAnimating; // Toggle state
                setAnimationVisibility(pnpAnimationElements, pnpAnimating);

                // If PNP animation is started and NPN is also animating, stop NPN.
                if (pnpAnimating && npnAnimating) {
                    npnAnimating = false;
                    setAnimationVisibility(npnAnimationElements, false);
                }
            });

            // Initialize: hide all animations
            setAnimationVisibility(npnAnimationElements, false);
            setAnimationVisibility(pnpAnimationElements, false);
        });
    </script>
</body>
</html>
