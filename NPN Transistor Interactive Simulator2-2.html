<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NPN Transistor Simulator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f0f2f5;
            color: #1c1e21;
            display: flex;
            justify-content: center;
        }
        .app-container {
            background: #ffffff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1), 0 8px 16px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 950px;
        }
        h1 {
            color: #1877f2; /* A Facebook-like blue for title */
            text-align: center;
            margin-bottom: 25px;
            font-size: 1.8em;
        }
        h2 {
            color: #333;
            text-align: left;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 8px;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        .main-layout {
            display: flex;
            flex-wrap: wrap;
            gap: 25px;
        }
        .diagram-section {
            flex: 1.2; /* Give slightly more space to diagram */
            min-width: 320px;
            padding: 20px;
            border: 1px solid #ccd0d5;
            border-radius: 6px;
            background-color: #f8f9fa;
        }
        .diagram-section svg {
            width: 100%;
            height: auto;
            max-height: 250px; 
        }
        .controls-results-section {
            flex: 1;
            min-width: 300px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .controls, .results, .status {
            padding: 20px;
            border: 1px solid #ccd0d5;
            border-radius: 6px;
            background-color: #f8f9fa;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #4b4f56;
        }
        input[type="range"] {
            width: 100%;
            margin-bottom: 10px;
            cursor: pointer;
        }
        .results p, .status p {
            margin: 10px 0;
            font-size: 0.98em;
            color: #333;
        }
        .results span, #vInValue {
            font-weight: bold;
            color: #1877f2;
        }
        .led-container {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px;
            background-color: #e9ecef;
            border-radius: 4px;
        }
        #led {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background-color: #d0d0d0; /* Off state */
            margin-right: 12px;
            border: 2px solid #adb5bd;
            transition: background-color 0.2s ease, box-shadow 0.2s ease;
        }
        #statusText {
            font-weight: 600;
            font-size: 1.05em;
            color: #000;
        }
        #thresholdInfo {
            font-size: 0.88em;
            color: #606770;
            padding-top: 5px;
            border-top: 1px dashed #ddd;
            margin-top: 10px;
        }

        /* Responsive adjustments */
        @media (max-width: 800px) {
            .main-layout {
                flex-direction: column;
            }
            .diagram-section, .controls-results-section {
                min-width: 100%;
            }
            h1 { font-size: 1.6em; }
            h2 { font-size: 1.2em; }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <h1>NPN Transistor Interactive Simulator</h1>

        <div class="main-layout">
            <div class="diagram-section">
                <h2>Circuit Diagram</h2>
                <svg id="transistorDiagram" viewBox="0 0 300 200" preserveAspectRatio="xMidYMid meet">
                    <!-- Vcc Source -->
                    <text x="150" y="12" font-size="10" text-anchor="middle" font-weight="500">Vcc (+5V)</text>
                    <line x1="150" y1="15" x2="150" y2="25" stroke="black" stroke-width="1"/>

                    <!-- Rc (Collector Resistor) -->
                    <rect x="140" y="25" width="20" height="25" stroke="black" fill="#fff" stroke-width="1"/>
                    <text x="172" y="38" font-size="9">Rc=1kΩ</text>
                    <line x1="150" y1="50" x2="150" y2="60" stroke="black" stroke-width="1"/> <!-- Wire to Collector -->

                    <!-- Transistor Symbol -->
                    <circle cx="150" cy="85" r="20" stroke="black" stroke-width="1.5" fill="#e8f3ff"/>
                    <!-- Collector line -->
                    <line x1="150" y1="60" x2="150" y2="65" stroke="black" stroke-width="1.5"/> <!-- Connection to circle -->
                    <line x1="150" y1="65" x2="158" y2="73" stroke="black" stroke-width="1.5"/> <!-- C internal -->
                    <text x="162" y="69" font-size="12" font-weight="bold">C</text>

                    <!-- Base line -->
                    <line x1="130" y1="85" x2="120" y2="85" stroke="black" stroke-width="1.5"/> <!-- B internal -->
                    <text x="100" y="90" font-size="12" font-weight="bold">B</text>
                    <line x1="120" y1="85" x2="70" y2="85" stroke="black" stroke-width="1"/> <!-- Wire from Base -->

                    <!-- Emitter line -->
                    <line x1="150" y1="105" x2="158" y2="97" stroke="black" stroke-width="1.5"/> <!-- E internal -->
                    <text x="162" y="108" font-size="12" font-weight="bold">E</text>
                    <line x1="150" y1="105" x2="150" y2="125" stroke="black" stroke-width="1"/> <!-- Wire from Emitter -->
                    <!-- Arrow for NPN (pointing out of Emitter) -->
                    <polygon points="158,97 164,105 152,103" fill="black" stroke="black" stroke-width="1"/>

                    <!-- Rb (Base Resistor) -->
                    <line x1="70" y1="85" x2="60" y2="85" stroke="black" stroke-width="1"/>
                    <rect x="40" y="77.5" width="20" height="15" stroke="black" fill="#fff" stroke-width="1"/>
                    <text x="38" y="73" font-size="9" text-anchor="middle">Rb=10kΩ</text>
                    <line x1="40" y1="85" x2="30" y2="85" stroke="black" stroke-width="1"/>

                    <!-- Base Input Voltage Source (Slider) -->
                    <text x="15" y="80" font-size="9" text-anchor="middle">V_in</text>
                    <circle cx="25" cy="85" r="4" stroke="black" fill="#d0d0d0" stroke-width="1"/> <!-- Input terminal -->
                    <line x1="25" y1="85" x2="30" y2="85" stroke="black" stroke-width="1"/>

                    <!-- Ground Connection -->
                    <line x1="150" y1="125" x2="150" y2="135" stroke="black" stroke-width="1"/>
                    <line x1="135" y1="135" x2="165" y2="135" stroke="black" stroke-width="1.5"/>
                    <line x1="140" y1="140" x2="160" y2="140" stroke="black" stroke-width="1.5"/>
                    <line x1="145" y1="145" x2="155" y2="145" stroke="black" stroke-width="1.5"/>
                    <text x="150" y="160" font-size="9" text-anchor="middle">GND (0V)</text>
                </svg>
            </div>

            <div class="controls-results-section">
                <div class="controls">
                    <h2>Controls</h2>
                    <label for="vInSlider">Base Input Voltage (V_in): <span id="vInValue">0.00</span> V</label>
                    <input type="range" id="vInSlider" min="0" max="5" step="0.01" value="0.00">
                </div>

                <div class="results">
                    <h2>Calculated Values</h2>
                    <p>Voltage at Base (Vb): <span id="vbValue">0.00</span> V</p>
                    <p>Voltage at Collector (Vc): <span id="vcValue">5.00</span> V</p>
                    <p>Base Current (Ib): <span id="ibValue">0.00</span> µA</p>
                    <p>Collector Current (Ic): <span id="icValue">0.00</span> mA</p>
                </div>

                <div class="status">
                    <h2>Transistor Status</h2>
                    <div class="led-container">
                        <div id="led"></div>
                        <span>LED (Collector Current Flow)</span>
                    </div>
                    <p id="statusText">Cut-off: Transistor acts as an open switch. No current flows.</p>
                    <p id="thresholdInfo">Turn-on (Vbe_on): ~0.7V. Saturation (Vc_sat): ~0.2V.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const vInSlider = document.getElementById('vInSlider');
            const vInValueDisplay = document.getElementById('vInValue');
            
            const vbValueDisplay = document.getElementById('vbValue');
            const vcValueDisplay = document.getElementById('vcValue');
            const ibValueDisplay = document.getElementById('ibValue');
            const icValueDisplay = document.getElementById('icValue');
            
            const led = document.getElementById('led');
            const statusTextDisplay = document.getElementById('statusText');

            // Transistor and circuit parameters
            const VCC = 5.0;       // Volts, Collector Supply Voltage
            const RC = 1000;     // Ohms (1kΩ), Collector Resistor
            const RB = 10000;    // Ohms (10kΩ), Base Resistor
            const BETA = 100;      // DC Current Gain (hFE)
            const VBE_ON = 0.7;  // Volts, Base-Emitter turn-on voltage
            const VCE_SAT = 0.2; // Volts, Collector-Emitter saturation voltage

            // Calculate the V_in value at which saturation begins for LED brightness scaling
            const IC_SAT_MAX = (VCC - VCE_SAT) / RC;
            const IB_FOR_SATURATION = IC_SAT_MAX / BETA;
            // Ensure IB_FOR_SATURATION is not negative or zero if BETA is very large or IC_SAT_MAX is small
            const V_IN_FOR_SATURATION_ONSET = (IB_FOR_SATURATION > 0) ? (IB_FOR_SATURATION * RB) + VBE_ON : VBE_ON;


            function updateSimulation() {
                const vIn = parseFloat(vInSlider.value);
                vInValueDisplay.textContent = vIn.toFixed(2);

                let vb_actual, ib, ic, vc_actual;
                let statusMessage = "";
                let ledColor = "rgba(128, 0, 0, 0.2)"; // Default: Dim Red (Cut-off like)
                let ledBoxShadow = "0 0 5px rgba(128, 0, 0, 0.2)";

                if (vIn <= VBE_ON) {
                    // --- Cut-off Region ---
                    vb_actual = vIn; // No current through Rb, so Vb_actual = V_in
                    ib = 0;
                    ic = 0;
                    vc_actual = VCC; // No current through Rc, so Vc_actual = Vcc
                    statusMessage = "Cut-off: Transistor acts as an open switch. Base voltage (Vb) is too low (<0.7V) to turn on the base-emitter junction. No base current (Ib) flows, hence no collector current (Ic) flows. Collector voltage (Vc) is high (equal to Vcc).";
                    ledColor = "rgba(220, 20, 60, 0.3)"; // Dim Crimson
                    ledBoxShadow = "0 0 6px rgba(220, 20, 60, 0.3)";
                } else {
                    // Transistor is potentially on (Active or Saturation)
                    vb_actual = VBE_ON; // Base-Emitter junction clamps Vb_actual (simplified model for "on" state)
                    ib = (vIn - vb_actual) / RB;
                    if (ib < 0) ib = 0; // Safety, should not occur if vIn > VBE_ON

                    let ic_potential_active = BETA * ib;
                    let vc_potential_if_active = VCC - (ic_potential_active * RC);

                    if (vc_potential_if_active <= VCE_SAT) {
                        // --- Saturation Region ---
                        vc_actual = VCE_SAT;
                        ic = (VCC - vc_actual) / RC; 
                        // In saturation, Ic is limited by the collector circuit, not Beta*Ib.
                        // It's possible Ic < Beta*Ib if base is overdriven.
                        statusMessage = `Saturation: Transistor acts like a fully closed switch. It is 'fully on'. Collector current (Ic) is at its maximum, limited by the collector resistor (Rc) and Vce_sat. Collector voltage (Vc) is very low (~${VCE_SAT}V). Further increases in V_in (and thus Ib) do not significantly increase Ic.`;
                        ledColor = "rgba(50, 205, 50, 1)"; // Bright LimeGreen
                        ledBoxShadow = "0 0 15px rgba(50, 205, 50, 0.9)";
                    } else {
                        // --- Active Region ---
                        ic = ic_potential_active;
                        vc_actual = vc_potential_if_active;
                        statusMessage = "Active: Transistor acts as an amplifier. The base-emitter junction is forward-biased (Vb ≈ 0.7V). A small base current (Ib) controls a larger collector current (Ic = β * Ib). Collector voltage (Vc) varies inversely with Ic, acting like a variable resistor.";
                        
                        let brightnessFactor = 0.0;
                        if (V_IN_FOR_SATURATION_ONSET > VBE_ON) { // Avoid division by zero if VBE_ON is somehow V_IN_FOR_SATURATION_ONSET
                             brightnessFactor = (vIn - VBE_ON) / (V_IN_FOR_SATURATION_ONSET - VBE_ON);
                        }
                        brightnessFactor = Math.min(1, Math.max(0, brightnessFactor)); // Clamp between 0 and 1
                        
                        let greenComponent = Math.round(50 + brightnessFactor * 155); // From dark green to brighter green
                        let opacityComponent = 0.4 + brightnessFactor * 0.6; // From 0.4 to 1.0
                        ledColor = `rgba(0, ${greenComponent}, 0, ${opacityComponent})`;
                        ledBoxShadow = `0 0 ${Math.round(5 + brightnessFactor * 10)}px rgba(0, ${greenComponent}, 0, ${opacityComponent * 0.8})`;
                    }
                }
                
                // Final clamping for vc_actual (though logic should mostly handle it)
                vc_actual = Math.max( (ic > 0 ? VCE_SAT : VCC) , Math.min(VCC, vc_actual));
                if (ic === 0) vc_actual = VCC; // Ensure Vc is VCC in perfect cut-off


                vbValueDisplay.textContent = vb_actual.toFixed(2);
                vcValueDisplay.textContent = vc_actual.toFixed(2);
                ibValueDisplay.textContent = (ib * 1e6).toFixed(2); // Convert Amps to microAmps (µA)
                icValueDisplay.textContent = (ic * 1e3).toFixed(2); // Convert Amps to milliAmps (mA)
                
                statusTextDisplay.textContent = statusMessage;
                led.style.backgroundColor = ledColor;
                led.style.boxShadow = ledBoxShadow;
            }

            vInSlider.addEventListener('input', updateSimulation);
            updateSimulation(); // Initial call to set values based on slider's default
        });
    </script>
</body>
</html>
