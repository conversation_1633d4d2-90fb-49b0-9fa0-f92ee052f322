<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مستكشف أوضاع تشغيل الترانزستور NPN BJT</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        /* أنماط إضافية لهذه الصفحة إذا لزم الأمر */
        .mode-explanation {
            padding: 15px;
            margin-top: 10px;
            border-radius: 4px;
        }
        .cutoff-mode { background-color: #ffebee; border-left: 5px solid #c62828; }
        .active-mode { background-color: #e8f5e9; border-left: 5px solid #2e7d32; }
        .saturation-mode { background-color: #e3f2fd; border-left: 5px solid #1565c0; }
    </style>
</head>
<body>
    <header>
        <h1>مستكشف أوضاع تشغيل الترانزستور NPN BJT</h1>
    </header>

    <nav>
        <ul>
            <li><a href="index.html">الرئيسية</a></li>
            <li><a href="simulation.html">بدء المحاكاة</a></li>
            <li><a href="NPN Transistor Behavior Explorer.html">مستكشف سلوك NPN</a></li>
        </ul>
    </nav>

    <main>
        <section id="modes-intro">
            <h2>فهم أوضاع تشغيل الترانزستور NPN</h2>
            <p>يعمل الترانزستور NPN BJT في ثلاثة أوضاع رئيسية: القطع (Cutoff)، الفعال (Active)، والتشبع (Saturation). فهم هذه الأوضاع ضروري لتصميم الدوائر الإلكترونية المختلفة مثل المفاتيح والمضخمات.</p>

            <div class="theory-box">
                <h3>الأهمية العملية لأوضاع التشغيل</h3>
                <ul>
                    <li><strong>وضع القطع:</strong> يستخدم في دوائر المفاتيح الإلكترونية عندما نريد إيقاف تشغيل الحمل تماماً (مثل إطفاء LED أو محرك).</li>
                    <li><strong>الوضع الفعال:</strong> يستخدم في دوائر التضخيم حيث يكون الترانزستور قادراً على تضخيم الإشارات الصغيرة بشكل خطي.</li>
                    <li><strong>وضع التشبع:</strong> يستخدم في دوائر المفاتيح الإلكترونية عندما نريد تشغيل الحمل بأقصى كفاءة (مثل تشغيل LED أو محرك).</li>
                </ul>

                <h3>تطبيقات صناعية</h3>
                <ul>
                    <li><strong>أنظمة التحكم الصناعية:</strong> تستخدم الترانزستورات في وضعي القطع والتشبع للتحكم في المحركات والصمامات.</li>
                    <li><strong>أنظمة الاتصالات:</strong> تستخدم الترانزستورات في الوضع الفعال لتضخيم إشارات الراديو والهاتف.</li>
                    <li><strong>أجهزة القياس الإلكترونية:</strong> تستخدم الترانزستورات في الوضع الفعال لمعالجة إشارات المستشعرات.</li>
                    <li><strong>أنظمة الطاقة:</strong> تستخدم الترانزستورات في وضعي القطع والتشبع للتحكم في تحويل الطاقة.</li>
                </ul>
            </div>
        </section>

        <section id="modes-simulation-area">
            <h2>محاكاة تفاعلية لأوضاع التشغيل</h2>
            <p>تحكم في جهد القاعدة (V<sub>BE</sub>) وجهد المجمع-الباعث (V<sub>CE</sub>) ولاحظ كيف ينتقل الترانزستور بين الأوضاع المختلفة.</p>

            <div class="interactive-demo">
                <div class="simulation-tabs">
                    <button type="button" class="tab-button active" data-tab="basic-simulation">المحاكاة الأساسية</button>
                    <button type="button" class="tab-button" data-tab="advanced-simulation">المحاكاة المتقدمة</button>
                    <button type="button" class="tab-button" data-tab="characteristic-curves">منحنيات الخواص</button>
                </div>

                <div class="tab-content active" id="basic-simulation">
                    <div class="simulation-area">
                        <div class="simulation-controls">
                            <div>
                                <label for="vbe-slider">جهد القاعدة-الباعث (V<sub>BE</sub>):</label>
                                <input type="range" id="vbe-slider" min="0" max="1.2" step="0.05" value="0.7">
                                <span id="vbe-value">0.70 V</span>
                            </div>
                            <div>
                                <label for="vce-slider">جهد المجمع-الباعث (V<sub>CE</sub>):</label>
                                <input type="range" id="vce-slider" min="0" max="10" step="0.1" value="5">
                                <span id="vce-value">5.0 V</span>
                            </div>
                            <div class="preset-buttons">
                                <button type="button" class="preset-button" data-vbe="0.2" data-vce="5.0">وضع القطع</button>
                                <button type="button" class="preset-button" data-vbe="0.8" data-vce="5.0">الوضع الفعال</button>
                                <button type="button" class="preset-button" data-vbe="0.8" data-vce="0.2">وضع التشبع</button>
                            </div>
                        </div>

                        <div class="simulation-visualization">
                            <div class="transistor-diagram">
                                <!-- سيتم إضافة رسم تفاعلي للترانزستور هنا -->
                                <svg id="transistor-svg" width="300" height="200">
                                    <!-- سيتم إنشاء هذا بواسطة JavaScript -->
                                </svg>
                            </div>
                        </div>

                        <div id="mode-results" class="mode-results">
                            <h4>النتائج:</h4>
                            <p>تيار القاعدة (I<sub>B</sub>): <span id="ib-result">--</span> µA</p>
                            <p>تيار المجمع (I<sub>C</sub>): <span id="ic-result">--</span> mA</p>
                            <p>نسبة التضخيم (β = I<sub>C</sub>/I<sub>B</sub>): <span id="beta-result">--</span></p>
                            <p><strong>وضع التشغيل الحالي: <span id="current-mode" class="current-mode">--</span></strong></p>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="advanced-simulation">
                    <div class="simulation-area">
                        <p>في المحاكاة المتقدمة، يمكنك التحكم في المزيد من المعلمات:</p>
                        <div class="simulation-controls">
                            <div>
                                <label for="beta-slider">معامل التضخيم (β):</label>
                                <input type="range" id="beta-slider" min="50" max="300" step="10" value="100">
                                <span id="beta-value">100</span>
                            </div>
                            <div>
                                <label for="temp-slider">درجة الحرارة (°C):</label>
                                <input type="range" id="temp-slider" min="0" max="100" step="5" value="25">
                                <span id="temp-value">25 °C</span>
                            </div>
                            <div>
                                <label for="rb-slider">مقاومة القاعدة (R<sub>B</sub>):</label>
                                <input type="range" id="rb-slider" min="1" max="100" step="1" value="10">
                                <span id="rb-value">10 kΩ</span>
                            </div>
                            <div>
                                <label for="rc-slider">مقاومة المجمع (R<sub>C</sub>):</label>
                                <input type="range" id="rc-slider" min="0.1" max="10" step="0.1" value="1">
                                <span id="rc-value">1.0 kΩ</span>
                            </div>
                            <div>
                                <label for="vcc-slider">جهد المصدر (V<sub>CC</sub>):</label>
                                <input type="range" id="vcc-slider" min="5" max="20" step="1" value="10">
                                <span id="vcc-value">10 V</span>
                            </div>
                        </div>

                        <div id="advanced-results" class="mode-results">
                            <h4>النتائج المتقدمة:</h4>
                            <p>تيار القاعدة (I<sub>B</sub>): <span id="adv-ib-result">--</span> µA</p>
                            <p>تيار المجمع (I<sub>C</sub>): <span id="adv-ic-result">--</span> mA</p>
                            <p>جهد المجمع (V<sub>C</sub>): <span id="adv-vc-result">--</span> V</p>
                            <p>قدرة التبديد (P<sub>D</sub>): <span id="adv-pd-result">--</span> mW</p>
                            <p><strong>وضع التشغيل الحالي: <span id="adv-current-mode" class="current-mode">--</span></strong></p>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="characteristic-curves">
                    <div class="simulation-area">
                        <p>منحنيات الخواص للترانزستور:</p>
                        <div class="curve-selection">
                            <button type="button" class="curve-button active" data-curve="output">منحنى الخرج (I<sub>C</sub> vs V<sub>CE</sub>)</button>
                            <button type="button" class="curve-button" data-curve="input">منحنى الدخل (I<sub>B</sub> vs V<sub>BE</sub>)</button>
                            <button type="button" class="curve-button" data-curve="transfer">منحنى النقل (I<sub>C</sub> vs I<sub>B</sub>)</button>
                        </div>

                        <div class="curve-container">
                            <canvas id="curve-canvas" width="600" height="400"></canvas>
                        </div>

                        <div class="curve-controls">
                            <p>اضبط قيم I<sub>B</sub> لمنحنى الخرج:</p>
                            <div class="ib-values">
                                <label><input type="checkbox" class="ib-checkbox" value="10" checked> 10 µA</label>
                                <label><input type="checkbox" class="ib-checkbox" value="20" checked> 20 µA</label>
                                <label><input type="checkbox" class="ib-checkbox" value="30" checked> 30 µA</label>
                                <label><input type="checkbox" class="ib-checkbox" value="40"> 40 µA</label>
                                <label><input type="checkbox" class="ib-checkbox" value="50"> 50 µA</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="simulation-help">
                    <details>
                        <summary>تعليمات استخدام المحاكاة</summary>
                        <div class="help-content">
                            <p><strong>المحاكاة الأساسية:</strong> استخدم منزلقات V<sub>BE</sub> و V<sub>CE</sub> لتغيير جهود الترانزستور ومراقبة تأثيرها على التيارات ووضع التشغيل.</p>
                            <p><strong>المحاكاة المتقدمة:</strong> تتيح لك التحكم في المزيد من المعلمات مثل β ودرجة الحرارة ومقاومات الدائرة.</p>
                            <p><strong>منحنيات الخواص:</strong> تعرض العلاقات البيانية بين مختلف معلمات الترانزستور.</p>
                            <p><strong>أزرار الإعدادات المسبقة:</strong> تضبط المنزلقات تلقائياً لعرض أوضاع التشغيل المختلفة.</p>
                        </div>
                    </details>
                </div>
            </div>

            <div class="troubleshooting-tips">
                <h3>نصائح وإرشادات</h3>
                <ul>
                    <li><strong>لا يدخل الترانزستور في وضع القطع؟</strong> تأكد من أن V<sub>BE</sub> أقل من 0.7 فولت (عتبة التشغيل للسيليكون).</li>
                    <li><strong>لا يدخل الترانزستور في وضع التشبع؟</strong> تأكد من أن V<sub>BE</sub> > 0.7 فولت وأن V<sub>CE</sub> منخفض (حوالي 0.2 فولت).</li>
                    <li><strong>تيار المجمع منخفض في الوضع الفعال؟</strong> تحقق من قيمة β وتيار القاعدة، حيث I<sub>C</sub> = β × I<sub>B</sub>.</li>
                    <li><strong>تغير مفاجئ في وضع التشغيل؟</strong> هذا طبيعي عند الانتقال بين المناطق الحدودية لأوضاع التشغيل المختلفة.</li>
                </ul>
            </div>
        </section>

        <section id="circuit-diagram-workbench" class="circuit-diagram-workbench">
            <h2>رسم الدائرة ومنطقة العمل</h2>
            <div class="circuit-diagram-container">
                <p><strong>رسم الدائرة:</strong></p>
                <!-- سيتم استبدال هذا برسم SVG أو صورة للدائرة لاحقًا -->
                <img src="images/placeholder_circuit_npn_modes.png" alt="رسم دائرة مستكشف أوضاع NPN" class="circuit-diagram-image-sm">
            </div>
            <div class="workbench-container">
                <p><strong>منطقة العمل (Workbench):</strong></p>
                <p><em>(سيتم هنا إضافة عناصر تحكم ومكونات تفاعلية لرسم الدائرة وتعديلها)</em></p>
                <!-- مثال لعناصر يمكن إضافتها -->
                <div>
                    <label for="component-select">اختر مكونًا:</label>
                    <select id="component-select">
                        <option value="resistor">مقاومة</option>
                        <option value="capacitor">مكثف</option>
                        <option value="transistor_npn">ترانزستور NPN</option>
                        <option value="voltage_source">مصدر جهد</option>
                    </select>
                    <button id="addComponentButtonNpnModes" type="button">إضافة المكون</button>
                </div>
                <div id="interactive-circuit-drawing-area" class="interactive-circuit-area">
                    <p>مساحة رسم الدائرة (قيد التطوير)</p>
                </div>
            </div>
        </section>

        <section id="modes-explanation-details">
            <h2>شرح تفصيلي لأوضاع التشغيل</h2>

            <div id="cutoff-explanation" class="mode-explanation cutoff-mode">
                <h3>1. وضع القطع (Cutoff Mode)</h3>
                <p>يحدث عندما يكون جهد القاعدة-الباعث (V<sub>BE</sub>) أقل من جهد العتبة (حوالي 0.7 فولت للسيليكون). في هذا الوضع:</p>
                <ul>
                    <li>وصلة القاعدة-الباعث (BEJ) تكون في حالة انحياز عكسي أو انحياز أمامي ضعيف جداً.</li>
                    <li>وصلة المجمع-القاعدة (CBJ) تكون في حالة انحياز عكسي.</li>
                    <li>الترانزستور يكون "مطفأ" (OFF)، ولا يمر تيار يذكر من المجمع إلى الباعث (I<sub>C</sub> ≈ 0).</li>
                    <li>يُستخدم الترانزستور كمفتاح مفتوح في هذا الوضع.</li>
                </ul>
            </div>

            <div id="active-explanation" class="mode-explanation active-mode">
                <h3>2. الوضع الفعال (Active Mode)</h3>
                <p>يحدث عندما يكون جهد القاعدة-الباعث (V<sub>BE</sub>) كافياً (حوالي 0.7 فولت) لجعل وصلة القاعدة-الباعث في انحياز أمامي، ووصلة المجمع-القاعدة في انحياز عكسي (V<sub>CB</sub> > 0، أو V<sub>CE</sub> > V<sub>BE</sub>).</p>
                <ul>
                    <li>وصلة القاعدة-الباعث (BEJ) تكون في حالة انحياز أمامي.</li>
                    <li>وصلة المجمع-القاعدة (CBJ) تكون في حالة انحياز عكسي.</li>
                    <li>تيار المجمع (I<sub>C</sub>) يتناسب طردياً مع تيار القاعدة (I<sub>B</sub>) من خلال العلاقة: I<sub>C</sub> = β * I<sub>B</sub> (حيث β هو كسب التيار).</li>
                    <li>يُستخدم الترانزستور كمضخم للإشارة في هذا الوضع.</li>
                </ul>
            </div>

            <div id="saturation-explanation" class="mode-explanation saturation-mode">
                <h3>3. وضع التشبع (Saturation Mode)</h3>
                <p>يحدث عندما تكون كل من وصلة القاعدة-الباعث ووصلة المجمع-القاعدة في حالة انحياز أمامي. هذا يعني أن V<sub>BE</sub> ≥ 0.7V و V<sub>BC</sub> ≥ 0.7V (أو V<sub>CE</sub> يصبح صغيراً جداً، عادةً V<sub>CE(sat)</sub> ≈ 0.2V).</p>
                <ul>
                    <li>وصلة القاعدة-الباعث (BEJ) تكون في حالة انحياز أمامي.</li>
                    <li>وصلة المجمع-القاعدة (CBJ) تكون أيضاً في حالة انحياز أمامي.</li>
                    <li>الترانزستور يكون "مفتوح بالكامل" (ON)، ويمر أقصى تيار ممكن عبر المجمع، ويتم تحديده بشكل أساسي بواسطة دائرة المجمع الخارجية وليس فقط بتيار القاعدة.</li>
                    <li>العلاقة I<sub>C</sub> = β * I<sub>B</sub> لم تعد صالحة تماماً؛ I<sub>C</sub> < β * I<sub>B</sub>.</li>
                    <li>يُستخدم الترانزستور كمفتاح مغلق في هذا الوضع.</li>
                </ul>
            </div>
        </section>

        <section id="experiment-procedure">
            <h2>الخطوات الأساسية لإجراء التجربة</h2>

            <div class="procedure-container">
                <h3>الهدف من التجربة</h3>
                <p>استيعاب مفهوم أوضاع تشغيل الترانزستور NPN (القطع، الفعال، التشبع) وكيفية التحكم فيها من خلال جهود الانحياز.</p>

                <h3>المتطلبات النظرية</h3>
                <p>قبل البدء في هذه التجربة، يجب أن يكون لديك فهم أساسي لما يلي:</p>
                <ul>
                    <li>تركيب الترانزستور ثنائي القطبية (BJT) ومبدأ عمله الأساسي.</li>
                    <li>مفهوم الانحياز الأمامي والعكسي للوصلات شبه الموصلة.</li>
                    <li>العلاقة بين تيار القاعدة وتيار المجمع (I<sub>C</sub> = β × I<sub>B</sub>).</li>
                    <li>مفهوم جهد العتبة للوصلة القاعدة-باعث (V<sub>BE</sub> ≈ 0.7V للسيليكون).</li>
                </ul>

                <h3>الأدوات والمعدات</h3>
                <p>في هذه التجربة الافتراضية، سنستخدم:</p>
                <ul>
                    <li>محاكي أوضاع تشغيل الترانزستور NPN BJT.</li>
                    <li>منزلقات تحكم لضبط جهد القاعدة-باعث (V<sub>BE</sub>) وجهد المجمع-باعث (V<sub>CE</sub>).</li>
                    <li>مؤشرات لقراءة تيار القاعدة (I<sub>B</sub>) وتيار المجمع (I<sub>C</sub>).</li>
                    <li>مؤشر لتحديد وضع التشغيل الحالي للترانزستور.</li>
                </ul>

                <h3>خطوات التجربة</h3>
                <ol>
                    <li><strong>التعرف على واجهة المحاكاة:</strong>
                        <ul>
                            <li>تعرف على منزلقات التحكم (V<sub>BE</sub> و V<sub>CE</sub>) وكيفية ضبطها.</li>
                            <li>تعرف على مؤشرات القراءة (I<sub>B</sub>، I<sub>C</sub>، وضع التشغيل).</li>
                            <li>لاحظ الشرح المرئي لكل وضع تشغيل في أسفل الصفحة.</li>
                        </ul>
                    </li>

                    <li><strong>الجزء الأول: استكشاف وضع القطع (Cutoff Mode)</strong>
                        <ul>
                            <li>اضبط V<sub>BE</sub> على قيمة أقل من 0.7 فولت (مثلاً 0.2 فولت).</li>
                            <li>غيّر قيمة V<sub>CE</sub> من 0 إلى 10 فولت وسجل قراءات I<sub>B</sub> و I<sub>C</sub> في الجدول 1.</li>
                            <li>لاحظ أن I<sub>B</sub> و I<sub>C</sub> يظلان قريبين من الصفر مهما تغيرت قيمة V<sub>CE</sub>.</li>
                            <li>سجل ملاحظاتك: لماذا يكون الترانزستور في وضع القطع عندما يكون V<sub>BE</sub> أقل من 0.7 فولت؟</li>
                        </ul>
                    </li>

                    <li><strong>الجزء الثاني: استكشاف الوضع الفعال (Active Mode)</strong>
                        <ul>
                            <li>اضبط V<sub>BE</sub> على 0.7 فولت.</li>
                            <li>اضبط V<sub>CE</sub> على 5 فولت.</li>
                            <li>سجل قراءات I<sub>B</sub> و I<sub>C</sub> في الجدول 2.</li>
                            <li>زد قيمة V<sub>BE</sub> تدريجياً (0.75، 0.8، 0.85، 0.9 فولت) مع الحفاظ على V<sub>CE</sub> = 5 فولت.</li>
                            <li>سجل قراءات I<sub>B</sub> و I<sub>C</sub> لكل قيمة، واحسب النسبة I<sub>C</sub>/I<sub>B</sub> (وهي تمثل β).</li>
                            <li>الآن، اضبط V<sub>BE</sub> على 0.8 فولت وغيّر V<sub>CE</sub> (2، 5، 8 فولت) وسجل القراءات.</li>
                            <li>لاحظ كيف يتأثر I<sub>C</sub> بتغير V<sub>CE</sub> في المنطقة الفعالة (تأثير طفيف).</li>
                        </ul>
                    </li>

                    <li><strong>الجزء الثالث: استكشاف وضع التشبع (Saturation Mode)</strong>
                        <ul>
                            <li>اضبط V<sub>BE</sub> على 0.8 فولت.</li>
                            <li>ابدأ بـ V<sub>CE</sub> = 5 فولت (وضع فعال).</li>
                            <li>قلل V<sub>CE</sub> تدريجياً (1.0، 0.5، 0.2، 0.1 فولت).</li>
                            <li>سجل قراءات I<sub>B</sub> و I<sub>C</sub> لكل قيمة في الجدول 3.</li>
                            <li>لاحظ متى يتغير وضع التشغيل من الفعال إلى التشبع.</li>
                            <li>زد V<sub>BE</sub> إلى 1.0 فولت مع إبقاء V<sub>CE</sub> عند 0.2 فولت.</li>
                            <li>لاحظ كيف يتغير I<sub>B</sub> بشكل كبير بينما يتغير I<sub>C</sub> بشكل طفيف.</li>
                            <li>سجل ملاحظاتك: لماذا لا تنطبق العلاقة I<sub>C</sub> = β × I<sub>B</sub> في وضع التشبع؟</li>
                        </ul>
                    </li>

                    <li><strong>الجزء الرابع: رسم منحنيات الخواص</strong>
                        <ul>
                            <li>استخدم البيانات المسجلة لرسم منحنى I<sub>C</sub> مقابل V<sub>CE</sub> لقيم مختلفة من I<sub>B</sub>.</li>
                            <li>حدد مناطق القطع والفعال والتشبع على المنحنى.</li>
                            <li>ارسم منحنى I<sub>C</sub> مقابل I<sub>B</sub> في المنطقة الفعالة وحدد قيمة β من ميل المنحنى.</li>
                        </ul>
                    </li>
                </ol>

                <h3>تحليل النتائج</h3>
                <p>بعد إكمال التجربة، قم بتحليل النتائج من خلال الإجابة على الأسئلة التالية:</p>
                <ol>
                    <li>ما هي الشروط اللازمة لكل وضع من أوضاع تشغيل الترانزستور (القطع، الفعال، التشبع)؟</li>
                    <li>كيف يتأثر تيار المجمع (I<sub>C</sub>) بتغير جهد القاعدة-باعث (V<sub>BE</sub>) في المنطقة الفعالة؟</li>
                    <li>كيف يتأثر تيار المجمع (I<sub>C</sub>) بتغير جهد المجمع-باعث (V<sub>CE</sub>) في المنطقة الفعالة؟</li>
                    <li>لماذا يكون تيار المجمع (I<sub>C</sub>) أقل من β × I<sub>B</sub> في وضع التشبع؟</li>
                    <li>ما هي التطبيقات العملية لكل وضع من أوضاع تشغيل الترانزستور؟</li>
                </ol>

                <h3>التقييم الذاتي</h3>
                <p>أجب على الأسئلة التالية لتقييم فهمك لأوضاع تشغيل الترانزستور:</p>

                <div class="assessment-question">
                    <h4>السؤال 1:</h4>
                    <p>ما هو الشرط الأساسي لدخول الترانزستور في وضع القطع؟</p>
                    <div class="assessment-options">
                        <label><input type="radio" name="q1" value="a"> V<sub>BE</sub> > 0.7V</label>
                        <label><input type="radio" name="q1" value="b"> V<sub>BE</sub> < 0.7V</label>
                        <label><input type="radio" name="q1" value="c"> V<sub>CE</sub> < 0.2V</label>
                        <label><input type="radio" name="q1" value="d"> I<sub>B</sub> = 0</label>
                    </div>
                    <div class="assessment-feedback" id="feedback-q1"></div>
                </div>

                <div class="assessment-question">
                    <h4>السؤال 2:</h4>
                    <p>في الوضع الفعال، ما هي العلاقة الصحيحة؟</p>
                    <div class="assessment-options">
                        <label><input type="radio" name="q2" value="a"> I<sub>C</sub> = I<sub>B</sub></label>
                        <label><input type="radio" name="q2" value="b"> I<sub>C</sub> = β × I<sub>B</sub></label>
                        <label><input type="radio" name="q2" value="c"> I<sub>C</sub> < I<sub>B</sub></label>
                        <label><input type="radio" name="q2" value="d"> I<sub>C</sub> = 0</label>
                    </div>
                    <div class="assessment-feedback" id="feedback-q2"></div>
                </div>

                <div class="assessment-question">
                    <h4>السؤال 3:</h4>
                    <p>ما هي قيمة V<sub>CE</sub> التقريبية في وضع التشبع؟</p>
                    <div class="assessment-options">
                        <label><input type="radio" name="q3" value="a"> 0.7V</label>
                        <label><input type="radio" name="q3" value="b"> 5V</label>
                        <label><input type="radio" name="q3" value="c"> 0.2V</label>
                        <label><input type="radio" name="q3" value="d"> 0V</label>
                    </div>
                    <div class="assessment-feedback" id="feedback-q3"></div>
                </div>

                <div class="assessment-question">
                    <h4>السؤال 4:</h4>
                    <p>أي من أوضاع التشغيل التالية يستخدم في دوائر التضخيم؟</p>
                    <div class="assessment-options">
                        <label><input type="radio" name="q4" value="a"> وضع القطع</label>
                        <label><input type="radio" name="q4" value="b"> الوضع الفعال</label>
                        <label><input type="radio" name="q4" value="c"> وضع التشبع</label>
                        <label><input type="radio" name="q4" value="d"> وضع الانهيار</label>
                    </div>
                    <div class="assessment-feedback" id="feedback-q4"></div>
                </div>

                <div class="assessment-question">
                    <h4>السؤال 5:</h4>
                    <p>ما هو تأثير زيادة درجة الحرارة على جهد العتبة V<sub>BE</sub> للترانزستور؟</p>
                    <div class="assessment-options">
                        <label><input type="radio" name="q5" value="a"> يزداد</label>
                        <label><input type="radio" name="q5" value="b"> ينقص</label>
                        <label><input type="radio" name="q5" value="c"> لا يتغير</label>
                        <label><input type="radio" name="q5" value="d"> يصبح صفراً</label>
                    </div>
                    <div class="assessment-feedback" id="feedback-q5"></div>
                </div>

                <button type="button" id="check-answers-btn" class="button">تحقق من إجاباتك</button>
            </div>
        </section>

        <section id="practical-applications">
            <h2>التطبيقات العملية</h2>

            <div class="practical-application">
                <h3>تطبيق 1: الترانزستور كمفتاح إلكتروني</h3>
                <div class="grid">
                    <div>
                        <p>يستخدم الترانزستور في وضعي القطع والتشبع للعمل كمفتاح إلكتروني في العديد من التطبيقات:</p>
                        <ul>
                            <li><strong>التحكم في LED:</strong> يمكن استخدام الترانزستور للتحكم في تشغيل وإيقاف مصباح LED بناءً على إشارة تحكم صغيرة.</li>
                            <li><strong>التحكم في المحركات:</strong> يمكن استخدام الترانزستور للتحكم في تشغيل وإيقاف محرك كهربائي.</li>
                            <li><strong>دوائر المنطق الرقمية:</strong> تستخدم الترانزستورات في بناء البوابات المنطقية الأساسية مثل AND و OR و NOT.</li>
                        </ul>
                        <p>في هذه التطبيقات، يكون الترانزستور إما في وضع القطع (مفتوح) أو في وضع التشبع (مغلق).</p>
                    </div>
                    <div>
                        <img src="images/placeholder_circuit_transistor_switch.png" alt="الترانزستور كمفتاح" class="circuit-diagram-image-sm">
                    </div>
                </div>
            </div>

            <div class="practical-application">
                <h3>تطبيق 2: الترانزستور كمضخم للإشارة</h3>
                <div class="grid">
                    <div>
                        <p>يستخدم الترانزستور في الوضع الفعال كمضخم للإشارات الصغيرة في العديد من التطبيقات:</p>
                        <ul>
                            <li><strong>مضخمات الصوت:</strong> تستخدم الترانزستورات لتضخيم إشارات الصوت في أنظمة الصوت والراديو.</li>
                            <li><strong>مضخمات الإشارة:</strong> تستخدم لتضخيم الإشارات الضعيفة القادمة من المستشعرات المختلفة.</li>
                            <li><strong>دوائر الاتصالات:</strong> تستخدم في أجهزة الإرسال والاستقبال لتضخيم إشارات الراديو.</li>
                        </ul>
                        <p>في هذه التطبيقات، يعمل الترانزستور في المنطقة الفعالة حيث يكون I<sub>C</sub> = β × I<sub>B</sub>.</p>
                    </div>
                    <div>
                        <img src="images/placeholder_circuit_ce_amplifier.png" alt="الترانزستور كمضخم" class="circuit-diagram-image-sm">
                    </div>
                </div>
            </div>

            <div class="practical-application">
                <h3>تطبيق 3: دوائر التغذية المرتدة والتحكم</h3>
                <p>تستخدم الترانزستورات في دوائر التغذية المرتدة والتحكم للحفاظ على استقرار النظام:</p>
                <ul>
                    <li><strong>منظمات الجهد:</strong> تستخدم الترانزستورات للحفاظ على جهد خرج ثابت رغم تغير جهد الدخل أو الحمل.</li>
                    <li><strong>دوائر التحكم في درجة الحرارة:</strong> تستخدم للتحكم في تشغيل وإيقاف أنظمة التبريد أو التسخين بناءً على قراءات المستشعرات.</li>
                    <li><strong>دوائر المذبذبات:</strong> تستخدم لتوليد إشارات متناوبة بترددات محددة.</li>
                </ul>
                <p>في هذه التطبيقات، قد يعمل الترانزستور في أوضاع تشغيل مختلفة اعتماداً على متطلبات التصميم.</p>
            </div>

            <div class="practical-application">
                <h3>مشروع عملي: تصميم دائرة تحكم في LED باستخدام الترانزستور</h3>
                <p>يمكنك تطبيق ما تعلمته في هذه التجربة من خلال تصميم دائرة بسيطة للتحكم في LED باستخدام ترانزستور NPN:</p>
                <ol>
                    <li>استخدم ترانزستور NPN (مثل 2N2222 أو BC547).</li>
                    <li>وصل مقاومة (10kΩ) بين طرف إدخال الإشارة وقاعدة الترانزستور.</li>
                    <li>وصل مقاومة (220Ω) بين المجمع و LED.</li>
                    <li>وصل الطرف الآخر من LED بمصدر جهد (5V).</li>
                    <li>وصل باعث الترانزستور بالأرضي.</li>
                </ol>
                <p>عندما تطبق جهداً أكبر من 0.7V على طرف الإدخال، سيدخل الترانزستور في وضع التشبع ويضيء LED. وعندما تطبق جهداً أقل من 0.7V، سيدخل الترانزستور في وضع القطع وينطفئ LED.</p>
            </div>
        </section>

        <section id="report-writing">
            <h2>كتابة تقرير التجربة</h2>
            <p>يهدف التقرير إلى تلخيص فهمك لأوضاع تشغيل الترانزستور NPN بناءً على المحاكاة التفاعلية. يجب أن يتضمن التقرير العناصر التالية:</p>
            <ul>
                <li><strong>مقدمة:</strong> وصف موجز للترانزستور NPN وأهمية فهم أوضاع تشغيله.</li>
                <li><strong>الأدوات المستخدمة:</strong> ذكر أن التجربة تمت باستخدام محاكي "مستكشف أوضاع تشغيل الترانزستور NPN BJT".</li>
                <li><strong>الخطوات المتبعة:</strong> تلخيص للخطوات التي قمت بها خلال استكشاف الأوضاع المختلفة.</li>
                <li><strong>النتائج والملاحظات:</strong> عرض البيانات التي جمعتها في جداول، مع ملاحظاتك حول كل وضع.</li>
                <li><strong>تحليل النتائج:</strong> شرح كيف تتوافق النتائج مع النظرية الخاصة بكل وضع تشغيل. ناقش العلاقة بين V<sub>BE</sub>، V<sub>CE</sub>، I<sub>B</sub>، I<sub>C</sub>، ووضع التشغيل.</li>
                <li><strong>التطبيقات العملية:</strong> ناقش التطبيقات العملية لكل وضع من أوضاع تشغيل الترانزستور.</li>
                <li><strong>الاستنتاج:</strong> تلخيص لما تعلمته من التجربة.</li>
            </ul>

            <h3>جداول مقترحة لتسجيل النتائج:</h3>
            <h4>جدول 1: استكشاف وضع القطع</h4>
            <table>
                <thead>
                    <tr>
                        <th>V<sub>BE</sub> (V)</th>
                        <th>V<sub>CE</sub> (V)</th>
                        <th>I<sub>B</sub> (µA)</th>
                        <th>I<sub>C</sub> (mA)</th>
                        <th>وضع التشغيل الملاحظ</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>0.2</td>
                        <td>2.0</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>0.4</td>
                        <td>5.0</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>0.6</td>
                        <td>8.0</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <!-- أضف المزيد من الصفوف حسب الحاجة -->
                </tbody>
            </table>

            <h4>جدول 2: استكشاف الوضع الفعال</h4>
            <table>
                <thead>
                    <tr>
                        <th>V<sub>BE</sub> (V)</th>
                        <th>V<sub>CE</sub> (V)</th>
                        <th>I<sub>B</sub> (µA)</th>
                        <th>I<sub>C</sub> (mA)</th>
                        <th>β (I<sub>C</sub>/I<sub>B</sub>)</th>
                        <th>وضع التشغيل الملاحظ</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>0.7</td>
                        <td>2.0</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>0.7</td>
                        <td>5.0</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>0.75</td>
                        <td>5.0</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>0.8</td>
                        <td>8.0</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <!-- أضف المزيد من الصفوف حسب الحاجة -->
                </tbody>
            </table>

            <h4>جدول 3: استكشاف وضع التشبع</h4>
            <table>
                <thead>
                    <tr>
                        <th>V<sub>BE</sub> (V)</th>
                        <th>V<sub>CE</sub> (V)</th>
                        <th>I<sub>B</sub> (µA)</th>
                        <th>I<sub>C</sub> (mA)</th>
                        <th>وضع التشغيل الملاحظ</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>0.8</td>
                        <td>0.5</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>0.8</td>
                        <td>0.2</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>0.9</td>
                        <td>0.1</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                     <tr>
                        <td>1.0</td>
                        <td>0.2</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <!-- أضف المزيد من الصفوف حسب الحاجة -->
                </tbody>
            </table>
        </section>

    </main>

    <footer>
        <p>&copy; 2024 معمل الترانزستور الافتراضي. جميع الحقوق محفوظة.</p>
    </footer>

    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const vbeSlider = document.getElementById('vbe-slider');
            const vceSlider = document.getElementById('vce-slider');
            const vbeValueSpan = document.getElementById('vbe-value');
            const vceValueSpan = document.getElementById('vce-value');
            const ibResultSpan = document.getElementById('ib-result');
            const icResultSpan = document.getElementById('ic-result');
            const currentModeSpan = document.getElementById('current-mode');

            // قيم افتراضية للترانزستور (يمكن تعديلها)
            const BETA = 100; // كسب التيار
            const VBE_THRESHOLD = 0.7; // جهد عتبة القاعدة-باعث
            const VCE_SAT = 0.2; // جهد التشبع مجمع-باعث
            // لتبسيط حساب تيار القاعدة، نفترض مقاومة قاعدة Rb
            const RB_KOHM = 10; // مقاومة القاعدة بالكيلو أوم
            // لتبسيط حساب تيار المجمع في التشبع، نفترض مقاومة مجمع Rc
            const RC_KOHM = 1; // مقاومة المجمع بالكيلو أوم

            function updateOperatingMode() {
                let vbe = parseFloat(vbeSlider.value);
                let vce = parseFloat(vceSlider.value);

                vbeValueSpan.textContent = vbe.toFixed(2) + ' V';
                vceValueSpan.textContent = vce.toFixed(1) + ' V';

                let ib_uA = 0;
                let ic_mA = 0;
                let mode = '';

                if (vbe < VBE_THRESHOLD) {
                    mode = 'القطع (Cutoff)';
                    ib_uA = 0;
                    ic_mA = 0;
                } else {
                    // حساب تيار القاعدة التقريبي
                    // نفترض أن جهد الدخل متصل مباشرة بـ VBE خلال مقاومة القاعدة RB
                    // هذا تبسيط، في الواقع V_input - VBE = IB * RB
                    // لكن هنا VBE هو المدخل المباشر للوصلة
                    ib_uA = ((vbe - VBE_THRESHOLD) / RB_KOHM) * 1000; // تحويل من mA إلى µA
                    ib_uA = Math.max(0, ib_uA); // لا يمكن أن يكون التيار سالبًا

                    let ic_active_mA = (BETA * ib_uA) / 1000; // تحويل من µA إلى mA

                    if (vce <= VCE_SAT) {
                        mode = 'التشبع (Saturation)';
                        // في التشبع، تيار المجمع يحدده VCC و RC (نفترض VCC هو VCE المعطى كحد أقصى)
                        // هذا تبسيط كبير، VCE هو نتيجة وليس مدخل مباشر دائماً
                        // ic_mA = (vceSlider.max - VCE_SAT) / RC_KOHM; // هذا ليس دقيقاً تماماً للطريقة التي يتم بها التحكم
                        // طريقة أفضل: إذا كان VCE أقل من VCE_SAT، فهو في التشبع
                        // وتيار المجمع هو ما تسمح به الدائرة الخارجية، حتى لو كان أقل من beta*IB
                        // هنا، بما أننا نتحكم بـ VCE مباشرة، إذا كان VCE <= VCE_SAT، فهو تشبع.
                        // تيار المجمع في التشبع سيكون محدودًا بالدائرة الخارجية.
                        // لغرض هذه المحاكاة، إذا كان VCE <= VCE_SAT، نعتبره تشبع ونحسب Ic بناءً على VCE.
                        // هذا ليس نموذجًا فيزيائيًا دقيقًا لكيفية عمل الدائرة، بل هو استكشاف للأوضاع.
                        ic_mA = (parseFloat(vceSlider.max) - vce) / RC_KOHM; // مثال لتيار التشبع
                        ic_mA = Math.max(0, ic_mA);
                        // يجب أن يكون تيار التشبع الفعلي أقل من أو يساوي beta * ib
                        // إذا كان beta * ib أقل، فهذا يعني أنه لم يصل للتشبع الكامل بعد أو أن النموذج مبسط
                        ic_mA = Math.min(ic_mA, ic_active_mA);

                    } else if (vce > VBE_THRESHOLD && (vce - vbe) > VCE_SAT ) { // VCB > 0 (تقريبي)
                        // VCB = VCE - VBE. للانحياز العكسي لـ CBJ، VCB > 0 (أو قيمة سالبة صغيرة جداً)
                        // بشكل أدق، طالما VCE > VCE_SAT، فهو في المنطقة الفعالة أو القطع.
                        // وبما أننا تجاوزنا شرط القطع (VBE >= VBE_THRESHOLD)
                        mode = 'الفعال (Active)';
                        ic_mA = ic_active_mA;
                    } else {
                        // هذه الحالة قد تمثل انتقالاً أو منطقة حدودية، أو أن VCE قريب جداً من VBE
                        // إذا كان VCE قريب من VBE ولكن أكبر من VCE_SAT، لا يزال يعتبر فعالاً
                        // إذا كان VCE أقل من VBE (يعني VCB موجب، أي CBJ انحياز أمامي)، فهذا تشبع
                        // الشرط vce <= VCE_SAT يغطي التشبع بشكل جيد
                        // إذا لم يكن قطعاً ولم يكن تشبعاً (VCE > VCE_SAT)، فهو فعال
                        mode = 'الفعال (Active)'; // افتراضي إذا لم يكن قطع أو تشبع
                        ic_mA = ic_active_mA;
                         if (vce <= vbe) { // إذا كان VCE أقل من VBE (VBC موجب) فهذا يقود للتشبع
                             mode = 'التشبع (Saturation)';
                             // إعادة حساب Ic للتشبع إذا لزم الأمر
                             let ic_sat_approx = (parseFloat(vceSlider.max) - VCE_SAT) / RC_KOHM;
                             ic_mA = Math.min(ic_active_mA, ic_sat_approx);
                         }
                    }
                }

                // التأكد من أن ic لا يتجاوز ما يمكن أن يوفره المصدر (تبسيط)
                let max_ic_possible_by_vce = (parseFloat(vceSlider.max) - vce) / RC_KOHM;
                if (mode === 'الفعال (Active)') {
                     ic_mA = Math.min(ic_mA, max_ic_possible_by_vce);
                }

                ibResultSpan.textContent = ib_uA.toFixed(2) + ' µA';
                icResultSpan.textContent = ic_mA.toFixed(2) + ' mA';
                currentModeSpan.textContent = mode;

                // تحديث ألوان الخلفية للشرح بناءً على الوضع الحالي
                document.querySelectorAll('.mode-explanation').forEach(el => el.style.opacity = '0.6');
                if (mode.includes('القطع')) document.getElementById('cutoff-explanation').style.opacity = '1';
                if (mode.includes('الفعال')) document.getElementById('active-explanation').style.opacity = '1';
                if (mode.includes('التشبع')) document.getElementById('saturation-explanation').style.opacity = '1';
            }

            vbeSlider.addEventListener('input', updateOperatingMode);
            vceSlider.addEventListener('input', updateOperatingMode);

            updateOperatingMode(); // Call on load
        });
    </script>
<script src="js/main.js" defer></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const addButton = document.getElementById('addComponentButtonNpnModes');
        const componentSelect = document.getElementById('component-select');

        if (addButton && componentSelect) {
            addButton.addEventListener('click', function() {
                const selectedComponent = componentSelect.value;
                if (typeof addComponent === 'function') {
                    addComponent(selectedComponent);
                } else {
                    console.error('addComponent function is not defined. Make sure js/main.js is loaded.');
                }
            });
        }
    });
</script>
</body>
</html>
