<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transistor Water Tap Analogy</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0;
            padding: 15px;
            background-color: #f0f2f5;
            color: #333;
            line-height: 1.5;
        }
        .app-container {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 550px;
        }
        h1 {
            text-align: center;
            color: #007aff; /* iOS-like blue */
            margin-top: 0;
            margin-bottom: 25px;
            font-size: 1.8em;
        }
        .controls {
            margin-bottom: 25px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f8f8f8;
        }
        .controls label, .controls .radio-group-title {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        .controls input[type="range"] {
            width: 100%;
            margin-bottom: 15px;
            cursor: pointer;
        }
        .controls .radio-group label {
            font-weight: normal;
            margin-right: 15px;
            color: #333;
        }
        .controls input[type="radio"] {
            margin-right: 5px;
            vertical-align: middle;
        }

        .analogy-display {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            grid-template-rows: auto auto auto;
            align-items: stretch; /* Stretch items to fill cell height */
            justify-items: center;
            gap: 0px;
            width: 90%;
            max-width: 320px; /* Reduced max-width for better mobile fit */
            margin: 25px auto;
            padding: 10px;
            border: 1px solid #d1d1d1;
            border-radius: 8px;
            background-color: #f9f9f9;
        }

        .pipe {
            border: 2px solid #888;
            position: relative;
            overflow: hidden;
            background-color: #d6eaf8; /* Lighter blue for pipe background */
        }
        .pipe-label {
            text-align: center;
            font-size: 0.75em;
            padding: 3px 0;
            background-color: rgba(0, 0, 0, 0.05);
            color: #222;
            position: absolute;
            width: 100%;
            top: 0;
            left: 0;
            z-index: 10;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        
        #sourcePipe {
            grid-column: 2 / 3;
            grid-row: 1 / 2;
            width: 60px;
            height: 100px;
            border-bottom: none;
        }

        #gatePipe {
            grid-column: 1 / 2;
            grid-row: 2 / 3;
            align-self: center; /* Vertically center gate pipe in its row */
            width: 70px;
            height: 40px;
            justify-self: end;
            margin-right: -2px; 
            border-right: none;
            background-color: #e0e0e0; 
        }
        #gatePipe .pipe-label { background-color: rgba(0,0,0,0.08); }

        #valveBody {
            grid-column: 2 / 3;
            grid-row: 2 / 3;
            width: 60px;
            height: 40px;
            background-color: #cccccc; 
            border-top: none;
            border-bottom: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        #valveGateVisual {
            background-color: rgba(30, 144, 255, 0.4); /* Semi-transparent dodgerblue */
            width: 65%; 
            height: 0%; 
            border: 1px dashed #0056b3;
            transition: height 0.1s ease-out;
            box-sizing: border-box;
        }
        
        #drainPipe {
            grid-column: 2 / 3;
            grid-row: 3 / 4;
            width: 60px;
            height: 100px;
            border-top: none;
        }

        .water-particle {
            position: absolute;
            width: 5px;
            height: 5px;
            background-color: #007aff; /* iOS blue */
            border-radius: 50%;
            opacity: 0.8;
            will-change: transform; /* Optimize for animation */
        }
        #gatePipe .water-particle {
            background-color: #34c759; /* iOS green */
        }
        
        .status-display {
            margin-top: 25px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f8f8f8;
            line-height: 1.7;
        }
        .status-display span {
            font-weight: 600;
            color: #007aff;
        }

        @media (max-width: 420px) { /* Adjusted breakpoint */
            body { padding: 10px; }
            .app-container { padding: 15px; }
            h1 { font-size: 1.6em; margin-bottom: 20px; }
            .analogy-display { 
                width: 100%; 
                max-width: none; /* Allow full width */
                padding: 5px;
                transform: scale(0.85);
                transform-origin: top center;
                margin-bottom: -20px; /* Compensate for scaling margin */
            }
            .pipe-label { font-size: 0.7em; }
            #sourcePipe, #drainPipe { height: 90px; width: 50px; }
            #gatePipe { width: 60px; height: 35px; }
            #valveBody { height: 35px; width: 50px; }
            .controls label, .controls .radio-group-title { font-size: 0.9em; }
            .controls .radio-group label { font-size: 0.85em; }
            .status-display { font-size: 0.9em; line-height: 1.6; }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <h1>Transistor Water Tap Analogy</h1>

        <div class="controls">
            <div>
                <label for="gateVoltageSlider">Gate Input (Tap Handle): <span id="gateVoltageValueText">0</span></label>
                <input type="range" id="gateVoltageSlider" min="0" max="100" value="0">
            </div>
            <div class="radio-group">
                <div class="radio-group-title">Transistor Type:</div>
                <input type="radio" name="transistorType" id="npn" value="NPN" checked> <label for="npn">NPN (More Input = More Flow)</label><br>
                <input type="radio" name="transistorType" id="pnp" value="PNP"> <label for="pnp">PNP (More Input = Less Flow)</label>
            </div>
        </div>

        <div class="analogy-display">
            <div id="sourcePipe" class="pipe">
                <div class="pipe-label">Source</div>
            </div>
            <div id="gatePipe" class="pipe"> <!-- gatePipe also needs pipe class for particle containment -->
                <div class="pipe-label">Gate</div>
            </div>
            <div id="valveBody" class="pipe"> <!-- valveBody also needs pipe class -->
                <div id="valveGateVisual"></div>
            </div>
            <div id="drainPipe" class="pipe">
                <div class="pipe-label">Drain</div>
            </div>
        </div>

        <div class="status-display">
            Current Gate Input: <span id="gateVoltageDisplay">0</span><br>
            Transistor Type: <span id="transistorTypeDisplay">NPN</span><br>
            Operational State: <span id="transistorStateDisplay">Cutoff</span>
        </div>
    </div>

<script>
    const gateVoltageSlider = document.getElementById('gateVoltageSlider');
    const gateVoltageValueText = document.getElementById('gateVoltageValueText');
    const npnRadio = document.getElementById('npn');
    // const pnpRadio = document.getElementById('pnp'); // Not directly needed, npnRadio.checked tells type

    const sourcePipeEl = document.getElementById('sourcePipe');
    const gatePipeEl = document.getElementById('gatePipe');
    const valveBodyEl = document.getElementById('valveBody');
    const valveGateVisualEl = document.getElementById('valveGateVisual');
    const drainPipeEl = document.getElementById('drainPipe');

    const gateVoltageDisplay = document.getElementById('gateVoltageDisplay');
    const transistorTypeDisplay = document.getElementById('transistorTypeDisplay');
    const transistorStateDisplay = document.getElementById('transistorStateDisplay');

    const MAX_PARTICLES_PER_SECTION = 12; 
    const MAX_GATE_PARTICLES = 7;
    const PARTICLE_MIN_SPEED = 0.8; 
    const PARTICLE_MAX_SPEED = 3.5;
    const PARTICLE_WIDTH = 5;
    const PARTICLE_HEIGHT = 5;

    const CUTOFF_THRESHOLD = 0.05;
    const SATURATION_THRESHOLD = 0.95;

    let mainParticles = []; 
    let gateParticles = []; 

    function createParticle(container, type) {
        const el = document.createElement('div');
        el.classList.add('water-particle');
        // Type-specific styling is handled by CSS (#gatePipe .water-particle)
        container.appendChild(el);
        el.style.display = 'none'; // Start hidden
        return { el, x: 0, y: 0, vx:0, vy:0, active: false, section: null };
    }

    function initParticles() {
        // Main flow particles (source, valve, drain)
        ['source', 'valve', 'drain'].forEach(sectionName => {
            let container;
            if (sectionName === 'source') container = sourcePipeEl;
            else if (sectionName === 'valve') container = valveBodyEl;
            else container = drainPipeEl;
            
            for (let i = 0; i < MAX_PARTICLES_PER_SECTION; i++) {
                const p = createParticle(container, 'main');
                p.section = sectionName;
                mainParticles.push(p);
            }
        });

        // Gate flow particles
        for (let i = 0; i < MAX_GATE_PARTICLES; i++) {
            const p = createParticle(gatePipeEl, 'gate');
            p.section = 'gate'; // Though not strictly needed for gate particles array
            gateParticles.push(p);
        }
    }
    
    function updateSimulation() {
        const gateVoltage = parseInt(gateVoltageSlider.value);
        const isNPN = npnRadio.checked;

        gateVoltageValueText.textContent = gateVoltage;
        gateVoltageDisplay.textContent = gateVoltage;
        transistorTypeDisplay.textContent = isNPN ? "NPN" : "PNP";

        const gateSignalStrength = gateVoltage / 100;

        let mainFlowControl;
        if (isNPN) {
            mainFlowControl = gateVoltage / 100;
        } else { 
            mainFlowControl = (100 - gateVoltage) / 100;
        }

        let state;
        let effectiveMainFlow = mainFlowControl; // Use this for animation after clamping
        if (mainFlowControl <= CUTOFF_THRESHOLD) {
            state = "Cutoff";
            effectiveMainFlow = 0; 
        } else if (mainFlowControl >= SATURATION_THRESHOLD) {
            state = "Saturation";
            effectiveMainFlow = 1; 
        } else {
            state = "Active";
        }
        transistorStateDisplay.textContent = state;

        valveGateVisualEl.style.height = `${effectiveMainFlow * 100}%`;

        const numActiveMainParticlesPerSection = Math.round(effectiveMainFlow * MAX_PARTICLES_PER_SECTION);
        const mainParticleSpeed = PARTICLE_MIN_SPEED + (PARTICLE_MAX_SPEED - PARTICLE_MIN_SPEED) * effectiveMainFlow;

        const numActiveGateParticles = Math.round(gateSignalStrength * MAX_GATE_PARTICLES);
        const gateParticleSpeed = PARTICLE_MIN_SPEED + (PARTICLE_MAX_SPEED - PARTICLE_MIN_SPEED) * gateSignalStrength;
        
        let sourceParticlesActivated = 0;
        let valveParticlesActivated = 0;
        let drainParticlesActivated = 0;

        mainParticles.forEach(p => {
            let shouldBeActive = false;
            if (p.section === 'source' && sourceParticlesActivated < numActiveMainParticlesPerSection) {
                shouldBeActive = true;
                sourceParticlesActivated++;
            } else if (p.section === 'valve' && valveParticlesActivated < numActiveMainParticlesPerSection) {
                shouldBeActive = true;
                valveParticlesActivated++;
            } else if (p.section === 'drain' && drainParticlesActivated < numActiveMainParticlesPerSection) {
                shouldBeActive = true;
                drainParticlesActivated++;
            }

            if (shouldBeActive) {
                if (!p.active) { 
                    p.y = -Math.random() * p.el.parentElement.clientHeight * 0.3 - PARTICLE_HEIGHT; 
                    p.x = Math.random() * (p.el.parentElement.clientWidth - PARTICLE_WIDTH);
                    if (p.x < 0) p.x = 0; // Ensure within bounds
                    if (p.x > p.el.parentElement.clientWidth - PARTICLE_WIDTH) p.x = p.el.parentElement.clientWidth - PARTICLE_WIDTH;
                }
                p.active = true;
                p.vy = mainParticleSpeed;
                p.el.style.display = 'block';
            } else {
                p.active = false;
                p.el.style.display = 'none';
            }
        });

        let gateParticlesActivated = 0;
        gateParticles.forEach(p => {
            if (gateParticlesActivated < numActiveGateParticles) {
                if(!p.active) {
                    p.x = -Math.random() * p.el.parentElement.clientWidth * 0.3 - PARTICLE_WIDTH; 
                    p.y = Math.random() * (p.el.parentElement.clientHeight - PARTICLE_HEIGHT);
                     if (p.y < 0) p.y = 0; // Ensure within bounds
                    if (p.y > p.el.parentElement.clientHeight - PARTICLE_HEIGHT) p.y = p.el.parentElement.clientHeight - PARTICLE_HEIGHT;
                }
                p.active = true;
                p.vx = gateParticleSpeed;
                p.el.style.display = 'block';
                gateParticlesActivated++;
            } else {
                p.active = false;
                p.el.style.display = 'none';
            }
        });
    }

    function animationLoop() {
        mainParticles.forEach(p => {
            if (p.active) {
                p.y += p.vy;
                const parentHeight = p.el.parentElement.clientHeight;
                if (p.y > parentHeight) {
                    p.y = -PARTICLE_HEIGHT - Math.random() * 20; 
                    p.x = Math.random() * (p.el.parentElement.clientWidth - PARTICLE_WIDTH);
                    if (p.x < 0) p.x = 0;
                    if (p.x > p.el.parentElement.clientWidth - PARTICLE_WIDTH) p.x = p.el.parentElement.clientWidth - PARTICLE_WIDTH;
                }
                p.el.style.transform = `translate3d(${p.x}px, ${p.y}px, 0)`;
            }
        });

        gateParticles.forEach(p => {
            if (p.active) {
                p.x += p.vx;
                const parentWidth = p.el.parentElement.clientWidth;
                if (p.x > parentWidth) {
                    p.x = -PARTICLE_WIDTH - Math.random() * 20; 
                    p.y = Math.random() * (p.el.parentElement.clientHeight - PARTICLE_HEIGHT);
                    if (p.y < 0) p.y = 0;
                    if (p.y > p.el.parentElement.clientHeight - PARTICLE_HEIGHT) p.y = p.el.parentElement.clientHeight - PARTICLE_HEIGHT;
                }
                p.el.style.transform = `translate3d(${p.x}px, ${p.y}px, 0)`;
            }
        });

        requestAnimationFrame(animationLoop);
    }

    gateVoltageSlider.addEventListener('input', updateSimulation);
    document.querySelectorAll('input[name="transistorType"]').forEach(radio => {
        radio.addEventListener('change', updateSimulation);
    });

    initParticles();
    updateSimulation(); 
    requestAnimationFrame(animationLoop);

</script>
</body>
</html>
