@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo Electronic Circuits Lab - Windows EXE Builder
echo ===================================================
echo.

:: Check if Node.js is installed
echo Checking if Node.js is installed...
node --version > nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Node.js is not installed or not in the PATH.
    echo Please run download-nodejs.bat to download and install Node.js.
    echo After installing Node.js, restart your computer and run this script again.
    pause
    exit /b 1
)
echo Node.js is installed. Continuing...
echo.

:: Create directories if they don't exist
echo Creating necessary directories...
if not exist electron-app\app mkdir electron-app\app
if not exist electron-app\app\css mkdir electron-app\app\css
if not exist electron-app\app\js mkdir electron-app\app\js
if not exist electron-app\app\images mkdir electron-app\app\images
if not exist electron-app\app\images\components mkdir electron-app\app\images\components
if not exist electron-app\app\images\lab-notes mkdir electron-app\app\images\lab-notes
if not exist electron-app\build mkdir electron-app\build
echo Directories created.
echo.

:: Copy web application files to the Electron app
echo Copying web application files to the Electron app...
copy *.html electron-app\app\ > nul 2>&1
copy css\*.css electron-app\app\css\ > nul 2>&1
copy js\*.js electron-app\app\js\ > nul 2>&1
copy images\components\*.* electron-app\app\images\components\ > nul 2>&1
copy images\lab-notes\*.* electron-app\app\images\lab-notes\ > nul 2>&1
echo Files copied successfully.
echo.

:: Create renderer.js for Electron integration
echo Creating renderer.js for Electron integration...
(
echo // Renderer process integration with Electron
echo document.addEventListener('DOMContentLoaded', ^(^) ^=^> {
echo   // Listen for IPC messages from the main process
echo   if (window.api) {
echo     window.api.receive('new-circuit', ^(^) ^=^> {
echo       if (typeof resetWorkbench === 'function'^) {
echo         resetWorkbench^(^);
echo       }
echo     }^);
echo.
echo     window.api.receive('save-circuit', ^(^) ^=^> {
echo       if (typeof saveCircuit === 'function'^) {
echo         const circuitData = saveCircuit^(^);
echo         window.api.send('save-file-dialog', JSON.stringify^(circuitData^)^);
echo       }
echo     }^);
echo.
echo     window.api.receive('open-circuit', ^(filePath^) ^=^> {
echo       if (typeof loadCircuit === 'function'^) {
echo         // Read the file and load the circuit
echo         fetch^(filePath^)
echo           .then^(response ^=^> response.json^(^)^)
echo           .then^(data ^=^> loadCircuit^(data^)^)
echo           .catch^(error ^=^> console.error^('Error loading circuit:', error^)^);
echo       }
echo     }^);
echo.
echo     window.api.receive('export-image', ^(^) ^=^> {
echo       if (typeof exportCircuitImage === 'function'^) {
echo         const imageData = exportCircuitImage^(^);
echo         window.api.send('export-image-dialog', imageData^);
echo       }
echo     }^);
echo.
echo     // Listen for responses from the main process
echo     window.api.receive('save-file-response', ^(response^) ^=^> {
echo       if (response.success^) {
echo         alert^('Circuit saved successfully!'^);
echo       } else {
echo         alert^('Error saving circuit: ' + response.message^);
echo       }
echo     }^);
echo.
echo     window.api.receive('export-image-response', ^(response^) ^=^> {
echo       if (response.success^) {
echo         alert^('Image exported successfully!'^);
echo       } else {
echo         alert^('Error exporting image: ' + response.message^);
echo       }
echo     }^);
echo   }
echo }^);
) > electron-app\app\js\renderer.js
echo renderer.js created successfully.
echo.

:: Add renderer.js script tag to HTML files
echo Adding renderer.js script tag to HTML files...
for %%f in (electron-app\app\*.html) do (
    echo ^<script src="js/renderer.js"^>^</script^> >> "%%f"
)
echo Script tags added.
echo.

:: Convert SVG icon to ICO
echo Creating icon.ico from SVG...
echo This is a placeholder step. In a real scenario, you would need to convert the SVG to ICO.
echo For now, we'll create a simple placeholder ICO file.
copy electron-app\build\icon.svg electron-app\build\icon.ico > nul 2>&1
echo Icon created (placeholder).
echo.

:: Navigate to the electron-app directory
echo Navigating to the electron-app directory...
cd electron-app
echo.

:: Install dependencies
echo Installing dependencies...
echo This may take a few minutes. Please be patient.
call npm install
if %ERRORLEVEL% neq 0 (
    echo Error installing dependencies!
    cd ..
    pause
    exit /b 1
)
echo Dependencies installed successfully.
echo.

:: Build the executable
echo Building the executable...
echo This may take several minutes. Please be patient.
call npm run dist
if %ERRORLEVEL% neq 0 (
    echo Error building executable!
    cd ..
    pause
    exit /b 1
)
echo.

:: Return to the original directory
cd ..

echo ===================================================
echo Build completed successfully!
echo ===================================================
echo.
echo The executable can be found in the electron-app\dist directory.
echo.
echo Note: This is a development build. For production, you may want to:
echo 1. Create a proper icon.ico file
echo 2. Sign the executable with a code signing certificate
echo 3. Customize the installer further
echo.
pause
