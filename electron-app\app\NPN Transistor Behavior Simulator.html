<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Transistor Simulator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 700px;
        }

        h1 {
            text-align: center;
            color: #333;
        }

        .intro-text {
            margin-bottom: 20px;
            font-size: 0.9em;
            color: #555;
            border-left: 3px solid #007bff;
            padding-left: 10px;
        }

        .main-layout {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .transistor-area {
            flex: 1;
            min-width: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .transistor-symbol svg {
            width: 150px;
            height: auto;
        }
        
        .controls-readouts-area {
            flex: 2;
            min-width: 300px;
        }

        .slider-group {
            margin-bottom: 20px;
        }

        .slider-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .slider-group input[type="range"] {
            width: 100%;
            cursor: pointer;
        }

        .slider-group .value-display {
            display: inline-block;
            min-width: 60px; /* To prevent layout shift */
            text-align: right;
            font-weight: bold;
            color: #007bff;
        }

        .readouts-area {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }

        .readouts-area p {
            margin: 8px 0;
            font-size: 1.1em;
        }

        .readouts-area strong {
            color: #333;
        }
        
        #modeDisplay {
            font-weight: bold;
            padding: 3px 6px;
            border-radius: 4px;
            cursor: help;
        }

        .mode-Cutoff { background-color: #ffe0e0; color: #a00000; }
        .mode-Active { background-color: #e0ffe0; color: #00a000; }
        .mode-Saturation { background-color: #e0e0ff; color: #0000a0; }

        /* Responsive adjustments */
        @media (max-width: 600px) {
            .main-layout {
                flex-direction: column;
            }
            .transistor-area, .controls-readouts-area {
                width: 100%;
            }
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>NPN Transistor Behavior Simulator</h1>

        <p class="intro-text">
            In electronics, a transistor acts as a variable resistor. The amount of current that flows through the transistor is controlled by the current at the base. In this way, a small current can control a larger current. Also, by changing the base current, one changes the resistance of the transistor itself. These effects make the transistor a crucial electronic component. This app helps you explore its behavior.
        </p>

        <div class="main-layout">
            <div class="transistor-area">
                <h3>NPN Transistor</h3>
                <div class="transistor-symbol">
                    <svg viewBox="0 0 100 120" preserveAspectRatio="xMidYMid meet">
                        <title>NPN Transistor Symbol</title>
                        <!-- Circle -->
                        <circle cx="50" cy="60" r="35" stroke="black" stroke-width="2" fill="none"/>
                        <!-- Base line -->
                        <line x1="15" y1="60" x2="38" y2="60" stroke="black" stroke-width="2"/>
                        <!-- Collector line -->
                        <line x1="50" y1="35" x2="50" y2="5" stroke="black" stroke-width="2"/>
                        <line x1="38" y1="45" x2="50" y2="35" stroke="black" stroke-width="2"/>
                        <!-- Emitter line -->
                        <line x1="50" y1="85" x2="50" y2="115" stroke="black" stroke-width="2"/>
                        <line x1="38" y1="75" x2="50" y2="85" stroke="black" stroke-width="2"/>
                        <!-- Emitter arrowhead -->
                        <polygon points="50,85 58,75 42,75" fill="black" transform="rotate(45 50 85) translate(-2, 2)"/>
                        <!-- Labels -->
                        <text x="50" y="-2" font-size="10" text-anchor="middle">C (Collector)</text>
                        <text x="5" y="55" font-size="10" text-anchor="start">B (Base)</text>
                        <text x="50" y="128" font-size="10" text-anchor="middle">E (Emitter)</text>
                    </svg>
                </div>
            </div>

            <div class="controls-readouts-area">
                <div class="controls-area">
                    <div class="slider-group">
                        <label for="ibSlider">Base Current (I<sub>b</sub>): <span id="ibSliderValue" class="value-display">0 µA</span></label>
                        <input type="range" id="ibSlider" min="0" max="1000" step="1" value="0">
                    </div>

                    <div class="slider-group">
                        <label for="vceSlider">Collector-Emitter Voltage (V<sub>ce</sub>): <span id="vceSliderValue" class="value-display">0.0 V</span></label>
                        <input type="range" id="vceSlider" min="0" max="10" step="0.1" value="5">
                    </div>
                </div>

                <div class="readouts-area">
                    <p><strong>Displayed Base Current (I<sub>b</sub>):</strong> <span id="actualIbDisplay">0 µA</span></p>
                    <p><strong>Calculated Collector Current (I<sub>c</sub>):</strong> <span id="icDisplay">0.00 mA</span></p>
                    <p><strong>Operating Mode:</strong> 
                        <span id="modeDisplay" title="Hover for details">Cutoff</span>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // DOM Elements
        const ibSlider = document.getElementById('ibSlider');
        const vceSlider = document.getElementById('vceSlider');
        const ibSliderValue = document.getElementById('ibSliderValue');
        const vceSliderValue = document.getElementById('vceSliderValue');
        const actualIbDisplay = document.getElementById('actualIbDisplay');
        const icDisplay = document.getElementById('icDisplay');
        const modeDisplay = document.getElementById('modeDisplay');

        // Transistor Parameters (Constants)
        const BETA (hFE) = 100;
        const VCE_SAT = 0.2; // Volts, saturation voltage
        const IB_CUTOFF_uA = 1; // Microamps, base current below which transistor is considered off
        const IC_MAX_mA = 100; // Milliamps, maximum collector current (device/circuit limit)

        // Mode descriptions
        const modeTooltips = {
            Cutoff: "Transistor is OFF. No significant current flows from collector to emitter (Ic ≈ 0) because base current (Ib) is too low.",
            Active: "Transistor acts as an amplifier. Collector current (Ic) is proportional to base current (Ib), i.e., Ic = β * Ib. Vce is greater than Vce_sat.",
            Saturation: "Transistor is fully ON, like a closed switch. Collector current (Ic) is at its maximum possible value for the current Ib or circuit limits, and Vce is very small (around Vce_sat). Further increases in Ib may not significantly increase Ic if already at IC_MAX_mA."
        };

        function updateTransistorState() {
            // 1. Read slider values
            const ib_uA = parseFloat(ibSlider.value);
            const vce_V = parseFloat(vceSlider.value);

            // Update slider value displays
            ibSliderValue.textContent = `${ib_uA} µA`;
            vceSliderValue.textContent = `${vce_V.toFixed(1)} V`;

            // Update actual Ib display
            actualIbDisplay.textContent = `${ib_uA} µA`;

            // 2. Calculations
            const ib_A = ib_uA / 1e6; // Convert base current to Amps

            let calculated_Ic_mA;
            let currentMode;

            // 3. Determine Operating Mode and Collector Current
            if (ib_uA < IB_CUTOFF_uA) {
                currentMode = "Cutoff";
                calculated_Ic_mA = 0;
            } else {
                // Potential Ic if in active mode (Ic = BETA * Ib)
                const ic_active_potential_mA = BETA * ib_A * 1000;

                if (vce_V <= VCE_SAT) {
                    currentMode = "Saturation";
                    // In saturation, Ic is limited by external circuit or BETA*Ib if that's lower than max.
                    calculated_Ic_mA = Math.min(ic_active_potential_mA, IC_MAX_mA);
                } else { // vce_V > VCE_SAT (Potential for Active or Saturation if current limit is hit)
                    if (ic_active_potential_mA >= IC_MAX_mA) {
                        // Even if Vce is high, if BETA*Ib demands current >= IC_MAX_mA,
                        // it effectively saturates current-wise.
                        currentMode = "Saturation"; 
                        calculated_Ic_mA = IC_MAX_mA;
                    } else {
                        currentMode = "Active";
                        calculated_Ic_mA = ic_active_potential_mA;
                    }
                }
            }
            
            // Ensure Ic is not negative and format
            calculated_Ic_mA = Math.max(0, calculated_Ic_mA);

            // 4. Update Displays
            icDisplay.textContent = `${calculated_Ic_mA.toFixed(2)} mA`;
            
            modeDisplay.textContent = currentMode;
            modeDisplay.title = modeTooltips[currentMode];
            
            // Update mode display class for styling
            modeDisplay.className = ''; // Clear existing classes
            modeDisplay.classList.add(`mode-${currentMode}`);
        }

        // Event Listeners
        ibSlider.addEventListener('input', updateTransistorState);
        vceSlider.addEventListener('input', updateTransistorState);

        // Initial state update on page load
        document.addEventListener('DOMContentLoaded', updateTransistorState);
    </script>
</body>
</html>
