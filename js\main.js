/**
 * Main JavaScript file for Electronic Circuits Lab
 * Contains common functionality used across the application
 */

// Global variables
let currentExperiment = null;
let searchTimeout = null;
const experimentData = []; // Will be populated with experiment metadata

/**
 * Load an experiment into the iframe
 * @param {string} experimentPath - Path to the experiment HTML file
 * @param {boolean} updateSelect - Whether to update the select dropdown
 */
function loadExperiment(experimentPath, updateSelect = false) {
    const iframeElement = document.getElementById('experiment-frame');
    const placeholderElement = document.getElementById('simulation-placeholder');
    const selectElement = document.getElementById('experiment-select');

    if (!experimentPath && selectElement) {
        experimentPath = selectElement.value;
    }

    if (updateSelect && selectElement) {
        selectElement.value = experimentPath;
    }

    if (experimentPath) {
        // Store the current experiment
        currentExperiment = experimentPath;

        // Update iframe
        if (iframeElement) {
            iframeElement.src = experimentPath;
            iframeElement.style.display = 'block';

            // Add loading indicator
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'loading-indicator';
            loadingIndicator.innerHTML = '<div class="spinner"></div><p>جاري تحميل التجربة...</p>';

            if (placeholderElement) {
                placeholderElement.innerHTML = '';
                placeholderElement.appendChild(loadingIndicator);
                placeholderElement.style.display = 'flex';
            }

            // Hide loading indicator when iframe loads
            iframeElement.onload = function() {
                if (placeholderElement) {
                    placeholderElement.style.display = 'none';
                }
            };
        }

        // Update URL with experiment parameter
        const url = new URL(window.location.href);
        url.searchParams.set('experiment', experimentPath);
        window.history.replaceState({}, '', url);

        // Update breadcrumb if it exists
        updateBreadcrumb(experimentPath);
    } else {
        if (iframeElement) {
            iframeElement.src = 'about:blank';
            iframeElement.style.display = 'none';
        }

        if (placeholderElement) {
            placeholderElement.innerHTML = '<p>اختر تجربة من القائمة لبدء المحاكاة</p>';
            placeholderElement.style.display = 'flex';
        }

        // Remove experiment parameter from URL
        const url = new URL(window.location.href);
        url.searchParams.delete('experiment');
        window.history.replaceState({}, '', url);
    }
}

/**
 * Update the breadcrumb navigation based on current experiment
 * @param {string} experimentPath - Path to the current experiment
 */
function updateBreadcrumb(experimentPath) {
    const breadcrumbElement = document.getElementById('experiment-breadcrumb');
    if (!breadcrumbElement) return;

    // Clear existing breadcrumb
    breadcrumbElement.innerHTML = '';

    // Add home link
    const homeLink = document.createElement('a');
    homeLink.href = 'index.html';
    homeLink.textContent = 'الرئيسية';
    breadcrumbElement.appendChild(homeLink);

    // Add separator
    const separator = document.createElement('span');
    separator.className = 'breadcrumb-separator';
    separator.textContent = ' / ';
    breadcrumbElement.appendChild(separator);

    if (experimentPath) {
        // Find experiment title from path
        let experimentTitle = experimentPath.split('/').pop().replace('.html', '');

        // Make it more readable
        experimentTitle = experimentTitle.replace(/-/g, ' ');

        // Add experiment link
        const experimentLink = document.createElement('span');
        experimentLink.textContent = experimentTitle;
        breadcrumbElement.appendChild(experimentLink);
    } else {
        // Add experiments link if no specific experiment
        const experimentsLink = document.createElement('span');
        experimentsLink.textContent = 'التجارب';
        breadcrumbElement.appendChild(experimentsLink);
    }
}

/**
 * Filter experiments based on search query or category
 * @param {string} query - Search query
 * @param {string} category - Category to filter by
 */
function filterExperiments(query = '', category = 'all') {
    const experimentCards = document.querySelectorAll('.experiment-card');
    const noResultsElement = document.getElementById('no-results');
    let resultsFound = false;

    experimentCards.forEach(card => {
        const title = card.querySelector('h3').textContent.toLowerCase();
        const description = card.querySelector('p').textContent.toLowerCase();
        const cardCategory = card.dataset.category || 'all';

        const matchesQuery = query === '' ||
            title.includes(query.toLowerCase()) ||
            description.includes(query.toLowerCase());

        const matchesCategory = category === 'all' || cardCategory === category;

        if (matchesQuery && matchesCategory) {
            card.style.display = 'flex';
            resultsFound = true;
        } else {
            card.style.display = 'none';
        }
    });

    // Show/hide no results message
    if (noResultsElement) {
        noResultsElement.style.display = resultsFound ? 'none' : 'block';
    }
}

/**
 * Add a component to the interactive drawing area
 * @param {string} componentName - Name of the component to add
 */
function addComponent(componentName) {
    console.log(`Adding component: ${componentName}`);
    const interactiveArea = document.getElementById('interactive-circuit-drawing-area');

    if (interactiveArea) {
        const componentElement = document.createElement('div');
        componentElement.textContent = componentName;
        componentElement.classList.add('circuit-component');
        componentElement.dataset.component = componentName;
        componentElement.draggable = true;

        // Add drag functionality
        componentElement.addEventListener('dragstart', function(e) {
            e.dataTransfer.setData('text/plain', componentName);
            e.dataTransfer.effectAllowed = 'move';
        });

        interactiveArea.appendChild(componentElement);

        // Notify the workbench if it exists
        if (window.notifyWorkbench) {
            window.notifyWorkbench('componentAdded', {
                type: componentName,
                element: componentElement
            });
        }
    } else {
        console.error('Interactive drawing area not found.');
    }
}

/**
 * Initialize the application when the DOM is loaded
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize experiment selector if it exists
    const selectElement = document.getElementById('experiment-select');
    if (selectElement) {
        selectElement.addEventListener('change', function() {
            loadExperiment();
        });

        // Check if there's an experiment parameter in the URL
        const urlParams = new URLSearchParams(window.location.search);
        const experimentParam = urlParams.get('experiment');

        if (experimentParam) {
            // Set the select value to the experiment from URL
            selectElement.value = experimentParam;
        }

        // Load the selected experiment
        loadExperiment();
    }

    // Initialize search functionality if it exists
    const searchInput = document.getElementById('experiment-search');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            // Debounce search to avoid too many updates
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                filterExperiments(searchInput.value);
                updateResultsCount();
            }, 300);
        });
    }

    // Initialize search button if it exists
    const searchButton = document.getElementById('search-button');
    if (searchButton && searchInput) {
        searchButton.addEventListener('click', function() {
            filterExperiments(searchInput.value);
            updateResultsCount();
        });
    }

    // Initialize advanced search toggle if it exists
    const advancedSearchToggle = document.getElementById('advanced-search-toggle');
    const advancedSearchOptions = document.getElementById('advanced-search-options');
    if (advancedSearchToggle && advancedSearchOptions) {
        advancedSearchToggle.addEventListener('click', function() {
            advancedSearchOptions.classList.toggle('show');
            advancedSearchToggle.classList.toggle('active');

            // Change icon based on state
            const icon = advancedSearchToggle.querySelector('i');
            if (icon) {
                if (advancedSearchOptions.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-sliders-h';
                }
            }
        });
    }

    // Initialize advanced search filters
    const advancedFilters = document.querySelectorAll('.filter-option input[type="checkbox"]');
    if (advancedFilters.length > 0) {
        advancedFilters.forEach(filter => {
            filter.addEventListener('change', function() {
                applyAdvancedFilters();
            });
        });
    }

    // Initialize sort dropdown if it exists
    const sortDropdown = document.getElementById('sort-experiments');
    if (sortDropdown) {
        sortDropdown.addEventListener('change', function() {
            sortExperiments(sortDropdown.value);
        });
    }

    // Initialize category filters if they exist
    const categoryFilters = document.querySelectorAll('.category-filter');
    if (categoryFilters.length > 0) {
        categoryFilters.forEach(filter => {
            filter.addEventListener('click', function() {
                // Remove active class from all filters
                categoryFilters.forEach(f => f.classList.remove('active'));

                // Add active class to clicked filter
                filter.classList.add('active');

                // Filter experiments by category
                const category = filter.dataset.category;
                filterExperiments(searchInput ? searchInput.value : '', category);
                updateResultsCount();
            });
        });
    }

    // Initialize tabs if they exist
    const tabs = document.querySelectorAll('.tab');
    if (tabs.length > 0) {
        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs
                tabs.forEach(t => t.classList.remove('active'));

                // Add active class to clicked tab
                tab.classList.add('active');

                // Hide all tab content
                const tabContents = document.querySelectorAll('.tab-content');
                tabContents.forEach(content => content.classList.remove('active'));

                // Show the corresponding tab content
                const tabId = tab.dataset.tab;
                const tabContent = document.getElementById(tabId);
                if (tabContent) {
                    tabContent.classList.add('active');
                }
            });
        });
    }

    // Initialize FAQ accordion if it exists
    const faqQuestions = document.querySelectorAll('.faq-question');
    if (faqQuestions.length > 0) {
        faqQuestions.forEach(question => {
            question.addEventListener('click', function() {
                const faqItem = this.parentElement;
                faqItem.classList.toggle('active');

                // Toggle icon
                const icon = this.querySelector('i');
                if (icon) {
                    if (faqItem.classList.contains('active')) {
                        icon.className = 'fas fa-chevron-up';
                    } else {
                        icon.className = 'fas fa-chevron-down';
                    }
                }
            });
        });
    }

    // Initialize newsletter form if it exists
    const newsletterForm = document.getElementById('newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const emailInput = document.getElementById('newsletter-email');
            if (emailInput && emailInput.value) {
                // Here you would normally send the data to a server
                // For now, just show a success message
                alert('شكراً لاشتراكك في النشرة الإخبارية!');
                newsletterForm.reset();
            }
        });
    }

    // Initialize theme toggle if it exists
    const themeToggle = document.getElementById('theme-toggle-btn');
    if (themeToggle) {
        // Check if user has a theme preference stored
        const currentTheme = localStorage.getItem('theme');
        if (currentTheme) {
            document.documentElement.setAttribute('data-theme', currentTheme);

            // Update toggle button icon
            const icon = themeToggle.querySelector('i');
            if (icon) {
                icon.className = currentTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        }

        themeToggle.addEventListener('click', function() {
            // Toggle between light and dark themes
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            // Update toggle button icon
            const icon = themeToggle.querySelector('i');
            if (icon) {
                icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        });
    }

    // Initialize mobile menu toggle if it exists
    const mobileMenuToggle = document.getElementById('nav-menu-toggle');
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', function(e) {
            e.preventDefault();
            const navUl = this.closest('ul');
            navUl.classList.toggle('show-mobile');

            // Toggle icon
            const icon = this.querySelector('i');
            if (icon) {
                if (navUl.classList.contains('show-mobile')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
    }

    // Initialize share buttons
    const shareButtons = document.querySelectorAll('.btn-icon[title="مشاركة"]');
    if (shareButtons.length > 0) {
        shareButtons.forEach(button => {
            button.addEventListener('click', function() {
                const experimentCard = this.closest('.experiment-card');
                const experimentTitle = experimentCard.querySelector('h3').textContent;
                const experimentLink = experimentCard.querySelector('h3 a').getAttribute('href');

                // Check if Web Share API is supported
                if (navigator.share) {
                    navigator.share({
                        title: experimentTitle,
                        text: 'تحقق من هذه التجربة في معمل الدوائر الإلكترونية الافتراضي!',
                        url: window.location.origin + '/' + experimentLink
                    })
                    .catch(error => console.log('Error sharing:', error));
                } else {
                    // Fallback for browsers that don't support the Web Share API
                    prompt('انسخ الرابط لمشاركة هذه التجربة:', window.location.origin + '/' + experimentLink);
                }
            });
        });
    }

    // Initialize bookmark buttons
    const bookmarkButtons = document.querySelectorAll('.btn-icon[title="حفظ للمشاهدة لاحقاً"]');
    if (bookmarkButtons.length > 0) {
        // Load saved bookmarks from localStorage
        let savedBookmarks = JSON.parse(localStorage.getItem('savedExperiments')) || [];

        // Update bookmark button states
        updateBookmarkButtonStates(savedBookmarks);

        bookmarkButtons.forEach(button => {
            button.addEventListener('click', function() {
                const experimentCard = this.closest('.experiment-card');
                const experimentTitle = experimentCard.querySelector('h3').textContent;
                const experimentLink = experimentCard.querySelector('h3 a').getAttribute('href');

                // Check if already bookmarked
                const bookmarkIndex = savedBookmarks.findIndex(b => b.link === experimentLink);

                if (bookmarkIndex === -1) {
                    // Add to bookmarks
                    savedBookmarks.push({
                        title: experimentTitle,
                        link: experimentLink,
                        timestamp: new Date().toISOString()
                    });

                    // Update button state
                    button.innerHTML = '<i class="fas fa-bookmark"></i>';
                    button.classList.add('active');
                } else {
                    // Remove from bookmarks
                    savedBookmarks.splice(bookmarkIndex, 1);

                    // Update button state
                    button.innerHTML = '<i class="far fa-bookmark"></i>';
                    button.classList.remove('active');
                }

                // Save to localStorage
                localStorage.setItem('savedExperiments', JSON.stringify(savedBookmarks));
            });
        });
    }
});

/**
 * Update the bookmark button states based on saved bookmarks
 * @param {Array} savedBookmarks - Array of saved bookmark objects
 */
function updateBookmarkButtonStates(savedBookmarks) {
    const bookmarkButtons = document.querySelectorAll('.btn-icon[title="حفظ للمشاهدة لاحقاً"]');

    bookmarkButtons.forEach(button => {
        const experimentCard = button.closest('.experiment-card');
        const experimentLink = experimentCard.querySelector('h3 a').getAttribute('href');

        // Check if this experiment is bookmarked
        const isBookmarked = savedBookmarks.some(b => b.link === experimentLink);

        if (isBookmarked) {
            button.innerHTML = '<i class="fas fa-bookmark"></i>';
            button.classList.add('active');
        } else {
            button.innerHTML = '<i class="far fa-bookmark"></i>';
            button.classList.remove('active');
        }
    });
}

/**
 * Apply advanced filters to experiment cards
 */
function applyAdvancedFilters() {
    const experimentCards = document.querySelectorAll('.experiment-card');
    const levelFilters = Array.from(document.querySelectorAll('input[name="level"]:checked')).map(el => el.value);
    const durationFilters = Array.from(document.querySelectorAll('input[name="duration"]:checked')).map(el => el.value);

    experimentCards.forEach(card => {
        const cardLevel = card.dataset.level || '';
        const cardDuration = card.dataset.duration || '';

        const matchesLevel = levelFilters.length === 0 || levelFilters.includes(cardLevel);
        const matchesDuration = durationFilters.length === 0 || durationFilters.includes(cardDuration);

        // Only update display if the card is already visible (respecting category filters)
        if (card.style.display !== 'none') {
            if (matchesLevel && matchesDuration) {
                card.style.display = 'flex';
            } else {
                card.style.display = 'none';
            }
        }
    });

    updateResultsCount();
}

/**
 * Sort experiment cards based on selected criteria
 * @param {string} sortBy - Sorting criteria (newest, popular, az, difficulty)
 */
function sortExperiments(sortBy) {
    const experimentGrid = document.querySelector('.experiment-grid');
    if (!experimentGrid) return;

    const experimentCards = Array.from(experimentGrid.querySelectorAll('.experiment-card'));

    // Sort the cards based on the selected criteria
    experimentCards.sort((a, b) => {
        switch (sortBy) {
            case 'newest':
                // Sort by new badge first, then by position in the DOM
                const aIsNew = a.querySelector('.new-badge') !== null;
                const bIsNew = b.querySelector('.new-badge') !== null;
                return bIsNew - aIsNew;

            case 'popular':
                // Sort by popular badge first, then by position in the DOM
                const aIsPopular = a.querySelector('.experiment-badge:not(.new-badge)') !== null;
                const bIsPopular = b.querySelector('.experiment-badge:not(.new-badge)') !== null;
                return bIsPopular - aIsPopular;

            case 'az':
                // Sort alphabetically by title
                const aTitle = a.querySelector('h3').textContent.trim();
                const bTitle = b.querySelector('h3').textContent.trim();
                return aTitle.localeCompare(bTitle, 'ar');

            case 'difficulty':
                // Sort by difficulty level (beginner, intermediate, advanced)
                const difficultyOrder = { 'beginner': 1, 'intermediate': 2, 'advanced': 3 };
                const aLevel = a.dataset.level || 'beginner';
                const bLevel = b.dataset.level || 'beginner';
                return difficultyOrder[aLevel] - difficultyOrder[bLevel];

            default:
                return 0;
        }
    });

    // Reappend the cards in the new order
    experimentCards.forEach(card => {
        experimentGrid.appendChild(card);
    });
}

/**
 * Update the results count display
 */
function updateResultsCount() {
    const resultsCountElement = document.getElementById('results-count');
    if (!resultsCountElement) return;

    const visibleCards = document.querySelectorAll('.experiment-card[style*="display: flex"], .experiment-card:not([style*="display"])').length;
    const totalCards = document.querySelectorAll('.experiment-card').length;

    if (visibleCards === totalCards) {
        resultsCountElement.textContent = `عرض جميع التجارب (${totalCards})`;
    } else {
        resultsCountElement.textContent = `عرض ${visibleCards} من ${totalCards} تجربة`;
    }
}