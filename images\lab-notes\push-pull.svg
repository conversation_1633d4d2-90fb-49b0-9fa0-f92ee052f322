<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="600" height="450" viewBox="0 0 600 450">
  <!-- Background -->
  <rect width="600" height="450" fill="#ffffff"/>
  
  <!-- Title -->
  <text x="300" y="30" font-family="Arial" font-size="20" text-anchor="middle" fill="#333333">Push-Pull Output Stage Circuit</text>
  
  <!-- Power Supply (VCC) -->
  <text x="300" y="70" font-family="Arial" font-size="14" text-anchor="middle" fill="#333333">VCC (+12V)</text>
  <line x1="300" y1="80" x2="300" y2="100" stroke="#000000" stroke-width="2"/>
  
  <!-- NPN Transistor (Q1) -->
  <!-- Base -->
  <line x1="250" y1="150" x2="280" y2="150" stroke="#000000" stroke-width="2"/>
  <!-- Collector -->
  <line x1="300" y1="100" x2="300" y2="140" stroke="#000000" stroke-width="2"/>
  <!-- Emitter -->
  <line x1="300" y1="160" x2="300" y2="200" stroke="#000000" stroke-width="2"/>
  <!-- Transistor Symbol -->
  <circle cx="290" cy="150" r="15" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <line x1="280" y1="150" x2="290" y2="140" stroke="#000000" stroke-width="2"/>
  <line x1="290" y1="140" x2="290" y2="160" stroke="#000000" stroke-width="2"/>
  <line x1="290" y1="160" x2="300" y2="170" stroke="#000000" stroke-width="2"/>
  <line x1="290" y1="140" x2="300" y2="130" stroke="#000000" stroke-width="2"/>
  <text x="270" y="150" font-family="Arial" font-size="12" fill="#333333">Q1</text>
  <text x="320" y="150" font-family="Arial" font-size="10" fill="#666666">NPN</text>
  
  <!-- PNP Transistor (Q2) -->
  <!-- Base -->
  <line x1="250" y1="250" x2="280" y2="250" stroke="#000000" stroke-width="2"/>
  <!-- Collector -->
  <line x1="300" y1="260" x2="300" y2="300" stroke="#000000" stroke-width="2"/>
  <!-- Emitter -->
  <line x1="300" y1="200" x2="300" y2="240" stroke="#000000" stroke-width="2"/>
  <!-- Transistor Symbol -->
  <circle cx="290" cy="250" r="15" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <line x1="280" y1="250" x2="290" y2="260" stroke="#000000" stroke-width="2"/>
  <line x1="290" y1="240" x2="290" y2="260" stroke="#000000" stroke-width="2"/>
  <line x1="290" y1="240" x2="300" y2="230" stroke="#000000" stroke-width="2"/>
  <line x1="290" y1="260" x2="300" y2="270" stroke="#000000" stroke-width="2"/>
  <text x="270" y="250" font-family="Arial" font-size="12" fill="#333333">Q2</text>
  <text x="320" y="250" font-family="Arial" font-size="10" fill="#666666">PNP</text>
  
  <!-- Negative Power Supply (-VCC) -->
  <text x="300" y="330" font-family="Arial" font-size="14" text-anchor="middle" fill="#333333">-VCC (-12V)</text>
  <line x1="300" y1="300" x2="300" y2="320" stroke="#000000" stroke-width="2"/>
  
  <!-- Input Stage -->
  <line x1="150" y1="200" x2="170" y2="200" stroke="#000000" stroke-width="2"/>
  <line x1="170" y1="150" x2="170" y2="250" stroke="#000000" stroke-width="2"/>
  <line x1="170" y1="150" x2="200" y2="150" stroke="#000000" stroke-width="2"/>
  <line x1="170" y1="250" x2="200" y2="250" stroke="#000000" stroke-width="2"/>
  
  <!-- Biasing Diodes -->
  <!-- D1 -->
  <line x1="200" y1="150" x2="220" y2="150" stroke="#000000" stroke-width="2"/>
  <polygon points="220,150 230,145 230,155" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <line x1="230" y1="150" x2="250" y2="150" stroke="#000000" stroke-width="2"/>
  <text x="225" y="140" font-family="Arial" font-size="12" fill="#333333">D1</text>
  
  <!-- D2 -->
  <line x1="200" y1="250" x2="220" y2="250" stroke="#000000" stroke-width="2"/>
  <polygon points="220,250 230,245 230,255" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <line x1="230" y1="250" x2="250" y2="250" stroke="#000000" stroke-width="2"/>
  <text x="225" y="270" font-family="Arial" font-size="12" fill="#333333">D2</text>
  
  <!-- Output -->
  <line x1="300" y1="200" x2="350" y2="200" stroke="#000000" stroke-width="2"/>
  <line x1="350" y1="180" x2="350" y2="220" stroke="#000000" stroke-width="2"/>
  <line x1="360" y1="180" x2="360" y2="220" stroke="#000000" stroke-width="2"/>
  <line x1="360" y1="200" x2="400" y2="200" stroke="#000000" stroke-width="2"/>
  <text x="355" y="170" font-family="Arial" font-size="12" fill="#333333">C1</text>
  <text x="355" y="240" font-family="Arial" font-size="10" fill="#666666">100µF</text>
  
  <!-- Load Resistor -->
  <line x1="400" y1="200" x2="420" y2="200" stroke="#000000" stroke-width="2"/>
  <rect x="420" y="180" width="20" height="40" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <text x="450" y="200" font-family="Arial" font-size="12" fill="#333333">RL</text>
  <text x="450" y="215" font-family="Arial" font-size="10" fill="#666666">8Ω</text>
  <line x1="440" y1="200" x2="460" y2="200" stroke="#000000" stroke-width="2"/>
  
  <!-- Ground -->
  <line x1="460" y1="200" x2="460" y2="230" stroke="#000000" stroke-width="2"/>
  <line x1="440" y1="230" x2="480" y2="230" stroke="#000000" stroke-width="2"/>
  <line x1="445" y1="235" x2="475" y2="235" stroke="#000000" stroke-width="2"/>
  <line x1="450" y1="240" x2="470" y2="240" stroke="#000000" stroke-width="2"/>
  <line x1="455" y1="245" x2="465" y2="245" stroke="#000000" stroke-width="2"/>
  
  <!-- Input and Output Labels -->
  <text x="150" y="180" font-family="Arial" font-size="12" fill="#333333">Input</text>
  <text x="400" y="180" font-family="Arial" font-size="12" fill="#333333">Output</text>
  
  <!-- Input Signal -->
  <path d="M 100,200 Q 110,180 120,200 Q 130,220 140,200 Q 150,180 160,200" fill="none" stroke="#007bff" stroke-width="2"/>
  <text x="130" y="240" font-family="Arial" font-size="10" fill="#007bff">Vin (Sine Wave)</text>
  
  <!-- Circuit Operation -->
  <text x="100" y="380" font-family="Arial" font-size="14" fill="#333333">Circuit Operation:</text>
  <text x="100" y="400" font-family="Arial" font-size="12" fill="#333333">• Q1 conducts during positive half-cycle (Push)</text>
  <text x="100" y="420" font-family="Arial" font-size="12" fill="#333333">• Q2 conducts during negative half-cycle (Pull)</text>
  <text x="100" y="440" font-family="Arial" font-size="12" fill="#333333">• Diodes D1 and D2 provide biasing to prevent crossover distortion</text>
  
  <!-- Waveforms -->
  <text x="400" y="380" font-family="Arial" font-size="14" fill="#333333">Waveforms:</text>
  <!-- Input Waveform -->
  <path d="M 400,400 Q 410,390 420,400 Q 430,410 440,400 Q 450,390 460,400" fill="none" stroke="#007bff" stroke-width="2"/>
  <text x="430" y="420" font-family="Arial" font-size="10" fill="#007bff">Input</text>
  <!-- Output Waveform -->
  <path d="M 480,400 Q 490,390 500,400 Q 510,410 520,400 Q 530,390 540,400" fill="none" stroke="#28a745" stroke-width="2"/>
  <text x="510" y="420" font-family="Arial" font-size="10" fill="#28a745">Output</text>
</svg>
