# Electronic Circuits Lab - Electron App

This is an Electron wrapper for the Electronic Circuits Lab web application, allowing it to run as a standalone desktop application.

## Prerequisites

- [Node.js](https://nodejs.org/) (v14 or later)
- npm (comes with Node.js)

## Setup Instructions

1. Install dependencies:
   ```
   npm install
   ```

2. Create the icon:
   - Open `convert-icon.html` in a browser
   - Click "Convert and Download" to get a zip file with PNG icons
   - Extract the zip file
   - Use a tool like [ICO Convert](https://icoconvert.com/) to create an ICO file from the PNGs
   - Save the ICO file as `build/icon.ico`

3. Copy web application files:
   - Copy all HTML, CSS, JavaScript, and image files from the web application to the `app` directory
   - Make sure `index.html` is in the root of the `app` directory

4. Test the application:
   ```
   npm start
   ```

5. Build the executable:
   ```
   npm run dist
   ```

   This will create the executable in the `dist` directory.

## Building for Different Platforms

### Windows
```
npm run dist -- --win
```

### macOS
```
npm run dist -- --mac
```

### Linux
```
npm run dist -- --linux
```

## Customizing the Application

You can customize the application by modifying the following files:

- `package.json`: Change the application name, version, description, etc.
- `main.js`: Modify the Electron window settings, menus, etc.
- `build/icon.ico`: Replace with your own application icon

## Integrating with the Web Application

To make the web application work with Electron, you may need to modify some of the JavaScript code to use the Electron API for file operations. The `preload.js` file exposes safe methods for communicating with the main process.

Example usage in your web application:

```javascript
// Save a circuit
window.api.send('save-file-dialog', JSON.stringify(circuitData));

// Listen for the response
window.api.receive('save-file-response', (response) => {
  if (response.success) {
    console.log('File saved successfully at:', response.filePath);
  } else {
    console.error('Error saving file:', response.message);
  }
});
```

## License

MIT
