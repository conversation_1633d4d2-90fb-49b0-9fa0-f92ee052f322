<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NPN Transistor Explorer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            line-height: 1.6;
            margin: 0;
            padding: 15px;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 900px;
        }
        h1 {
            text-align: center;
            color: #1a73e8; /* Google Blue */
            margin-bottom: 25px;
            font-size: 1.8em;
        }
        h2 {
            color: #3c4043; /* Dark Gray */
            border-bottom: 2px solid #e8eaed; /* Light Gray */
            padding-bottom: 8px;
            margin-top: 0;
            font-size: 1.3em;
        }
        .main-layout {
            display: flex;
            flex-wrap: wrap;
            gap: 25px;
        }
        .circuit-diagram-container {
            flex: 1 1 350px; /* Allow shrinking but prefer 350px */
            min-width: 300px; /* Minimum width before wrapping */
            border: 1px solid #dadce0; /* Google Gray */
            border-radius: 8px;
            padding: 15px;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f8f9fa; /* Lighter Gray */
        }
        .circuit-svg {
            width: 100%;
            max-width: 350px; /* Max width of SVG itself */
            height: auto;
        }
        .controls-results-container {
            flex: 1.5 1 400px; /* Grow more, basis 400px */
            display: flex;
            flex-direction: column;
            gap: 25px;
        }
        .controls, .results {
            background: #ffffff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dadce0; /* Google Gray */
        }
        .control-group {
            margin-bottom: 18px;
        }
        .control-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #5f6368; /* Medium Gray */
        }
        .control-group input[type="range"] {
            width: calc(100% - 95px); /* Adjust based on number input width and spacing */
            margin-right: 10px;
            vertical-align: middle;
        }
        .control-group input[type="number"] {
            width: 70px;
            padding: 6px 8px;
            border: 1px solid #dadce0;
            border-radius: 4px;
            vertical-align: middle;
            box-sizing: border-box;
        }
        .control-group .value-display {
            font-weight: bold;
            color: #1a73e8;
        }
        .results p {
            margin: 12px 0;
            font-size: 1em;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .results .label {
            color: #5f6368;
        }
        .output-value {
            font-weight: bold;
            color: #1967d2; /* Darker Blue */
            background-color: #e8f0fe; /* Light Blue Background */
            padding: 3px 8px;
            border-radius: 4px;
            min-width: 60px; /* Ensure consistent width for values */
            text-align: right;
        }
        .mode-indicator-container {
            margin-top:15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .mode-indicator {
            display: inline-block;
            padding: 8px 15px;
            text-align: center;
            font-size: 1.1em;
            font-weight: bold;
            border-radius: 5px;
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
            border: 1px solid transparent;
        }
        .mode-cutoff { background-color: #e8eaed; color: #5f6368; border-color: #d2d5d8;} /* Gray */
        .mode-active { background-color: #e6f4ea; color: #1e8e3e; border-color: #a8d5b5;} /* Green */
        .mode-saturation { background-color: #feefc3; color: #f29900; border-color: #fbd679;} /* Orange/Yellow */

        /* SVG styles */
        .wire { stroke: #3c4043; stroke-width: 1.5; }
        .component { stroke: #3c4043; stroke-width: 1.5; fill: #fff; }
        .component-nofill { stroke: #3c4043; stroke-width: 1.5; fill: none; }
        .label-text { font-family: "Roboto", Arial, sans-serif; font-size: 12px; fill: #3c4043; user-select: none;}
        .arrow-head { fill: #3c4043; }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .main-layout {
                flex-direction: column;
            }
            .controls-results-container {
                flex-basis: auto; /* Reset basis for column layout */
            }
            .control-group input[type="range"] {
                width: calc(100% - 90px);
            }
        }
         @media (max-width: 480px) {
            body { padding: 10px; }
            .container { padding: 15px; }
            h1 { font-size: 1.6em; }
            .control-group label { font-size: 0.9em; }
            .control-group input[type="range"] { width: calc(100% - 85px); } /* Further reduce for smaller screens */
            .control-group input[type="number"] { width: 65px; padding: 4px 6px;}
            .results p {font-size: 0.95em;}
            .mode-indicator { font-size: 1em; padding: 6px 12px;}
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>NPN Transistor Explorer</h1>

        <div class="main-layout">
            <div class="circuit-diagram-container">
                <svg id="circuitSvg" viewBox="0 0 350 300" class="circuit-svg" preserveAspectRatio="xMidYMid meet">
                    <!-- Ground -->
                    <line x1="175" y1="250" x2="175" y2="270" class="wire" />
                    <line x1="160" y1="270" x2="190" y2="270" class="wire" />
                    <line x1="165" y1="275" x2="185" y2="275" class="wire" />
                    <line x1="170" y1="280" x2="180" y2="280" class="wire" />

                    <!-- Transistor NPN -->
                    <circle cx="175" cy="200" r="20" class="component" />
                    <line x1="175" y1="180" x2="175" y2="140" class="wire" /> <!-- Collector wire up -->
                    <line x1="155" y1="200" x2="120" y2="200" class="wire" /> <!-- Base wire left -->
                    <line x1="175" y1="220" x2="175" y2="250" class="wire" /> <!-- Emitter wire down -->

                    <!-- NPN Symbol internals -->
                    <line x1="165" y1="200" x2="185" y2="200" class="component-nofill" stroke-width="2"/> <!-- Base contact line -->
                    <line x1="168" y1="200" x2="180" y2="188" class="component-nofill"/> <!-- Collector internal line -->
                    <line x1="168" y1="200" x2="180" y2="212" class="component-nofill"/> <!-- Emitter internal line -->
                    <polyline points="177,214 180,212 180,209" class="arrow-head" /> <!-- Emitter arrow -->

                    <!-- Labels for Transistor -->
                    <text x="180" y="135" class="label-text">C</text>
                    <text x="100" y="205" class="label-text">B</text>
                    <text x="180" y="245" class="label-text">E</text>

                    <!-- Load Resistor RL -->
                    <rect x="165" y="80" width="20" height="50" class="component"/>
                    <line x1="175" y1="140" x2="175" y2="130" class="wire" /> <!-- C to RL -->
                    <line x1="175" y1="80" x2="175" y2="50" class="wire" /> <!-- RL to Vcc -->
                    <text x="190" y="110" class="label-text">RL</text>

                    <!-- Vcc (Power Supply Symbol) -->
                    <line x1="175" y1="50" x2="175" y2="30" class="wire"/>
                    <line x1="160" y1="30" x2="190" y2="30" class="wire"/> <!-- Long plate -->
                    <line x1="168" y1="40" x2="182" y2="40" class="wire"/> <!-- Short plate -->
                    <text x="195" y="38" class="label-text">Vcc</text>

                    <!-- Vb (Voltage Source Symbol) -->
                    <line x1="120" y1="200" x2="77" y2="200" class="wire"/> <!-- Base wire to Vb symbol -->
                    <circle cx="65" cy="200" r="12" class="component"/>
                    <line x1="60" y1="200" x2="70" y2="200" class="component-nofill" stroke-width="1.5"/> <!-- Plus horizontal -->
                    <line x1="65" y1="195" x2="65" y2="205" class="component-nofill" stroke-width="1.5"/> <!-- Plus vertical -->
                    <text x="35" y="205" class="label-text">Vb</text>


                    <!-- Current Arrows -->
                    <!-- Ic Arrow (Conventional current into Collector) -->
                    <line x1="195" y1="130" x2="195" y2="90" class="wire" />
                    <polyline points="192,95 195,90 198,95" class="arrow-head" />
                    <text x="200" y="115" class="label-text">Ic</text>

                    <!-- Ib Arrow (Conventional current into Base) -->
                    <line x1="110" y1="180" x2="90" y2="180" class="wire" />
                    <polyline points="95,177 90,180 95,183" class="arrow-head" />
                    <text x="95" y="175" class="label-text">Ib</text>
                </svg>
            </div>

            <div class="controls-results-container">
                <div class="controls">
                    <h2>Controls</h2>
                    <div class="control-group">
                        <label for="vbSlider">Base Voltage (Vb): <span id="vbValue" class="value-display">0.00</span> V</label>
                        <input type="range" id="vbSlider" min="0" max="5" value="0" step="0.01" aria-labelledby="vbSliderLabel">
                        <input type="number" id="vbNumber" min="0" max="5" value="0" step="0.01">
                    </div>
                    <div class="control-group">
                        <label for="vccSlider">Collector Supply (Vcc): <span id="vccValue" class="value-display">5.0</span> V</label>
                        <input type="range" id="vccSlider" min="0" max="15" value="5" step="0.1" aria-labelledby="vccSliderLabel">
                        <input type="number" id="vccNumber" min="0" max="15" value="5" step="0.1">
                    </div>
                    <div class="control-group">
                        <label for="rlSlider">Load Resistance (RL): <span id="rlValue" class="value-display">1000</span> &Omega;</label>
                        <input type="range" id="rlSlider" min="100" max="10000" value="1000" step="10" aria-labelledby="rlSliderLabel">
                        <input type="number" id="rlNumber" min="100" max="10000" value="1000" step="10">
                    </div>
                </div>

                <div class="results">
                    <h2>Outputs</h2>
                    <p><span class="label">Base Current (Ib):</span> <span id="ibResult" class="output-value">0.00</span> mA</p>
                    <p><span class="label">Collector Current (Ic):</span> <span id="icResult" class="output-value">0.00</span> mA</p>
                    <p><span class="label">Collector-Emitter Voltage (Vce):</span> <span id="vceResult" class="output-value">0.00</span> V</p>
                    <div class="mode-indicator-container">
                        <span class="label">Transistor Mode:</span>
                        <span id="modeIndicator" class="mode-indicator mode-cutoff">Cutoff</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Constants for transistor model
        const VBE_ON = 0.7;       // Volts, Base-Emitter turn-on voltage
        const BETA = 100;         // DC current gain (hFE)
        const VCE_SAT = 0.2;      // Volts, Collector-Emitter saturation voltage
        const RB_INTERNAL = 10000; // Ohms (10kΩ) - Assumed base resistance for Vb input

        // DOM Elements
        const vbSlider = document.getElementById('vbSlider');
        const vbNumber = document.getElementById('vbNumber');
        const vbValueDisplay = document.getElementById('vbValue');

        const vccSlider = document.getElementById('vccSlider');
        const vccNumber = document.getElementById('vccNumber');
        const vccValueDisplay = document.getElementById('vccValue');

        const rlSlider = document.getElementById('rlSlider');
        const rlNumber = document.getElementById('rlNumber');
        const rlValueDisplay = document.getElementById('rlValue');

        const ibResultDisplay = document.getElementById('ibResult');
        const icResultDisplay = document.getElementById('icResult');
        const vceResultDisplay = document.getElementById('vceResult');
        const modeIndicator = document.getElementById('modeIndicator');

        function updateCircuit() {
            const Vb_input = parseFloat(vbSlider.value);
            const Vcc = parseFloat(vccSlider.value);
            const RL = parseFloat(rlSlider.value);

            vbValueDisplay.textContent = Vb_input.toFixed(2);
            vccValueDisplay.textContent = Vcc.toFixed(1);
            rlValueDisplay.textContent = RL.toFixed(0);

            let Ib_mA = 0;
            let Ic_mA = 0;
            let Vce = Vcc;
            let mode = "Cutoff";

            if (Vb_input > VBE_ON && RB_INTERNAL > 0) {
                Ib_mA = ((Vb_input - VBE_ON) / RB_INTERNAL) * 1000;
            }
            Ib_mA = Math.max(0, Ib_mA);

            if (Vcc < 0.01) { // Effectively Vcc is zero
                mode = "Cutoff";
            } else if (Ib_mA < 1e-6 || Vb_input <= VBE_ON) { // Ib negligible or Vb too low
                mode = "Cutoff";
            } else { // Ib > 0, transistor is potentially on
                const Ic_active_mA = BETA * Ib_mA;
                let Ic_saturation_limit_mA = 0;

                if (RL > 0 && Vcc > VCE_SAT) {
                    Ic_saturation_limit_mA = ((Vcc - VCE_SAT) / RL) * 1000;
                }
                Ic_saturation_limit_mA = Math.max(0, Ic_saturation_limit_mA);

                if (Ic_active_mA >= Ic_saturation_limit_mA && Ic_saturation_limit_mA > 0) {
                    mode = "Saturation";
                    Ic_mA = Ic_saturation_limit_mA;
                    Vce = VCE_SAT;
                } else {
                    mode = "Active";
                    Ic_mA = Ic_active_mA;
                    Vce = Vcc - (Ic_mA / 1000 * RL);
                    if (Vce < VCE_SAT) {
                        mode = "Saturation";
                        Vce = VCE_SAT;
                        Ic_mA = (RL > 0 && Vcc > VCE_SAT) ? (((Vcc - VCE_SAT) / RL) * 1000) : 0;
                        Ic_mA = Math.max(0, Ic_mA); // Ensure non-negative
                    }
                }
                // If Vcc is too low to sustain even VCE_SAT, re-evaluate to Cutoff
                if (Vcc <= VCE_SAT && mode !== "Cutoff") {
                    mode = "Cutoff";
                }
            }

            // Final adjustments for Cutoff mode
            if (mode === "Cutoff") {
                Ib_mA = 0; // In cutoff, all currents are zero as per prompt
                Ic_mA = 0;
                Vce = Vcc;
            }
            
            // Clamp Vce to be between 0 and Vcc
            Vce = Math.max(0, Vce);
            Vce = Math.min(Vcc, Vce);

            // Update display
            ibResultDisplay.textContent = Ib_mA.toFixed(2);
            icResultDisplay.textContent = Ic_mA.toFixed(2);
            vceResultDisplay.textContent = Vce.toFixed(2);

            modeIndicator.textContent = mode;
            modeIndicator.className = 'mode-indicator'; // Reset classes
            if (mode === "Cutoff") modeIndicator.classList.add('mode-cutoff');
            else if (mode === "Active") modeIndicator.classList.add('mode-active');
            else if (mode === "Saturation") modeIndicator.classList.add('mode-saturation');
        }

        function syncInputs(slider, numberInput, isFloat) {
            const updateFromSlider = () => {
                const val = parseFloat(slider.value);
                numberInput.value = isFloat ? val.toFixed(isFloat) : val.toFixed(0);
                updateCircuit();
            };
            const updateFromNumber = () => {
                let val = parseFloat(numberInput.value);
                const min = parseFloat(slider.min);
                const max = parseFloat(slider.max);
                if (isNaN(val)) val = min;
                val = Math.max(min, Math.min(max, val));
                numberInput.value = isFloat ? val.toFixed(isFloat) : val.toFixed(0);
                slider.value = val;
                updateCircuit();
            };
            slider.addEventListener('input', updateFromSlider);
            numberInput.addEventListener('change', updateFromNumber); // Use 'change' to avoid issues with partial input
            numberInput.addEventListener('blur', updateFromNumber); // Also update on blur
        }

        syncInputs(vbSlider, vbNumber, 2); // 2 decimal places for Vb
        syncInputs(vccSlider, vccNumber, 1); // 1 decimal place for Vcc
        syncInputs(rlSlider, rlNumber, false); // 0 decimal places for RL

        // Initial calculation on page load
        updateCircuit();
    </script>
</body>
</html>
