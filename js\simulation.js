/**
 * نظام محاكاة الدوائر التفاعلي
 * تطوير: د. محمد يعقوب إسماعيل
 */

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة المتغيرات العامة
    const canvas = document.getElementById('main-canvas');
    const ctx = canvas.getContext('2d');
    const circuitCanvas = document.getElementById('circuit-canvas');
    const componentsList = document.querySelector('.components-list');
    const propertiesPanel = document.querySelector('.properties-panel');
    const noSelectionMessage = document.getElementById('no-selection-message');
    const componentProperties = document.getElementById('component-properties');
    const componentSpecificProperties = document.getElementById('component-specific-properties');
    const componentTitle = document.getElementById('component-title');
    const componentFunction = document.getElementById('component-function');
    const componentSearch = document.getElementById('component-search');
    const waveformCanvas = document.getElementById('waveform-canvas');
    const waveformCtx = waveformCanvas ? waveformCanvas.getContext('2d') : null;

    // حالة المحاكاة
    let simulationState = {
        running: false,
        paused: false,
        components: [],
        wires: [],
        selectedComponent: null,
        selectedWire: null,
        activeTool: 'select',
        grid: {
            size: 20,
            snap: true
        },
        zoom: 1,
        offset: { x: 0, y: 0 },
        mousePos: { x: 0, y: 0 },
        dragStart: null,
        lastTimestamp: 0,
        simulationSpeed: 1,
        nodes: {},
        results: {}
    };

    // تعريف خصائص العناصر
    const componentDefinitions = {
        // مصادر الطاقة
        dc_voltage: {
            name: 'مصدر جهد مستمر',
            category: 'sources',
            terminals: ['positive', 'negative'],
            terminalPositions: function(x, y, rotation) {
                const angle = (rotation || 0) * Math.PI / 180;
                const cos = Math.cos(angle);
                const sin = Math.sin(angle);
                return {
                    positive: { x: x + 0 * cos - (-40) * sin, y: y + 0 * sin + (-40) * cos },
                    negative: { x: x + 0 * cos - 40 * sin, y: y + 0 * sin + 40 * cos }
                };
            },
            properties: {
                voltage: { type: 'number', default: 5, min: 0, max: 100, step: 0.1, unit: 'V', label: 'الجهد' }
            },
            function: 'يوفر جهد كهربائي ثابت (مستمر) للدائرة. يستخدم لتغذية الدائرة بالطاقة اللازمة لتشغيلها.',
            simulate: function(component, circuit, time) {
                // حساب الجهد بين طرفي المصدر
                return {
                    voltage: component.properties.voltage,
                    current: 0, // سيتم حسابه لاحقاً بناءً على الدائرة
                    power: 0 // سيتم حسابه لاحقاً بناءً على الدائرة
                };
            },
            render: function(ctx, component, selected) {
                const x = component.x;
                const y = component.y;
                const rotation = component.rotation || 0;

                // حفظ حالة الكانفاس
                ctx.save();

                // تطبيق التدوير
                ctx.translate(x, y);
                ctx.rotate(rotation * Math.PI / 180);
                ctx.translate(-x, -y);

                // رسم الدائرة
                ctx.beginPath();
                ctx.arc(x, y, 20, 0, 2 * Math.PI);
                ctx.strokeStyle = selected ? '#ff5722' : '#000000';
                ctx.lineWidth = selected ? 2 : 1;
                ctx.stroke();

                // رسم الرمز الموجب
                ctx.beginPath();
                ctx.moveTo(x, y - 15);
                ctx.lineTo(x, y - 5);
                ctx.moveTo(x - 5, y - 10);
                ctx.lineTo(x + 5, y - 10);
                ctx.stroke();

                // رسم الرمز السالب
                ctx.beginPath();
                ctx.moveTo(x - 5, y + 10);
                ctx.lineTo(x + 5, y + 10);
                ctx.stroke();

                // رسم الأطراف
                ctx.beginPath();
                ctx.moveTo(x, y - 20);
                ctx.lineTo(x, y - 40);
                ctx.moveTo(x, y + 20);
                ctx.lineTo(x, y + 40);
                ctx.stroke();

                // رسم قيمة الجهد
                if (selected) {
                    ctx.fillStyle = '#000000';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`${component.properties.voltage}V`, x, y + 35);
                }

                // استعادة حالة الكانفاس
                ctx.restore();
            }
        },
        ac_voltage: {
            name: 'مصدر جهد متردد',
            category: 'sources',
            terminals: ['positive', 'negative'],
            terminalPositions: function(x, y, rotation) {
                const angle = (rotation || 0) * Math.PI / 180;
                const cos = Math.cos(angle);
                const sin = Math.sin(angle);
                return {
                    positive: { x: x + 0 * cos - (-40) * sin, y: y + 0 * sin + (-40) * cos },
                    negative: { x: x + 0 * cos - 40 * sin, y: y + 0 * sin + 40 * cos }
                };
            },
            properties: {
                amplitude: { type: 'number', default: 5, min: 0, max: 100, step: 0.1, unit: 'V', label: 'سعة الموجة' },
                frequency: { type: 'number', default: 1000, min: 1, max: 100000, step: 1, unit: 'Hz', label: 'التردد' },
                phase: { type: 'number', default: 0, min: 0, max: 360, step: 1, unit: '°', label: 'الطور' },
                waveform: { type: 'select', options: ['sine', 'square', 'triangle', 'sawtooth'], default: 'sine', label: 'شكل الموجة' }
            },
            function: 'يوفر جهد كهربائي متغير (متردد) للدائرة. يستخدم لتوليد إشارات متغيرة مع الزمن مثل الموجات الجيبية.',
            simulate: function(component, circuit, time) {
                const amplitude = component.properties.amplitude;
                const frequency = component.properties.frequency;
                const phase = component.properties.phase * Math.PI / 180;
                const waveform = component.properties.waveform;

                let voltage = 0;
                const t = time * frequency;

                // حساب الجهد حسب شكل الموجة
                switch (waveform) {
                    case 'sine':
                        voltage = amplitude * Math.sin(2 * Math.PI * t + phase);
                        break;
                    case 'square':
                        voltage = amplitude * (Math.sin(2 * Math.PI * t + phase) >= 0 ? 1 : -1);
                        break;
                    case 'triangle':
                        voltage = amplitude * (1 - 4 * Math.abs(Math.round(t - 0.25) - (t - 0.25)));
                        break;
                    case 'sawtooth':
                        voltage = amplitude * (2 * (t - Math.floor(t + 0.5)));
                        break;
                }

                return {
                    voltage: voltage,
                    current: 0, // سيتم حسابه لاحقاً بناءً على الدائرة
                    power: 0 // سيتم حسابه لاحقاً بناءً على الدائرة
                };
            },
            render: function(ctx, component, selected) {
                const x = component.x;
                const y = component.y;
                const rotation = component.rotation || 0;

                // حفظ حالة الكانفاس
                ctx.save();

                // تطبيق التدوير
                ctx.translate(x, y);
                ctx.rotate(rotation * Math.PI / 180);
                ctx.translate(-x, -y);

                // رسم الدائرة
                ctx.beginPath();
                ctx.arc(x, y, 20, 0, 2 * Math.PI);
                ctx.strokeStyle = selected ? '#ff5722' : '#000000';
                ctx.lineWidth = selected ? 2 : 1;
                ctx.stroke();

                // رسم الموجة الجيبية
                ctx.beginPath();
                ctx.moveTo(x - 15, y);
                for (let i = -15; i <= 15; i++) {
                    ctx.lineTo(x + i, y - 10 * Math.sin((i + 15) * Math.PI / 15));
                }
                ctx.stroke();

                // رسم الأطراف
                ctx.beginPath();
                ctx.moveTo(x, y - 20);
                ctx.lineTo(x, y - 40);
                ctx.moveTo(x, y + 20);
                ctx.lineTo(x, y + 40);
                ctx.stroke();

                // رسم قيمة الجهد
                if (selected) {
                    ctx.fillStyle = '#000000';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`${component.properties.amplitude}V ${component.properties.frequency}Hz`, x, y + 35);
                }

                // استعادة حالة الكانفاس
                ctx.restore();
            }
        },
        dc_current: {
            name: 'مصدر تيار مستمر',
            category: 'sources',
            terminals: ['output', 'input'],
            terminalPositions: function(x, y, rotation) {
                const angle = (rotation || 0) * Math.PI / 180;
                const cos = Math.cos(angle);
                const sin = Math.sin(angle);
                return {
                    output: { x: x + 0 * cos - (-40) * sin, y: y + 0 * sin + (-40) * cos },
                    input: { x: x + 0 * cos - 40 * sin, y: y + 0 * sin + 40 * cos }
                };
            },
            properties: {
                current: { type: 'number', default: 0.01, min: 0, max: 10, step: 0.001, unit: 'A', label: 'التيار' }
            },
            function: 'يوفر تيار كهربائي ثابت للدائرة بغض النظر عن الجهد. يستخدم في دوائر التغذية الثابتة للتيار.',
            simulate: function(component, circuit, time) {
                return {
                    current: component.properties.current,
                    voltage: 0, // سيتم حسابه لاحقاً بناءً على الدائرة
                    power: 0 // سيتم حسابه لاحقاً بناءً على الدائرة
                };
            },
            render: function(ctx, component, selected) {
                const x = component.x;
                const y = component.y;
                const rotation = component.rotation || 0;

                // حفظ حالة الكانفاس
                ctx.save();

                // تطبيق التدوير
                ctx.translate(x, y);
                ctx.rotate(rotation * Math.PI / 180);
                ctx.translate(-x, -y);

                // رسم الدائرة
                ctx.beginPath();
                ctx.arc(x, y, 20, 0, 2 * Math.PI);
                ctx.strokeStyle = selected ? '#ff5722' : '#000000';
                ctx.lineWidth = selected ? 2 : 1;
                ctx.stroke();

                // رسم سهم التيار
                ctx.beginPath();
                ctx.moveTo(x, y - 10);
                ctx.lineTo(x, y + 10);
                ctx.moveTo(x - 5, y + 5);
                ctx.lineTo(x, y + 10);
                ctx.lineTo(x + 5, y + 5);
                ctx.stroke();

                // رسم الأطراف
                ctx.beginPath();
                ctx.moveTo(x, y - 20);
                ctx.lineTo(x, y - 40);
                ctx.moveTo(x, y + 20);
                ctx.lineTo(x, y + 40);
                ctx.stroke();

                // رسم قيمة التيار
                if (selected) {
                    ctx.fillStyle = '#000000';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`${component.properties.current}A`, x, y + 35);
                }

                // استعادة حالة الكانفاس
                ctx.restore();
            }
        },

        // العناصر السلبية
        resistor: {
            name: 'مقاومة',
            category: 'passive',
            terminals: ['terminal1', 'terminal2'],
            terminalPositions: function(x, y, rotation) {
                const angle = (rotation || 0) * Math.PI / 180;
                const cos = Math.cos(angle);
                const sin = Math.sin(angle);
                return {
                    terminal1: { x: x - 40 * cos, y: y - 40 * sin },
                    terminal2: { x: x + 40 * cos, y: y + 40 * sin }
                };
            },
            properties: {
                resistance: { type: 'number', default: 1000, min: 0, max: 1000000, step: 10, unit: 'Ω', label: 'المقاومة' },
                power_rating: { type: 'number', default: 0.25, min: 0.125, max: 10, step: 0.125, unit: 'W', label: 'القدرة المقننة' }
            },
            function: 'تحد من تدفق التيار الكهربائي في الدائرة. تستخدم لتحديد قيمة التيار أو لتقسيم الجهد في الدائرة.',
            simulate: function(component, circuit, time) {
                // حساب التيار والجهد باستخدام قانون أوم
                const resistance = component.properties.resistance;
                const voltage = circuit.getVoltageDrop(component) || 0;
                const current = resistance > 0 ? voltage / resistance : 0;
                const power = voltage * current;

                return {
                    voltage: voltage,
                    current: current,
                    power: power,
                    resistance: resistance
                };
            },
            render: function(ctx, component, selected) {
                const x = component.x;
                const y = component.y;
                const rotation = component.rotation || 0;

                // حفظ حالة الكانفاس
                ctx.save();

                // تطبيق التدوير
                ctx.translate(x, y);
                ctx.rotate(rotation * Math.PI / 180);
                ctx.translate(-x, -y);

                // رسم المقاومة
                ctx.beginPath();
                ctx.moveTo(x - 40, y);
                ctx.lineTo(x - 15, y);

                // رسم الجزء المتعرج
                ctx.lineTo(x - 10, y - 10);
                ctx.lineTo(x, y + 10);
                ctx.lineTo(x + 10, y - 10);
                ctx.lineTo(x + 15, y);

                ctx.lineTo(x + 40, y);

                ctx.strokeStyle = selected ? '#ff5722' : '#000000';
                ctx.lineWidth = selected ? 2 : 1;
                ctx.stroke();

                // رسم قيمة المقاومة
                if (selected) {
                    ctx.fillStyle = '#000000';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';

                    // تنسيق القيمة بشكل مناسب
                    let valueText = '';
                    const resistance = component.properties.resistance;

                    if (resistance >= 1000000) {
                        valueText = `${(resistance / 1000000).toFixed(1)}MΩ`;
                    } else if (resistance >= 1000) {
                        valueText = `${(resistance / 1000).toFixed(1)}kΩ`;
                    } else {
                        valueText = `${resistance}Ω`;
                    }

                    ctx.fillText(valueText, x, y - 15);
                }

                // استعادة حالة الكانفاس
                ctx.restore();
            }
        },
        capacitor: {
            name: 'مكثف',
            category: 'passive',
            terminals: ['terminal1', 'terminal2'],
            terminalPositions: function(x, y, rotation) {
                const angle = (rotation || 0) * Math.PI / 180;
                const cos = Math.cos(angle);
                const sin = Math.sin(angle);
                return {
                    terminal1: { x: x - 40 * cos, y: y - 40 * sin },
                    terminal2: { x: x + 40 * cos, y: y + 40 * sin }
                };
            },
            properties: {
                capacitance: { type: 'number', default: 1, min: 0.001, max: 10000, step: 0.1, unit: 'μF', label: 'السعة' },
                voltage_rating: { type: 'number', default: 50, min: 10, max: 1000, step: 10, unit: 'V', label: 'الجهد المقنن' },
                type: { type: 'select', options: ['ceramic', 'electrolytic', 'tantalum', 'film'], default: 'ceramic', label: 'النوع' }
            },
            function: 'يخزن الشحنات الكهربائية ويسمح بمرور التيار المتردد ويمنع التيار المستمر. يستخدم في دوائر الترشيح والتوقيت.',
            simulate: function(component, circuit, time) {
                const capacitance = component.properties.capacitance * 1e-6; // تحويل من ميكروفاراد إلى فاراد
                const voltage = circuit.getVoltageDrop(component) || 0;

                // حساب التيار باستخدام معادلة المكثف I = C * dV/dt
                const prevVoltage = component._prevVoltage || 0;
                const deltaTime = circuit.deltaTime || 0.001;
                const current = capacitance * (voltage - prevVoltage) / deltaTime;

                // تخزين الجهد الحالي للاستخدام في الحساب التالي
                component._prevVoltage = voltage;

                // حساب الطاقة المخزنة
                const energy = 0.5 * capacitance * voltage * voltage;

                return {
                    voltage: voltage,
                    current: current,
                    power: voltage * current,
                    energy: energy,
                    capacitance: capacitance
                };
            },
            render: function(ctx, component, selected) {
                const x = component.x;
                const y = component.y;
                const rotation = component.rotation || 0;
                const isElectrolytic = component.properties.type === 'electrolytic';

                // حفظ حالة الكانفاس
                ctx.save();

                // تطبيق التدوير
                ctx.translate(x, y);
                ctx.rotate(rotation * Math.PI / 180);
                ctx.translate(-x, -y);

                // رسم المكثف
                ctx.beginPath();
                ctx.moveTo(x - 40, y);
                ctx.lineTo(x - 10, y);

                // رسم اللوح الأول
                ctx.moveTo(x - 10, y - 15);
                ctx.lineTo(x - 10, y + 15);

                // رسم اللوح الثاني
                ctx.moveTo(x + 10, y - 15);
                ctx.lineTo(x + 10, y + 15);

                // رسم الطرف الثاني
                ctx.moveTo(x + 10, y);
                ctx.lineTo(x + 40, y);

                ctx.strokeStyle = selected ? '#ff5722' : '#000000';
                ctx.lineWidth = selected ? 2 : 1;
                ctx.stroke();

                // إذا كان المكثف إلكتروليتي، نرسم علامة القطبية
                if (isElectrolytic) {
                    // رسم علامة الموجب
                    ctx.beginPath();
                    ctx.moveTo(x + 15, y - 10);
                    ctx.lineTo(x + 25, y - 10);
                    ctx.moveTo(x + 20, y - 15);
                    ctx.lineTo(x + 20, y - 5);
                    ctx.stroke();

                    // رسم خط منقط للإشارة إلى القطب السالب
                    ctx.beginPath();
                    ctx.setLineDash([2, 2]);
                    ctx.moveTo(x - 10, y - 15);
                    ctx.lineTo(x - 10, y + 15);
                    ctx.stroke();
                    ctx.setLineDash([]);
                }

                // رسم قيمة المكثف
                if (selected) {
                    ctx.fillStyle = '#000000';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';

                    // تنسيق القيمة بشكل مناسب
                    let valueText = '';
                    const capacitance = component.properties.capacitance;

                    if (capacitance >= 1000) {
                        valueText = `${(capacitance / 1000).toFixed(1)}mF`;
                    } else if (capacitance >= 1) {
                        valueText = `${capacitance.toFixed(1)}μF`;
                    } else {
                        valueText = `${(capacitance * 1000).toFixed(0)}nF`;
                    }

                    ctx.fillText(valueText, x, y - 20);
                }

                // استعادة حالة الكانفاس
                ctx.restore();
            }
        },
        inductor: {
            name: 'ملف',
            category: 'passive',
            terminals: ['terminal1', 'terminal2'],
            terminalPositions: function(x, y, rotation) {
                const angle = (rotation || 0) * Math.PI / 180;
                const cos = Math.cos(angle);
                const sin = Math.sin(angle);
                return {
                    terminal1: { x: x - 40 * cos, y: y - 40 * sin },
                    terminal2: { x: x + 40 * cos, y: y + 40 * sin }
                };
            },
            properties: {
                inductance: { type: 'number', default: 1, min: 0.001, max: 10000, step: 0.1, unit: 'mH', label: 'الحث' },
                current_rating: { type: 'number', default: 1, min: 0.1, max: 100, step: 0.1, unit: 'A', label: 'التيار المقنن' }
            },
            function: 'يخزن الطاقة في المجال المغناطيسي ويقاوم التغيرات في التيار. يستخدم في دوائر الترشيح والرنين.',
            simulate: function(component, circuit, time) {
                const inductance = component.properties.inductance * 1e-3; // تحويل من ميلي هنري إلى هنري
                const current = circuit.getCurrent(component) || 0;

                // حساب الجهد باستخدام معادلة الملف V = L * dI/dt
                const prevCurrent = component._prevCurrent || 0;
                const deltaTime = circuit.deltaTime || 0.001;
                const voltage = inductance * (current - prevCurrent) / deltaTime;

                // تخزين التيار الحالي للاستخدام في الحساب التالي
                component._prevCurrent = current;

                // حساب الطاقة المخزنة
                const energy = 0.5 * inductance * current * current;

                return {
                    voltage: voltage,
                    current: current,
                    power: voltage * current,
                    energy: energy,
                    inductance: inductance
                };
            },
            render: function(ctx, component, selected) {
                const x = component.x;
                const y = component.y;
                const rotation = component.rotation || 0;

                // حفظ حالة الكانفاس
                ctx.save();

                // تطبيق التدوير
                ctx.translate(x, y);
                ctx.rotate(rotation * Math.PI / 180);
                ctx.translate(-x, -y);

                // رسم الملف
                ctx.beginPath();
                ctx.moveTo(x - 40, y);
                ctx.lineTo(x - 25, y);

                // رسم الملفات
                const loopRadius = 5;
                const loopCount = 5;
                const loopSpacing = 10;

                for (let i = 0; i < loopCount; i++) {
                    const loopX = x - 20 + i * loopSpacing;

                    ctx.moveTo(loopX, y);
                    ctx.arc(loopX, y, loopRadius, 0, 2 * Math.PI);
                }

                // رسم الطرف الثاني
                ctx.moveTo(x + 25, y);
                ctx.lineTo(x + 40, y);

                ctx.strokeStyle = selected ? '#ff5722' : '#000000';
                ctx.lineWidth = selected ? 2 : 1;
                ctx.stroke();

                // رسم قيمة الملف
                if (selected) {
                    ctx.fillStyle = '#000000';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';

                    // تنسيق القيمة بشكل مناسب
                    let valueText = '';
                    const inductance = component.properties.inductance;

                    if (inductance >= 1000) {
                        valueText = `${(inductance / 1000).toFixed(1)}H`;
                    } else if (inductance >= 1) {
                        valueText = `${inductance.toFixed(1)}mH`;
                    } else {
                        valueText = `${(inductance * 1000).toFixed(0)}μH`;
                    }

                    ctx.fillText(valueText, x, y - 15);
                }

                // استعادة حالة الكانفاس
                ctx.restore();
            }
        },

        // العناصر النشطة - الترانزستورات
        npn: {
            name: 'ترانزستور NPN',
            category: 'transistors',
            terminals: ['collector', 'base', 'emitter'],
            terminalPositions: function(x, y, rotation) {
                const angle = (rotation || 0) * Math.PI / 180;
                const cos = Math.cos(angle);
                const sin = Math.sin(angle);
                return {
                    collector: { x: x + 0 * cos - (-30) * sin, y: y + 0 * sin + (-30) * cos },
                    base: { x: x + (-30) * cos - 0 * sin, y: y + (-30) * sin + 0 * cos },
                    emitter: { x: x + 0 * cos - 30 * sin, y: y + 0 * sin + 30 * cos }
                };
            },
            properties: {
                beta: { type: 'number', default: 100, min: 10, max: 500, step: 1, unit: '', label: 'معامل التضخيم (β)' },
                vbe: { type: 'number', default: 0.7, min: 0.5, max: 1, step: 0.01, unit: 'V', label: 'جهد القاعدة-باعث (VBE)' },
                vce_sat: { type: 'number', default: 0.2, min: 0.05, max: 1, step: 0.01, unit: 'V', label: 'جهد المجمع-باعث عند التشبع (VCE sat)' },
                ic_max: { type: 'number', default: 0.5, min: 0.1, max: 10, step: 0.1, unit: 'A', label: 'أقصى تيار للمجمع (IC max)' }
            },
            function: 'ترانزستور ثنائي القطبية من نوع NPN. يستخدم لتضخيم الإشارات أو كمفتاح إلكتروني. يتحكم تيار القاعدة الصغير في تيار المجمع الأكبر.',
            simulate: function(component, circuit, time) {
                const beta = component.properties.beta;
                const vbe = component.properties.vbe;
                const vce_sat = component.properties.vce_sat;
                const ic_max = component.properties.ic_max;

                // الحصول على الجهود عند الأطراف
                const vc = circuit.getNodeVoltage(component, 'collector') || 0;
                const vb = circuit.getNodeVoltage(component, 'base') || 0;
                const ve = circuit.getNodeVoltage(component, 'emitter') || 0;

                // حساب جهود الانحياز
                const vbe_actual = vb - ve;
                const vce_actual = vc - ve;

                // حساب تيارات الترانزستور
                let ib = 0;
                let ic = 0;
                let ie = 0;

                if (vbe_actual >= vbe) {
                    // الترانزستور في حالة التشغيل
                    ib = (vbe_actual - vbe) / 1000; // تقريب لمقاومة القاعدة الداخلية

                    if (vce_actual <= vce_sat) {
                        // حالة التشبع
                        ic = Math.min(beta * ib, ic_max);
                    } else {
                        // حالة التضخيم النشط
                        ic = Math.min(beta * ib, ic_max);
                    }

                    ie = ib + ic;
                }

                // حساب القدرة المبددة
                const power = vce_actual * ic + vbe_actual * ib;

                return {
                    vbe: vbe_actual,
                    vce: vce_actual,
                    ib: ib,
                    ic: ic,
                    ie: ie,
                    power: power,
                    state: vbe_actual < vbe ? 'cutoff' : (vce_actual <= vce_sat ? 'saturation' : 'active')
                };
            },
            render: function(ctx, component, selected) {
                const x = component.x;
                const y = component.y;
                const rotation = component.rotation || 0;

                // حفظ حالة الكانفاس
                ctx.save();

                // تطبيق التدوير
                ctx.translate(x, y);
                ctx.rotate(rotation * Math.PI / 180);
                ctx.translate(-x, -y);

                // رسم الدائرة
                ctx.beginPath();
                ctx.arc(x, y, 20, 0, 2 * Math.PI);
                ctx.strokeStyle = selected ? '#ff5722' : '#000000';
                ctx.lineWidth = selected ? 2 : 1;
                ctx.stroke();

                // رسم خط القاعدة
                ctx.beginPath();
                ctx.moveTo(x - 30, y);
                ctx.lineTo(x - 10, y);
                ctx.stroke();

                // رسم خط المجمع والباعث
                ctx.beginPath();
                ctx.moveTo(x, y - 30);
                ctx.lineTo(x, y - 10);
                ctx.moveTo(x, y + 10);
                ctx.lineTo(x, y + 30);
                ctx.stroke();

                // رسم رمز الترانزستور NPN
                ctx.beginPath();
                ctx.moveTo(x - 10, y - 10);
                ctx.lineTo(x - 10, y + 10);
                ctx.moveTo(x - 10, y);
                ctx.lineTo(x + 10, y - 10);
                ctx.lineTo(x + 10, y - 15);
                ctx.moveTo(x - 10, y);
                ctx.lineTo(x + 10, y + 10);
                ctx.lineTo(x + 10, y + 15);

                // رسم السهم للإشارة إلى نوع NPN
                ctx.moveTo(x + 5, y + 5);
                ctx.lineTo(x + 10, y + 10);
                ctx.lineTo(x + 15, y + 5);

                ctx.stroke();

                // رسم تسميات الأطراف
                if (selected) {
                    ctx.fillStyle = '#000000';
                    ctx.font = '10px Arial';
                    ctx.textAlign = 'center';

                    ctx.fillText('C', x, y - 25);
                    ctx.fillText('B', x - 25, y);
                    ctx.fillText('E', x, y + 25);

                    // رسم قيمة معامل التضخيم
                    ctx.font = '12px Arial';
                    ctx.fillText(`β=${component.properties.beta}`, x + 30, y);
                }

                // استعادة حالة الكانفاس
                ctx.restore();
            }
        },
        pnp: {
            name: 'ترانزستور PNP',
            category: 'transistors',
            terminals: ['collector', 'base', 'emitter'],
            terminalPositions: function(x, y, rotation) {
                const angle = (rotation || 0) * Math.PI / 180;
                const cos = Math.cos(angle);
                const sin = Math.sin(angle);
                return {
                    collector: { x: x + 0 * cos - (-30) * sin, y: y + 0 * sin + (-30) * cos },
                    base: { x: x + (-30) * cos - 0 * sin, y: y + (-30) * sin + 0 * cos },
                    emitter: { x: x + 0 * cos - 30 * sin, y: y + 0 * sin + 30 * cos }
                };
            },
            properties: {
                beta: { type: 'number', default: 100, min: 10, max: 500, step: 1, unit: '', label: 'معامل التضخيم (β)' },
                vbe: { type: 'number', default: 0.7, min: 0.5, max: 1, step: 0.01, unit: 'V', label: 'جهد القاعدة-باعث (VBE)' },
                vce_sat: { type: 'number', default: 0.2, min: 0.05, max: 1, step: 0.01, unit: 'V', label: 'جهد المجمع-باعث عند التشبع (VCE sat)' },
                ic_max: { type: 'number', default: 0.5, min: 0.1, max: 10, step: 0.1, unit: 'A', label: 'أقصى تيار للمجمع (IC max)' }
            },
            function: 'ترانزستور ثنائي القطبية من نوع PNP. يعمل بشكل معاكس للنوع NPN، حيث يتدفق التيار من الباعث إلى المجمع. يستخدم في دوائر التضخيم والتحكم.',
            simulate: function(component, circuit, time) {
                const beta = component.properties.beta;
                const vbe = component.properties.vbe;
                const vce_sat = component.properties.vce_sat;
                const ic_max = component.properties.ic_max;

                // الحصول على الجهود عند الأطراف
                const vc = circuit.getNodeVoltage(component, 'collector') || 0;
                const vb = circuit.getNodeVoltage(component, 'base') || 0;
                const ve = circuit.getNodeVoltage(component, 'emitter') || 0;

                // حساب جهود الانحياز (عكس NPN)
                const vbe_actual = ve - vb;
                const vce_actual = ve - vc;

                // حساب تيارات الترانزستور
                let ib = 0;
                let ic = 0;
                let ie = 0;

                if (vbe_actual >= vbe) {
                    // الترانزستور في حالة التشغيل
                    ib = (vbe_actual - vbe) / 1000; // تقريب لمقاومة القاعدة الداخلية

                    if (vce_actual <= vce_sat) {
                        // حالة التشبع
                        ic = Math.min(beta * ib, ic_max);
                    } else {
                        // حالة التضخيم النشط
                        ic = Math.min(beta * ib, ic_max);
                    }

                    ie = ib + ic;
                }

                // حساب القدرة المبددة
                const power = vce_actual * ic + vbe_actual * ib;

                return {
                    vbe: vbe_actual,
                    vce: vce_actual,
                    ib: ib,
                    ic: ic,
                    ie: ie,
                    power: power,
                    state: vbe_actual < vbe ? 'cutoff' : (vce_actual <= vce_sat ? 'saturation' : 'active')
                };
            },
            render: function(ctx, component, selected) {
                const x = component.x;
                const y = component.y;
                const rotation = component.rotation || 0;

                // حفظ حالة الكانفاس
                ctx.save();

                // تطبيق التدوير
                ctx.translate(x, y);
                ctx.rotate(rotation * Math.PI / 180);
                ctx.translate(-x, -y);

                // رسم الدائرة
                ctx.beginPath();
                ctx.arc(x, y, 20, 0, 2 * Math.PI);
                ctx.strokeStyle = selected ? '#ff5722' : '#000000';
                ctx.lineWidth = selected ? 2 : 1;
                ctx.stroke();

                // رسم خط القاعدة
                ctx.beginPath();
                ctx.moveTo(x - 30, y);
                ctx.lineTo(x - 10, y);
                ctx.stroke();

                // رسم خط المجمع والباعث
                ctx.beginPath();
                ctx.moveTo(x, y - 30);
                ctx.lineTo(x, y - 10);
                ctx.moveTo(x, y + 10);
                ctx.lineTo(x, y + 30);
                ctx.stroke();

                // رسم رمز الترانزستور PNP
                ctx.beginPath();
                ctx.moveTo(x - 10, y - 10);
                ctx.lineTo(x - 10, y + 10);
                ctx.moveTo(x - 10, y);
                ctx.lineTo(x + 10, y - 10);
                ctx.lineTo(x + 10, y - 15);
                ctx.moveTo(x - 10, y);
                ctx.lineTo(x + 10, y + 10);
                ctx.lineTo(x + 10, y + 15);

                // رسم السهم للإشارة إلى نوع PNP (عكس اتجاه NPN)
                ctx.moveTo(x - 5, y);
                ctx.lineTo(x - 10, y);
                ctx.lineTo(x - 5, y - 5);

                ctx.stroke();

                // رسم تسميات الأطراف
                if (selected) {
                    ctx.fillStyle = '#000000';
                    ctx.font = '10px Arial';
                    ctx.textAlign = 'center';

                    ctx.fillText('C', x, y - 25);
                    ctx.fillText('B', x - 25, y);
                    ctx.fillText('E', x, y + 25);

                    // رسم قيمة معامل التضخيم
                    ctx.font = '12px Arial';
                    ctx.fillText(`β=${component.properties.beta}`, x + 30, y);
                }

                // استعادة حالة الكانفاس
                ctx.restore();
            }
        },
        mosfet_n: {
            name: 'ترانزستور MOSFET-N',
            category: 'transistors',
            terminals: ['drain', 'gate', 'source'],
            terminalPositions: function(x, y, rotation) {
                const angle = (rotation || 0) * Math.PI / 180;
                const cos = Math.cos(angle);
                const sin = Math.sin(angle);
                return {
                    drain: { x: x + 0 * cos - (-30) * sin, y: y + 0 * sin + (-30) * cos },
                    gate: { x: x + (-30) * cos - 0 * sin, y: y + (-30) * sin + 0 * cos },
                    source: { x: x + 0 * cos - 30 * sin, y: y + 0 * sin + 30 * cos }
                };
            },
            properties: {
                threshold: { type: 'number', default: 2, min: 0.5, max: 10, step: 0.1, unit: 'V', label: 'جهد العتبة (Vth)' },
                rds_on: { type: 'number', default: 0.1, min: 0.01, max: 10, step: 0.01, unit: 'Ω', label: 'مقاومة التوصيل (RDS(on))' },
                transconductance: { type: 'number', default: 1, min: 0.1, max: 10, step: 0.1, unit: 'S', label: 'الناقلية العبورية (gm)' }
            },
            function: 'ترانزستور تأثير المجال أكسيد معدن شبه موصل من نوع N. يستخدم كمفتاح إلكتروني أو مضخم. يتميز بمقاومة دخل عالية جداً.',
            simulate: function(component, circuit, time) {
                const threshold = component.properties.threshold;
                const rds_on = component.properties.rds_on;
                const transconductance = component.properties.transconductance;

                // الحصول على الجهود عند الأطراف
                const vd = circuit.getNodeVoltage(component, 'drain') || 0;
                const vg = circuit.getNodeVoltage(component, 'gate') || 0;
                const vs = circuit.getNodeVoltage(component, 'source') || 0;

                // حساب جهود الانحياز
                const vgs = vg - vs;
                const vds = vd - vs;

                // حساب تيار المصرف
                let id = 0;

                if (vgs > threshold) {
                    // حالة التشغيل
                    if (vds < (vgs - threshold)) {
                        // المنطقة الخطية
                        id = transconductance * ((vgs - threshold) * vds - 0.5 * vds * vds);
                    } else {
                        // منطقة التشبع
                        id = 0.5 * transconductance * Math.pow(vgs - threshold, 2);
                    }
                }

                // حساب القدرة المبددة
                const power = vds * id;

                return {
                    vgs: vgs,
                    vds: vds,
                    id: id,
                    power: power,
                    state: vgs <= threshold ? 'cutoff' : (vds < (vgs - threshold) ? 'linear' : 'saturation')
                };
            },
            render: function(ctx, component, selected) {
                const x = component.x;
                const y = component.y;
                const rotation = component.rotation || 0;

                // حفظ حالة الكانفاس
                ctx.save();

                // تطبيق التدوير
                ctx.translate(x, y);
                ctx.rotate(rotation * Math.PI / 180);
                ctx.translate(-x, -y);

                // رسم الدائرة
                ctx.beginPath();
                ctx.arc(x, y, 20, 0, 2 * Math.PI);
                ctx.strokeStyle = selected ? '#ff5722' : '#000000';
                ctx.lineWidth = selected ? 2 : 1;
                ctx.stroke();

                // رسم خط البوابة
                ctx.beginPath();
                ctx.moveTo(x - 30, y);
                ctx.lineTo(x - 15, y);
                ctx.stroke();

                // رسم خط المصرف والمصدر
                ctx.beginPath();
                ctx.moveTo(x, y - 30);
                ctx.lineTo(x, y - 10);
                ctx.moveTo(x, y + 10);
                ctx.lineTo(x, y + 30);
                ctx.stroke();

                // رسم رمز الترانزستور MOSFET-N
                ctx.beginPath();
                // خط القناة
                ctx.moveTo(x - 15, y - 10);
                ctx.lineTo(x - 15, y + 10);
                // خط البوابة
                ctx.moveTo(x - 15, y);
                ctx.lineTo(x - 10, y);
                // رمز البوابة المعزولة
                ctx.moveTo(x - 10, y - 10);
                ctx.lineTo(x - 10, y + 10);
                // خطوط المصرف والمصدر
                ctx.moveTo(x - 10, y - 10);
                ctx.lineTo(x + 5, y - 10);
                ctx.lineTo(x + 5, y - 5);
                ctx.moveTo(x - 10, y + 10);
                ctx.lineTo(x + 5, y + 10);
                ctx.lineTo(x + 5, y + 5);
                // خط التوصيل
                ctx.moveTo(x + 5, y - 5);
                ctx.lineTo(x + 5, y + 5);

                // سهم للإشارة إلى نوع N
                ctx.moveTo(x + 5, y);
                ctx.lineTo(x + 15, y);
                ctx.moveTo(x + 10, y - 5);
                ctx.lineTo(x + 15, y);
                ctx.lineTo(x + 10, y + 5);

                ctx.stroke();

                // رسم تسميات الأطراف
                if (selected) {
                    ctx.fillStyle = '#000000';
                    ctx.font = '10px Arial';
                    ctx.textAlign = 'center';

                    ctx.fillText('D', x, y - 25);
                    ctx.fillText('G', x - 25, y);
                    ctx.fillText('S', x, y + 25);

                    // رسم قيمة جهد العتبة
                    ctx.font = '12px Arial';
                    ctx.fillText(`Vth=${component.properties.threshold}V`, x + 30, y);
                }

                // استعادة حالة الكانفاس
                ctx.restore();
            }
        },

        // الديودات
        diode: {
            name: 'ديود',
            category: 'diodes',
            terminals: ['anode', 'cathode'],
            terminalPositions: function(x, y, rotation) {
                const angle = (rotation || 0) * Math.PI / 180;
                const cos = Math.cos(angle);
                const sin = Math.sin(angle);
                return {
                    anode: { x: x - 30 * cos, y: y - 30 * sin },
                    cathode: { x: x + 30 * cos, y: y + 30 * sin }
                };
            },
            properties: {
                forward_voltage: { type: 'number', default: 0.7, min: 0.1, max: 5, step: 0.1, unit: 'V', label: 'جهد الانحياز الأمامي' },
                reverse_breakdown: { type: 'number', default: 100, min: 10, max: 1000, step: 10, unit: 'V', label: 'جهد الانهيار العكسي' },
                max_current: { type: 'number', default: 1, min: 0.1, max: 10, step: 0.1, unit: 'A', label: 'أقصى تيار' }
            },
            function: 'يسمح بمرور التيار في اتجاه واحد فقط (من الأنود إلى الكاثود). يستخدم في دوائر التقويم وحماية الدوائر.',
            simulate: function(component, circuit, time) {
                const forward_voltage = component.properties.forward_voltage;
                const reverse_breakdown = component.properties.reverse_breakdown;
                const max_current = component.properties.max_current;

                // الحصول على الجهود عند الأطراف
                const va = circuit.getNodeVoltage(component, 'anode') || 0;
                const vc = circuit.getNodeVoltage(component, 'cathode') || 0;

                // حساب جهد الانحياز
                const vd = va - vc;

                // حساب التيار
                let current = 0;

                if (vd >= forward_voltage) {
                    // الانحياز الأمامي
                    current = Math.min((vd - forward_voltage) / 0.1, max_current); // تقريب لمقاومة الديود الداخلية
                } else if (vd <= -reverse_breakdown) {
                    // الانهيار العكسي
                    current = -max_current;
                }

                // حساب القدرة المبددة
                const power = vd * current;

                return {
                    voltage: vd,
                    current: current,
                    power: power,
                    state: vd >= forward_voltage ? 'forward' : (vd <= -reverse_breakdown ? 'breakdown' : 'reverse')
                };
            },
            render: function(ctx, component, selected) {
                const x = component.x;
                const y = component.y;
                const rotation = component.rotation || 0;

                // حفظ حالة الكانفاس
                ctx.save();

                // تطبيق التدوير
                ctx.translate(x, y);
                ctx.rotate(rotation * Math.PI / 180);
                ctx.translate(-x, -y);

                // رسم الأطراف
                ctx.beginPath();
                ctx.moveTo(x - 30, y);
                ctx.lineTo(x - 15, y);
                ctx.moveTo(x + 15, y);
                ctx.lineTo(x + 30, y);
                ctx.strokeStyle = selected ? '#ff5722' : '#000000';
                ctx.lineWidth = selected ? 2 : 1;
                ctx.stroke();

                // رسم رمز الديود
                ctx.beginPath();
                // المثلث
                ctx.moveTo(x - 15, y - 10);
                ctx.lineTo(x - 15, y + 10);
                ctx.lineTo(x + 15, y);
                ctx.closePath();
                // خط الكاثود
                ctx.moveTo(x + 15, y - 10);
                ctx.lineTo(x + 15, y + 10);

                ctx.stroke();

                // تلوين المثلث
                if (selected) {
                    ctx.fillStyle = 'rgba(255, 87, 34, 0.2)';
                } else {
                    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
                }
                ctx.fill();

                // رسم قيمة جهد الانحياز الأمامي
                if (selected) {
                    ctx.fillStyle = '#000000';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`Vf=${component.properties.forward_voltage}V`, x, y - 15);
                }

                // استعادة حالة الكانفاس
                ctx.restore();
            }
        },
        zener: {
            name: 'ديود زينر',
            category: 'diodes',
            terminals: ['anode', 'cathode'],
            terminalPositions: function(x, y, rotation) {
                const angle = (rotation || 0) * Math.PI / 180;
                const cos = Math.cos(angle);
                const sin = Math.sin(angle);
                return {
                    anode: { x: x - 30 * cos, y: y - 30 * sin },
                    cathode: { x: x + 30 * cos, y: y + 30 * sin }
                };
            },
            properties: {
                forward_voltage: { type: 'number', default: 0.7, min: 0.1, max: 5, step: 0.1, unit: 'V', label: 'جهد الانحياز الأمامي' },
                zener_voltage: { type: 'number', default: 5.1, min: 1.8, max: 200, step: 0.1, unit: 'V', label: 'جهد زينر' },
                max_current: { type: 'number', default: 0.5, min: 0.1, max: 5, step: 0.1, unit: 'A', label: 'أقصى تيار' }
            },
            function: 'ديود خاص يسمح بمرور التيار في الاتجاه العكسي عند جهد معين (جهد زينر). يستخدم في دوائر تنظيم الجهد وحماية الدوائر.',
            simulate: function(component, circuit, time) {
                const forward_voltage = component.properties.forward_voltage;
                const zener_voltage = component.properties.zener_voltage;
                const max_current = component.properties.max_current;

                // الحصول على الجهود عند الأطراف
                const va = circuit.getNodeVoltage(component, 'anode') || 0;
                const vc = circuit.getNodeVoltage(component, 'cathode') || 0;

                // حساب جهد الانحياز
                const vd = va - vc;

                // حساب التيار
                let current = 0;

                if (vd >= forward_voltage) {
                    // الانحياز الأمامي
                    current = Math.min((vd - forward_voltage) / 0.1, max_current); // تقريب لمقاومة الديود الداخلية
                } else if (vd <= -zener_voltage) {
                    // الانحياز العكسي (منطقة زينر)
                    current = Math.max(-max_current, (vd + zener_voltage) / 0.1);
                }

                // حساب القدرة المبددة
                const power = vd * current;

                return {
                    voltage: vd,
                    current: current,
                    power: power,
                    state: vd >= forward_voltage ? 'forward' : (vd <= -zener_voltage ? 'zener' : 'reverse')
                };
            },
            render: function(ctx, component, selected) {
                const x = component.x;
                const y = component.y;
                const rotation = component.rotation || 0;

                // حفظ حالة الكانفاس
                ctx.save();

                // تطبيق التدوير
                ctx.translate(x, y);
                ctx.rotate(rotation * Math.PI / 180);
                ctx.translate(-x, -y);

                // رسم الأطراف
                ctx.beginPath();
                ctx.moveTo(x - 30, y);
                ctx.lineTo(x - 15, y);
                ctx.moveTo(x + 15, y);
                ctx.lineTo(x + 30, y);
                ctx.strokeStyle = selected ? '#ff5722' : '#000000';
                ctx.lineWidth = selected ? 2 : 1;
                ctx.stroke();

                // رسم رمز الديود
                ctx.beginPath();
                // المثلث
                ctx.moveTo(x - 15, y - 10);
                ctx.lineTo(x - 15, y + 10);
                ctx.lineTo(x + 15, y);
                ctx.closePath();
                // خط الكاثود المعدل (زينر)
                ctx.moveTo(x + 15, y - 10);
                ctx.lineTo(x + 15, y);
                ctx.lineTo(x + 10, y + 10);

                ctx.stroke();

                // تلوين المثلث
                if (selected) {
                    ctx.fillStyle = 'rgba(255, 87, 34, 0.2)';
                } else {
                    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
                }
                ctx.fill();

                // رسم قيمة جهد زينر
                if (selected) {
                    ctx.fillStyle = '#000000';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`Vz=${component.properties.zener_voltage}V`, x, y - 15);
                }

                // استعادة حالة الكانفاس
                ctx.restore();
            }
        },
        led: {
            name: 'ديود مضيء (LED)',
            category: 'diodes',
            terminals: ['anode', 'cathode'],
            terminalPositions: function(x, y, rotation) {
                const angle = (rotation || 0) * Math.PI / 180;
                const cos = Math.cos(angle);
                const sin = Math.sin(angle);
                return {
                    anode: { x: x - 30 * cos, y: y - 30 * sin },
                    cathode: { x: x + 30 * cos, y: y + 30 * sin }
                };
            },
            properties: {
                forward_voltage: { type: 'number', default: 2.0, min: 1.5, max: 4, step: 0.1, unit: 'V', label: 'جهد الانحياز الأمامي' },
                max_current: { type: 'number', default: 0.02, min: 0.005, max: 0.1, step: 0.001, unit: 'A', label: 'أقصى تيار' },
                color: { type: 'select', options: ['red', 'green', 'blue', 'yellow', 'white'], default: 'red', label: 'اللون' }
            },
            function: 'ديود يصدر ضوءاً عند مرور التيار فيه في الاتجاه الأمامي. يستخدم كمؤشر ضوئي في الدوائر الإلكترونية.',
            simulate: function(component, circuit, time) {
                const forward_voltage = component.properties.forward_voltage;
                const max_current = component.properties.max_current;

                // الحصول على الجهود عند الأطراف
                const va = circuit.getNodeVoltage(component, 'anode') || 0;
                const vc = circuit.getNodeVoltage(component, 'cathode') || 0;

                // حساب جهد الانحياز
                const vd = va - vc;

                // حساب التيار
                let current = 0;

                if (vd >= forward_voltage) {
                    // الانحياز الأمامي
                    current = Math.min((vd - forward_voltage) / 10, max_current); // مقاومة داخلية أعلى من الديود العادي
                }

                // حساب القدرة المبددة
                const power = vd * current;

                // حساب شدة الإضاءة (0-1)
                const brightness = Math.min(current / max_current, 1);

                return {
                    voltage: vd,
                    current: current,
                    power: power,
                    brightness: brightness,
                    state: vd >= forward_voltage ? 'on' : 'off'
                };
            },
            render: function(ctx, component, selected) {
                const x = component.x;
                const y = component.y;
                const rotation = component.rotation || 0;
                const color = component.properties.color || 'red';

                // تحديد لون LED
                let ledColor;
                switch (color) {
                    case 'red': ledColor = '#ff0000'; break;
                    case 'green': ledColor = '#00ff00'; break;
                    case 'blue': ledColor = '#0000ff'; break;
                    case 'yellow': ledColor = '#ffff00'; break;
                    case 'white': ledColor = '#ffffff'; break;
                    default: ledColor = '#ff0000';
                }

                // حساب شدة الإضاءة
                let brightness = 0;
                if (component._simulationResult && component._simulationResult.brightness) {
                    brightness = component._simulationResult.brightness;
                }

                // حفظ حالة الكانفاس
                ctx.save();

                // تطبيق التدوير
                ctx.translate(x, y);
                ctx.rotate(rotation * Math.PI / 180);
                ctx.translate(-x, -y);

                // رسم الأطراف
                ctx.beginPath();
                ctx.moveTo(x - 30, y);
                ctx.lineTo(x - 15, y);
                ctx.moveTo(x + 15, y);
                ctx.lineTo(x + 30, y);
                ctx.strokeStyle = selected ? '#ff5722' : '#000000';
                ctx.lineWidth = selected ? 2 : 1;
                ctx.stroke();

                // رسم رمز الديود
                ctx.beginPath();
                // المثلث
                ctx.moveTo(x - 15, y - 10);
                ctx.lineTo(x - 15, y + 10);
                ctx.lineTo(x + 15, y);
                ctx.closePath();
                // خط الكاثود
                ctx.moveTo(x + 15, y - 10);
                ctx.lineTo(x + 15, y + 10);

                ctx.stroke();

                // تلوين المثلث حسب حالة الإضاءة
                if (brightness > 0) {
                    // تحويل اللون إلى RGBA مع شفافية حسب شدة الإضاءة
                    const r = parseInt(ledColor.slice(1, 3), 16);
                    const g = parseInt(ledColor.slice(3, 5), 16);
                    const b = parseInt(ledColor.slice(5, 7), 16);
                    ctx.fillStyle = `rgba(${r}, ${g}, ${b}, ${0.2 + brightness * 0.8})`;
                } else if (selected) {
                    ctx.fillStyle = 'rgba(255, 87, 34, 0.2)';
                } else {
                    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
                }
                ctx.fill();

                // رسم أشعة الضوء إذا كان مضيئاً
                if (brightness > 0) {
                    ctx.beginPath();
                    // الشعاع الأول
                    ctx.moveTo(x + 15, y - 10);
                    ctx.lineTo(x + 25, y - 20);
                    // الشعاع الثاني
                    ctx.moveTo(x + 15, y);
                    ctx.lineTo(x + 30, y);
                    // الشعاع الثالث
                    ctx.moveTo(x + 15, y + 10);
                    ctx.lineTo(x + 25, y + 20);

                    ctx.strokeStyle = ledColor;
                    ctx.globalAlpha = brightness;
                    ctx.stroke();
                    ctx.globalAlpha = 1;
                }

                // رسم قيمة جهد الانحياز الأمامي
                if (selected) {
                    ctx.fillStyle = '#000000';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`Vf=${component.properties.forward_voltage}V`, x, y - 15);
                    ctx.fillText(component.properties.color, x, y - 30);
                }

                // استعادة حالة الكانفاس
                ctx.restore();
            }
        },

        // أدوات القياس
        voltmeter: {
            name: 'فولتميتر',
            category: 'instruments',
            terminals: ['positive', 'negative'],
            terminalPositions: function(x, y, rotation) {
                const angle = (rotation || 0) * Math.PI / 180;
                const cos = Math.cos(angle);
                const sin = Math.sin(angle);
                return {
                    positive: { x: x - 30 * cos, y: y - 30 * sin },
                    negative: { x: x + 30 * cos, y: y + 30 * sin }
                };
            },
            properties: {
                range: { type: 'select', options: ['2V', '5V', '10V', '50V', '100V'], default: '10V', label: 'نطاق القياس' },
                internal_resistance: { type: 'number', default: 1e6, min: 1e5, max: 1e9, step: 1e5, unit: 'Ω', label: 'المقاومة الداخلية' }
            },
            function: 'يقيس فرق الجهد بين نقطتين في الدائرة. يوصل بشكل متوازي مع العنصر المراد قياس الجهد عبره.',
            simulate: function(component, circuit, time) {
                // الحصول على الجهود عند الأطراف
                const vp = circuit.getNodeVoltage(component, 'positive') || 0;
                const vn = circuit.getNodeVoltage(component, 'negative') || 0;

                // حساب فرق الجهد
                const voltage = vp - vn;

                // حساب التيار (ضئيل جداً بسبب المقاومة الداخلية العالية)
                const internal_resistance = component.properties.internal_resistance;
                const current = voltage / internal_resistance;

                // حساب القدرة المستهلكة
                const power = voltage * current;

                return {
                    voltage: voltage,
                    current: current,
                    power: power,
                    reading: voltage
                };
            },
            render: function(ctx, component, selected) {
                const x = component.x;
                const y = component.y;
                const rotation = component.rotation || 0;

                // الحصول على قراءة الفولتميتر
                let reading = 0;
                if (component._simulationResult && component._simulationResult.reading !== undefined) {
                    reading = component._simulationResult.reading;
                }

                // حفظ حالة الكانفاس
                ctx.save();

                // تطبيق التدوير
                ctx.translate(x, y);
                ctx.rotate(rotation * Math.PI / 180);
                ctx.translate(-x, -y);

                // رسم الدائرة
                ctx.beginPath();
                ctx.arc(x, y, 20, 0, 2 * Math.PI);
                ctx.strokeStyle = selected ? '#ff5722' : '#000000';
                ctx.lineWidth = selected ? 2 : 1;
                ctx.stroke();

                // رسم الأطراف
                ctx.beginPath();
                ctx.moveTo(x - 30, y);
                ctx.lineTo(x - 20, y);
                ctx.moveTo(x + 20, y);
                ctx.lineTo(x + 30, y);
                ctx.stroke();

                // رسم حرف V للفولتميتر
                ctx.beginPath();
                ctx.moveTo(x - 10, y - 10);
                ctx.lineTo(x, y + 10);
                ctx.lineTo(x + 10, y - 10);
                ctx.stroke();

                // رسم قراءة الفولتميتر
                ctx.fillStyle = '#000000';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`${reading.toFixed(2)}V`, x, y + 5);

                // استعادة حالة الكانفاس
                ctx.restore();
            }
        },
        ammeter: {
            name: 'أميتر',
            category: 'instruments',
            terminals: ['input', 'output'],
            terminalPositions: function(x, y, rotation) {
                const angle = (rotation || 0) * Math.PI / 180;
                const cos = Math.cos(angle);
                const sin = Math.sin(angle);
                return {
                    input: { x: x - 30 * cos, y: y - 30 * sin },
                    output: { x: x + 30 * cos, y: y + 30 * sin }
                };
            },
            properties: {
                range: { type: 'select', options: ['1mA', '10mA', '100mA', '1A', '10A'], default: '100mA', label: 'نطاق القياس' },
                internal_resistance: { type: 'number', default: 0.1, min: 0.01, max: 10, step: 0.01, unit: 'Ω', label: 'المقاومة الداخلية' }
            },
            function: 'يقيس التيار المار في فرع من الدائرة. يوصل بشكل متسلسل مع العنصر المراد قياس التيار المار فيه.',
            simulate: function(component, circuit, time) {
                // الحصول على التيار المار في الأميتر
                const current = circuit.getCurrent(component) || 0;

                // حساب هبوط الجهد عبر الأميتر (بسبب المقاومة الداخلية)
                const internal_resistance = component.properties.internal_resistance;
                const voltage = current * internal_resistance;

                // حساب القدرة المستهلكة
                const power = voltage * current;

                return {
                    voltage: voltage,
                    current: current,
                    power: power,
                    reading: current
                };
            },
            render: function(ctx, component, selected) {
                const x = component.x;
                const y = component.y;
                const rotation = component.rotation || 0;

                // الحصول على قراءة الأميتر
                let reading = 0;
                if (component._simulationResult && component._simulationResult.reading !== undefined) {
                    reading = component._simulationResult.reading;
                }

                // حفظ حالة الكانفاس
                ctx.save();

                // تطبيق التدوير
                ctx.translate(x, y);
                ctx.rotate(rotation * Math.PI / 180);
                ctx.translate(-x, -y);

                // رسم الدائرة
                ctx.beginPath();
                ctx.arc(x, y, 20, 0, 2 * Math.PI);
                ctx.strokeStyle = selected ? '#ff5722' : '#000000';
                ctx.lineWidth = selected ? 2 : 1;
                ctx.stroke();

                // رسم الأطراف
                ctx.beginPath();
                ctx.moveTo(x - 30, y);
                ctx.lineTo(x - 20, y);
                ctx.moveTo(x + 20, y);
                ctx.lineTo(x + 30, y);
                ctx.stroke();

                // رسم حرف A للأميتر
                ctx.beginPath();
                ctx.moveTo(x - 10, y + 10);
                ctx.lineTo(x, y - 10);
                ctx.lineTo(x + 10, y + 10);
                ctx.moveTo(x - 7, y + 3);
                ctx.lineTo(x + 7, y + 3);
                ctx.stroke();

                // رسم قراءة الأميتر
                ctx.fillStyle = '#000000';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';

                // تنسيق القراءة حسب القيمة
                let readingText = '';
                if (Math.abs(reading) >= 1) {
                    readingText = `${reading.toFixed(2)}A`;
                } else if (Math.abs(reading) >= 0.001) {
                    readingText = `${(reading * 1000).toFixed(2)}mA`;
                } else {
                    readingText = `${(reading * 1000000).toFixed(2)}μA`;
                }

                ctx.fillText(readingText, x, y + 5);

                // استعادة حالة الكانفاس
                ctx.restore();
            }
        },
        oscilloscope: {
            name: 'راسم إشارة',
            category: 'instruments',
            terminals: ['ch1_pos', 'ch1_neg', 'ch2_pos', 'ch2_neg'],
            terminalPositions: function(x, y, rotation) {
                const angle = (rotation || 0) * Math.PI / 180;
                const cos = Math.cos(angle);
                const sin = Math.sin(angle);
                return {
                    ch1_pos: { x: x - 30 * cos - 10 * sin, y: y - 30 * sin + 10 * cos },
                    ch1_neg: { x: x - 30 * cos + 10 * sin, y: y - 30 * sin - 10 * cos },
                    ch2_pos: { x: x + 30 * cos - 10 * sin, y: y + 30 * sin + 10 * cos },
                    ch2_neg: { x: x + 30 * cos + 10 * sin, y: y + 30 * sin - 10 * cos }
                };
            },
            properties: {
                timebase: { type: 'select', options: ['1us', '10us', '100us', '1ms', '10ms', '100ms'], default: '1ms', label: 'قاعدة الزمن' },
                ch1_scale: { type: 'select', options: ['10mV', '100mV', '1V', '10V'], default: '1V', label: 'مقياس القناة 1' },
                ch2_scale: { type: 'select', options: ['10mV', '100mV', '1V', '10V'], default: '1V', label: 'مقياس القناة 2' }
            },
            function: 'يعرض الإشارات الكهربائية على شكل رسم بياني مع الزمن. يستخدم لتحليل الإشارات وقياس خصائصها.',
            simulate: function(component, circuit, time) {
                // الحصول على الجهود عند أطراف القناتين
                const v1p = circuit.getNodeVoltage(component, 'ch1_pos') || 0;
                const v1n = circuit.getNodeVoltage(component, 'ch1_neg') || 0;
                const v2p = circuit.getNodeVoltage(component, 'ch2_pos') || 0;
                const v2n = circuit.getNodeVoltage(component, 'ch2_neg') || 0;

                // حساب فرق الجهد لكل قناة
                const ch1_voltage = v1p - v1n;
                const ch2_voltage = v2p - v2n;

                // تخزين القراءات في مصفوفة التاريخ
                if (!component._history) {
                    component._history = {
                        ch1: [],
                        ch2: [],
                        times: []
                    };
                }

                // إضافة القراءة الحالية إلى التاريخ
                component._history.ch1.push(ch1_voltage);
                component._history.ch2.push(ch2_voltage);
                component._history.times.push(time);

                // الاحتفاظ بآخر 1000 قراءة فقط
                if (component._history.ch1.length > 1000) {
                    component._history.ch1.shift();
                    component._history.ch2.shift();
                    component._history.times.shift();
                }

                return {
                    ch1_voltage: ch1_voltage,
                    ch2_voltage: ch2_voltage,
                    history: component._history
                };
            },
            render: function(ctx, component, selected) {
                const x = component.x;
                const y = component.y;
                const rotation = component.rotation || 0;

                // حفظ حالة الكانفاس
                ctx.save();

                // تطبيق التدوير
                ctx.translate(x, y);
                ctx.rotate(rotation * Math.PI / 180);
                ctx.translate(-x, -y);

                // رسم المستطيل الخارجي
                ctx.beginPath();
                ctx.rect(x - 25, y - 20, 50, 40);
                ctx.strokeStyle = selected ? '#ff5722' : '#000000';
                ctx.lineWidth = selected ? 2 : 1;
                ctx.stroke();

                // رسم شاشة العرض
                ctx.beginPath();
                ctx.rect(x - 20, y - 15, 40, 30);
                ctx.fillStyle = '#222222';
                ctx.fill();

                // رسم الشبكة
                ctx.beginPath();
                ctx.strokeStyle = '#444444';
                ctx.lineWidth = 0.5;

                // خطوط أفقية
                for (let i = -10; i <= 10; i += 5) {
                    ctx.moveTo(x - 20, y + i);
                    ctx.lineTo(x + 20, y + i);
                }

                // خطوط رأسية
                for (let i = -20; i <= 20; i += 5) {
                    ctx.moveTo(x + i, y - 15);
                    ctx.lineTo(x + i, y + 15);
                }

                ctx.stroke();

                // رسم الإشارات إذا كانت متوفرة
                if (component._simulationResult && component._simulationResult.history) {
                    const history = component._simulationResult.history;

                    if (history.ch1.length > 1) {
                        // رسم إشارة القناة 1
                        ctx.beginPath();
                        ctx.strokeStyle = '#00ff00';
                        ctx.lineWidth = 1;

                        const startIdx = Math.max(0, history.ch1.length - 40);

                        for (let i = startIdx; i < history.ch1.length; i++) {
                            const xPos = x - 20 + (i - startIdx) * (40 / 40);
                            const yPos = y - history.ch1[i] * 5;

                            if (i === startIdx) {
                                ctx.moveTo(xPos, yPos);
                            } else {
                                ctx.lineTo(xPos, yPos);
                            }
                        }

                        ctx.stroke();

                        // رسم إشارة القناة 2
                        if (history.ch2.length > 1) {
                            ctx.beginPath();
                            ctx.strokeStyle = '#ffff00';
                            ctx.lineWidth = 1;

                            for (let i = startIdx; i < history.ch2.length; i++) {
                                const xPos = x - 20 + (i - startIdx) * (40 / 40);
                                const yPos = y - history.ch2[i] * 5;

                                if (i === startIdx) {
                                    ctx.moveTo(xPos, yPos);
                                } else {
                                    ctx.lineTo(xPos, yPos);
                                }
                            }

                            ctx.stroke();
                        }
                    }
                }

                // رسم الأطراف
                ctx.beginPath();
                ctx.strokeStyle = selected ? '#ff5722' : '#000000';
                ctx.lineWidth = selected ? 2 : 1;

                // أطراف القناة 1
                ctx.moveTo(x - 30, y - 10);
                ctx.lineTo(x - 25, y - 10);
                ctx.moveTo(x - 30, y + 10);
                ctx.lineTo(x - 25, y + 10);

                // أطراف القناة 2
                ctx.moveTo(x + 25, y - 10);
                ctx.lineTo(x + 30, y - 10);
                ctx.moveTo(x + 25, y + 10);
                ctx.lineTo(x + 30, y + 10);

                ctx.stroke();

                // رسم تسميات القنوات
                if (selected) {
                    ctx.fillStyle = '#ffffff';
                    ctx.font = '8px Arial';
                    ctx.textAlign = 'left';
                    ctx.fillText('CH1', x - 18, y - 8);
                    ctx.fillText('CH2', x - 18, y + 8);
                }

                // استعادة حالة الكانفاس
                ctx.restore();
            }
        }
    };

    // تهيئة الرسم
    function initCanvas() {
        // ضبط حجم الكانفاس ليملأ المساحة المتاحة
        function resizeCanvas() {
            const rect = circuitCanvas.getBoundingClientRect();
            canvas.width = rect.width;
            canvas.height = rect.height;

            if (waveformCanvas) {
                const waveformRect = waveformCanvas.parentElement.getBoundingClientRect();
                waveformCanvas.width = waveformRect.width;
                waveformCanvas.height = waveformRect.height;
            }

            drawCircuit();
        }

        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();
    }

    // رسم الدائرة
    function drawCircuit() {
        // مسح الكانفاس
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // تطبيق التكبير/التصغير والإزاحة
        ctx.save();
        ctx.translate(simulationState.offset.x * simulationState.zoom, simulationState.offset.y * simulationState.zoom);
        ctx.scale(simulationState.zoom, simulationState.zoom);

        // رسم الأسلاك
        drawWires();

        // رسم العناصر
        drawComponents();

        // رسم السلك المؤقت أثناء الرسم
        if (simulationState.activeTool === 'wire' && simulationState.wireStart && simulationState.mousePos) {
            ctx.beginPath();
            ctx.moveTo(simulationState.wireStart.x, simulationState.wireStart.y);
            ctx.lineTo(simulationState.mousePos.x, simulationState.mousePos.y);
            ctx.strokeStyle = '#0066cc';
            ctx.lineWidth = 2;
            ctx.stroke();
        }

        // رسم مؤشر الدوران للعنصر المحدد
        if (simulationState.selectedComponent) {
            drawRotationIndicator(simulationState.selectedComponent);
        }

        // استعادة حالة الكانفاس
        ctx.restore();
    }

    // رسم العناصر
    function drawComponents() {
        simulationState.components.forEach(component => {
            const def = componentDefinitions[component.type];
            if (def && def.render) {
                const selected = component === simulationState.selectedComponent;
                def.render(ctx, component, selected);
            }
        });
    }

    // رسم الأسلاك
    function drawWires() {
        simulationState.wires.forEach(wire => {
            ctx.beginPath();
            ctx.moveTo(wire.start.x, wire.start.y);
            ctx.lineTo(wire.end.x, wire.end.y);
            ctx.strokeStyle = '#000000';
            ctx.lineWidth = 2;
            ctx.stroke();
        });
    }

    // رسم مؤشر الدوران للعنصر المحدد
    function drawRotationIndicator(component) {
        const x = component.x;
        const y = component.y;
        const rotation = component.rotation || 0;

        // حفظ حالة الكانفاس
        ctx.save();

        // رسم دائرة حول العنصر
        ctx.beginPath();
        ctx.arc(x, y, 30, 0, 2 * Math.PI);
        ctx.strokeStyle = 'rgba(255, 87, 34, 0.3)';
        ctx.lineWidth = 1;
        ctx.stroke();

        // رسم خط يشير إلى زاوية الدوران
        ctx.beginPath();
        ctx.moveTo(x, y);
        ctx.lineTo(x + 30 * Math.cos(rotation * Math.PI / 180), y + 30 * Math.sin(rotation * Math.PI / 180));
        ctx.strokeStyle = '#ff5722';
        ctx.lineWidth = 2;
        ctx.stroke();

        // رسم نقطة في نهاية الخط
        ctx.beginPath();
        ctx.arc(x + 30 * Math.cos(rotation * Math.PI / 180), y + 30 * Math.sin(rotation * Math.PI / 180), 3, 0, 2 * Math.PI);
        ctx.fillStyle = '#ff5722';
        ctx.fill();

        // رسم قيمة الزاوية
        ctx.fillStyle = '#000000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`${rotation}°`, x, y - 35);

        // استعادة حالة الكانفاس
        ctx.restore();
    }

    // تحديث حالة المحاكاة
    function updateSimulation(timestamp) {
        if (!simulationState.running || simulationState.paused) return;

        const deltaTime = timestamp - simulationState.lastTimestamp;
        simulationState.lastTimestamp = timestamp;

        // حساب قيم الجهود والتيارات في الدائرة

        // تحديث النتائج
        updateResults();

        // تحديث الرسم
        drawCircuit();

        // طلب الإطار التالي
        requestAnimationFrame(updateSimulation);
    }

    // تحديث نتائج المحاكاة
    function updateResults() {
        const resultsDisplay = document.getElementById('results-display');
        if (!resultsDisplay) return;

        // عرض النتائج
        let resultsHTML = '';

        if (Object.keys(simulationState.results).length === 0) {
            resultsHTML = '<p class="no-results">لم يتم العثور على نتائج للمحاكاة. تأكد من تصميم الدائرة بشكل صحيح.</p>';
        } else {
            resultsHTML = '<div class="results-table"><table><thead><tr><th>العنصر</th><th>القيمة</th><th>الوحدة</th></tr></thead><tbody>';

            for (const [key, value] of Object.entries(simulationState.results)) {
                resultsHTML += `<tr><td>${value.label}</td><td>${value.value.toFixed(3)}</td><td>${value.unit}</td></tr>`;
            }

            resultsHTML += '</tbody></table></div>';
        }

        resultsDisplay.innerHTML = resultsHTML;
    }

    // تهيئة أحداث المستخدم
    function initEvents() {
        // أحداث أزرار التحكم في المحاكاة
        document.getElementById('run-simulation').addEventListener('click', startSimulation);
        document.getElementById('pause-simulation').addEventListener('click', pauseSimulation);
        document.getElementById('stop-simulation').addEventListener('click', stopSimulation);
        document.getElementById('reset-simulation').addEventListener('click', resetSimulation);
        document.getElementById('save-circuit').addEventListener('click', saveCircuit);
        document.getElementById('load-circuit').addEventListener('click', loadCircuit);

        // أحداث أدوات الرسم
        document.querySelectorAll('.tool-btn').forEach(button => {
            button.addEventListener('click', function() {
                const tool = this.id.replace('-tool', '');
                setActiveTool(tool);
            });
        });

        // أحداث أدوات بناء الدائرة
        document.querySelectorAll('.building-tool-btn').forEach(button => {
            button.addEventListener('click', function() {
                const buildTool = this.id.replace('build-', '');
                setActiveBuildingTool(buildTool);
            });
        });

        // أحداث تبديل الشبكة والالتصاق
        const gridToggleBtn = document.getElementById('grid-toggle');
        const snapToggleBtn = document.getElementById('snap-toggle');
        if (gridToggleBtn) gridToggleBtn.addEventListener('click', toggleGrid);
        if (snapToggleBtn) snapToggleBtn.addEventListener('click', toggleSnap);

        // أحداث تصفية العناصر
        document.querySelectorAll('.component-category').forEach(button => {
            button.addEventListener('click', function() {
                filterComponents(this.dataset.category);
            });
        });

        // أحداث البحث عن العناصر
        componentSearch.addEventListener('input', searchComponents);

        // أحداث السحب والإفلات للعناصر
        initDragAndDrop();

        // أحداث الكانفاس
        initCanvasEvents();

        // أحداث قائمة السياق
        initContextMenu();

        // أحداث أزرار التدوير
        const rotate90Btn = document.getElementById('rotate-90');
        const rotate180Btn = document.getElementById('rotate-180');
        const rotate270Btn = document.getElementById('rotate-270');
        const rotateCustomBtn = document.getElementById('rotate-custom');

        if (rotate90Btn) rotate90Btn.addEventListener('click', function() {
            rotateSelectedComponent(90);
        });

        if (rotate180Btn) rotate180Btn.addEventListener('click', function() {
            rotateSelectedComponent(180);
        });

        if (rotate270Btn) rotate270Btn.addEventListener('click', function() {
            rotateSelectedComponent(270);
        });

        if (rotateCustomBtn) rotateCustomBtn.addEventListener('click', showRotationDialog);

        // أحداث مربع حوار التدوير المخصص
        const applyRotationBtn = document.getElementById('apply-rotation');
        const cancelRotationBtn = document.getElementById('cancel-rotation');

        if (applyRotationBtn) applyRotationBtn.addEventListener('click', applyCustomRotation);
        if (cancelRotationBtn) cancelRotationBtn.addEventListener('click', hideRotationDialog);

        // أحداث علامات التبويب في لوحة التحليل
        document.querySelectorAll('.analysis-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                const tabId = this.dataset.tab;
                switchAnalysisTab(tabId);
            });
        });

        // أحداث لوحة الخصائص
        document.getElementById('apply-properties').addEventListener('click', applyProperties);
        document.getElementById('reset-properties').addEventListener('click', resetProperties);
        document.getElementById('close-properties').addEventListener('click', closeProperties);

        // أحداث تحليل الإشارة
        const addWaveformBtn = document.getElementById('add-waveform');
        const clearWaveformsBtn = document.getElementById('clear-waveforms');
        if (addWaveformBtn) addWaveformBtn.addEventListener('click', addWaveform);
        if (clearWaveformsBtn) clearWaveformsBtn.addEventListener('click', clearWaveforms);

        // أحداث لوحة المفاتيح
        document.addEventListener('keydown', handleKeyDown);
    }

    // بدء المحاكاة
    function startSimulation() {
        if (simulationState.running && !simulationState.paused) return;

        if (!validateCircuit()) {
            showMessage('خطأ في الدائرة', 'يرجى التأكد من توصيل جميع العناصر بشكل صحيح قبل بدء المحاكاة.', 'error');
            return;
        }

        if (simulationState.paused) {
            simulationState.paused = false;
        } else {
            simulationState.running = true;
            simulationState.lastTimestamp = performance.now();

            // تهيئة النتائج
            simulationState.results = {};

            // تحديث حالة الأزرار
            document.getElementById('run-simulation').disabled = true;
            document.getElementById('pause-simulation').disabled = false;
            document.getElementById('stop-simulation').disabled = false;

            // بدء حلقة المحاكاة
            requestAnimationFrame(updateSimulation);
        }

        showMessage('بدء المحاكاة', 'تم بدء المحاكاة بنجاح.', 'success');
    }

    // إيقاف المحاكاة مؤقتًا
    function pauseSimulation() {
        if (!simulationState.running || simulationState.paused) return;

        simulationState.paused = true;

        // تحديث حالة الأزرار
        document.getElementById('run-simulation').disabled = false;
        document.getElementById('pause-simulation').disabled = true;

        showMessage('إيقاف مؤقت', 'تم إيقاف المحاكاة مؤقتًا.', 'info');
    }

    // إيقاف المحاكاة
    function stopSimulation() {
        if (!simulationState.running) return;

        simulationState.running = false;
        simulationState.paused = false;

        // تحديث حالة الأزرار
        document.getElementById('run-simulation').disabled = false;
        document.getElementById('pause-simulation').disabled = true;
        document.getElementById('stop-simulation').disabled = true;

        showMessage('إيقاف المحاكاة', 'تم إيقاف المحاكاة.', 'info');
    }

    // إعادة ضبط المحاكاة
    function resetSimulation() {
        stopSimulation();

        // إعادة ضبط النتائج
        simulationState.results = {};
        updateResults();

        // إعادة رسم الدائرة
        drawCircuit();

        showMessage('إعادة ضبط', 'تم إعادة ضبط المحاكاة.', 'info');
    }

    // التحقق من صحة الدائرة
    function validateCircuit() {
        // التحقق من وجود عناصر في الدائرة
        if (simulationState.components.length === 0) {
            return false;
        }

        // التحقق من توصيل جميع العناصر
        // (هذا مجرد تحقق بسيط، يمكن تحسينه لاحقًا)

        return true;
    }

    // تعيين الأداة النشطة
    function setActiveTool(tool) {
        simulationState.activeTool = tool;

        // تحديث حالة الأزرار
        document.querySelectorAll('.tool-btn').forEach(button => {
            button.classList.remove('active');
        });

        document.getElementById(`${tool}-tool`).classList.add('active');

        // تغيير مؤشر الماوس حسب الأداة
        switch (tool) {
            case 'select':
                circuitCanvas.style.cursor = 'default';
                break;
            case 'wire':
                circuitCanvas.style.cursor = 'crosshair';
                break;
            case 'node':
                circuitCanvas.style.cursor = 'cell';
                break;
            case 'text':
                circuitCanvas.style.cursor = 'text';
                break;
            case 'delete':
                circuitCanvas.style.cursor = 'no-drop';
                break;
            case 'rotate':
                circuitCanvas.style.cursor = 'cell';
                break;
            case 'copy':
                circuitCanvas.style.cursor = 'copy';
                break;
            case 'paste':
                circuitCanvas.style.cursor = 'alias';
                break;
            default:
                circuitCanvas.style.cursor = 'default';
        }

        // إعادة رسم الدائرة
        drawCircuit();
    }

    // تعيين أداة البناء النشطة
    function setActiveBuildingTool(tool) {
        simulationState.activeBuildingTool = tool;

        // تحديث حالة الأزرار
        document.querySelectorAll('.building-tool-btn').forEach(button => {
            button.classList.remove('active');
        });

        document.getElementById(`build-${tool}`).classList.add('active');

        // تغيير الأداة النشطة حسب أداة البناء
        switch (tool) {
            case 'wire':
                setActiveTool('wire');
                break;
            case 'junction':
                setActiveTool('node');
                break;
            case 'ground':
                // إضافة أرضي عند النقر
                setActiveTool('select');
                addComponent('ground', simulationState.mousePos.x, simulationState.mousePos.y);
                break;
            case 'label':
                setActiveTool('text');
                break;
            case 'probe':
                // إضافة نقطة قياس عند النقر
                setActiveTool('select');
                addComponent('probe', simulationState.mousePos.x, simulationState.mousePos.y);
                break;
        }
    }

    // تبديل عرض الشبكة
    function toggleGrid() {
        const gridBackground = document.getElementById('grid-background');
        const gridToggleBtn = document.getElementById('grid-toggle');

        if (gridBackground.style.display === 'none') {
            gridBackground.style.display = 'block';
            gridToggleBtn.classList.add('active');
        } else {
            gridBackground.style.display = 'none';
            gridToggleBtn.classList.remove('active');
        }
    }

    // تبديل الالتصاق بالشبكة
    function toggleSnap() {
        const snapToggleBtn = document.getElementById('snap-toggle');

        simulationState.grid.snap = !simulationState.grid.snap;

        if (simulationState.grid.snap) {
            snapToggleBtn.classList.add('active');
        } else {
            snapToggleBtn.classList.remove('active');
        }
    }

    // التعامل مع أداة السلك
    function handleWireTool(x, y) {
        // إذا كانت هذه هي النقطة الأولى للسلك
        if (!simulationState.wireStart) {
            simulationState.wireStart = { x, y };
            return;
        }

        // إنشاء سلك جديد
        const wire = {
            id: `wire_${Date.now()}`,
            type: 'wire',
            start: { x: simulationState.wireStart.x, y: simulationState.wireStart.y },
            end: { x, y }
        };

        // إضافة السلك إلى القائمة
        simulationState.wires.push(wire);

        // إعادة تعيين نقطة البداية للسلك التالي
        simulationState.wireStart = { x, y };

        // إعادة رسم الدائرة
        drawCircuit();
    }

    // التعامل مع تحريك الماوس أثناء استخدام أداة السلك
    function handleWireToolMove(x, y) {
        if (simulationState.wireStart) {
            // تحديث الرسم لعرض السلك المؤقت
            simulationState.tempWire = {
                start: { x: simulationState.wireStart.x, y: simulationState.wireStart.y },
                end: { x, y }
            };
        }
    }

    // التعامل مع أداة نقطة التوصيل
    function handleNodeTool(x, y) {
        // إضافة نقطة توصيل جديدة
        const node = {
            id: `node_${Date.now()}`,
            type: 'node',
            x,
            y,
            properties: {}
        };

        // إضافة النقطة إلى القائمة
        simulationState.components.push(node);

        // إعادة رسم الدائرة
        drawCircuit();
    }

    // التعامل مع أداة النص
    function handleTextTool(x, y) {
        // طلب النص من المستخدم
        const text = prompt('أدخل النص:');

        if (text) {
            // إضافة نص جديد
            const textComponent = {
                id: `text_${Date.now()}`,
                type: 'text',
                x,
                y,
                properties: {
                    text,
                    fontSize: 14,
                    color: '#000000'
                }
            };

            // إضافة النص إلى القائمة
            simulationState.components.push(textComponent);

            // إعادة رسم الدائرة
            drawCircuit();
        }
    }

    // بدء سحب العنصر
    function startDragging(x, y) {
        // البحث عن العنصر عند موضع النقر
        const component = findComponentAt(x, y);

        if (component) {
            // تحديد العنصر
            simulationState.selectedComponent = component;

            // تعيين نقطة بداية السحب
            simulationState.dragStart = {
                x,
                y,
                componentX: component.x,
                componentY: component.y
            };

            // عرض خصائص العنصر
            showComponentProperties(component);
        } else {
            // إلغاء تحديد العنصر
            simulationState.selectedComponent = null;

            // إخفاء خصائص العنصر
            showComponentProperties(null);
        }

        // إعادة رسم الدائرة
        drawCircuit();
    }

    // إيقاف سحب العنصر
    function stopDragging(x, y) {
        if (simulationState.dragStart && simulationState.selectedComponent) {
            // حساب الإزاحة
            const dx = x - simulationState.dragStart.x;
            const dy = y - simulationState.dragStart.y;

            // تحديث موضع العنصر
            simulationState.selectedComponent.x = simulationState.dragStart.componentX + dx;
            simulationState.selectedComponent.y = simulationState.dragStart.componentY + dy;

            // إعادة تعيين نقطة بداية السحب
            simulationState.dragStart = null;

            // إعادة رسم الدائرة
            drawCircuit();
        }
    }

    // البحث عن عنصر عند موضع معين
    function findComponentAt(x, y) {
        // البحث من النهاية للبداية (العناصر المرسومة في الأعلى)
        for (let i = simulationState.components.length - 1; i >= 0; i--) {
            const component = simulationState.components[i];

            // التحقق مما إذا كانت النقطة داخل العنصر
            const distance = Math.sqrt(Math.pow(component.x - x, 2) + Math.pow(component.y - y, 2));

            if (distance < 20) {
                return component;
            }
        }

        return null;
    }

    // عرض قائمة السياق
    function showContextMenu(x, y, component) {
        const contextMenu = document.getElementById('context-menu');

        // تحديد موضع القائمة
        contextMenu.style.left = `${x}px`;
        contextMenu.style.top = `${y}px`;

        // إظهار القائمة
        contextMenu.classList.add('active');

        // تحديث حالة عناصر القائمة
        document.getElementById('context-paste').style.display = simulationState.clipboard ? 'flex' : 'none';
    }

    // إخفاء قائمة السياق
    function hideContextMenu() {
        const contextMenu = document.getElementById('context-menu');
        contextMenu.classList.remove('active');
    }

    // نسخ العنصر المحدد
    function copySelectedComponent() {
        if (simulationState.selectedComponent) {
            // نسخ العنصر إلى الحافظة
            simulationState.clipboard = JSON.parse(JSON.stringify(simulationState.selectedComponent));

            showMessage('نسخ', 'تم نسخ العنصر إلى الحافظة.', 'success');
        }
    }

    // قص العنصر المحدد
    function cutSelectedComponent() {
        if (simulationState.selectedComponent) {
            // نسخ العنصر إلى الحافظة
            simulationState.clipboard = JSON.parse(JSON.stringify(simulationState.selectedComponent));

            // حذف العنصر
            deleteSelectedComponent();

            showMessage('قص', 'تم قص العنصر إلى الحافظة.', 'success');
        }
    }

    // لصق العنصر
    function pasteComponent(x, y) {
        if (simulationState.clipboard) {
            // إنشاء نسخة من العنصر
            const component = JSON.parse(JSON.stringify(simulationState.clipboard));

            // تعديل المعرف
            component.id = `${component.type}_${Date.now()}`;

            // تعديل الموضع
            component.x = x;
            component.y = y;

            // إضافة العنصر إلى القائمة
            simulationState.components.push(component);

            // تحديد العنصر الجديد
            simulationState.selectedComponent = component;

            // عرض خصائص العنصر
            showComponentProperties(component);

            // إعادة رسم الدائرة
            drawCircuit();

            showMessage('لصق', 'تم لصق العنصر بنجاح.', 'success');
        }
    }

    // تدوير العنصر المحدد
    function rotateSelectedComponent(angle = 90) {
        if (simulationState.selectedComponent) {
            // تدوير العنصر بالزاوية المحددة
            simulationState.selectedComponent.rotation = (simulationState.selectedComponent.rotation || 0) + angle;

            // إعادة ضبط الزاوية إذا تجاوزت 360 درجة
            if (simulationState.selectedComponent.rotation >= 360) {
                simulationState.selectedComponent.rotation = simulationState.selectedComponent.rotation % 360;
            }

            // تحديث موضع الأطراف
            updateTerminalPositions(simulationState.selectedComponent);

            // تحديث الأسلاك المتصلة بالعنصر
            updateConnectedWires(simulationState.selectedComponent);

            // إعادة رسم الدائرة
            drawCircuit();

            showMessage('تدوير', `تم تدوير العنصر بزاوية ${angle} درجة بنجاح.`, 'success');
        } else {
            showMessage('تدوير', 'يرجى تحديد عنصر أولاً.', 'warning');
        }
    }

    // عرض مربع حوار التدوير المخصص
    function showRotationDialog() {
        if (!simulationState.selectedComponent) {
            showMessage('تدوير', 'يرجى تحديد عنصر أولاً.', 'warning');
            return;
        }

        const rotationDialog = document.getElementById('rotation-dialog');
        const rotationAngleInput = document.getElementById('rotation-angle');

        // تعيين القيمة الحالية للزاوية
        const currentRotation = simulationState.selectedComponent.rotation || 0;
        rotationAngleInput.value = 45; // قيمة افتراضية

        // عرض مربع الحوار
        rotationDialog.classList.add('active');

        // تركيز حقل الإدخال
        rotationAngleInput.focus();
        rotationAngleInput.select();
    }

    // إخفاء مربع حوار التدوير المخصص
    function hideRotationDialog() {
        const rotationDialog = document.getElementById('rotation-dialog');
        rotationDialog.classList.remove('active');
    }

    // تطبيق التدوير المخصص
    function applyCustomRotation() {
        const rotationAngleInput = document.getElementById('rotation-angle');
        const angle = parseInt(rotationAngleInput.value);

        if (isNaN(angle)) {
            showMessage('تدوير', 'يرجى إدخال قيمة صحيحة للزاوية.', 'error');
            return;
        }

        // تطبيق التدوير
        rotateSelectedComponent(angle);

        // إخفاء مربع الحوار
        hideRotationDialog();
    }

    // تحديث موضع أطراف العنصر
    function updateTerminalPositions(component) {
        if (!component || !componentDefinitions[component.type] || !componentDefinitions[component.type].terminalPositions) {
            return;
        }

        // الحصول على وظيفة حساب مواضع الأطراف
        const terminalPositionsFunc = componentDefinitions[component.type].terminalPositions;

        // حساب مواضع الأطراف الجديدة
        component.terminalPositions = terminalPositionsFunc(component.x, component.y, component.rotation);
    }

    // تحديث الأسلاك المتصلة بالعنصر
    function updateConnectedWires(component) {
        if (!component || !component.terminalPositions) {
            return;
        }

        // البحث عن الأسلاك المتصلة بالعنصر
        simulationState.wires.forEach(wire => {
            // التحقق من اتصال السلك بأحد أطراف العنصر
            let updated = false;

            // التحقق من اتصال بداية السلك بأحد أطراف العنصر
            if (wire.startComponent === component.id) {
                const terminal = wire.startTerminal;
                if (component.terminalPositions[terminal]) {
                    wire.start = {
                        x: component.terminalPositions[terminal].x,
                        y: component.terminalPositions[terminal].y
                    };
                    updated = true;
                }
            }

            // التحقق من اتصال نهاية السلك بأحد أطراف العنصر
            if (wire.endComponent === component.id) {
                const terminal = wire.endTerminal;
                if (component.terminalPositions[terminal]) {
                    wire.end = {
                        x: component.terminalPositions[terminal].x,
                        y: component.terminalPositions[terminal].y
                    };
                    updated = true;
                }
            }
        });
    }

    // قلب العنصر المحدد أفقياً
    function flipSelectedComponentHorizontally() {
        if (simulationState.selectedComponent) {
            // قلب العنصر أفقياً
            simulationState.selectedComponent.flipX = !simulationState.selectedComponent.flipX;

            // إعادة رسم الدائرة
            drawCircuit();

            showMessage('قلب أفقي', 'تم قلب العنصر أفقياً بنجاح.', 'success');
        }
    }

    // قلب العنصر المحدد رأسياً
    function flipSelectedComponentVertically() {
        if (simulationState.selectedComponent) {
            // قلب العنصر رأسياً
            simulationState.selectedComponent.flipY = !simulationState.selectedComponent.flipY;

            // إعادة رسم الدائرة
            drawCircuit();

            showMessage('قلب رأسي', 'تم قلب العنصر رأسياً بنجاح.', 'success');
        }
    }

    // حذف العنصر المحدد
    function deleteSelectedComponent() {
        if (simulationState.selectedComponent) {
            // حذف العنصر من القائمة
            const index = simulationState.components.indexOf(simulationState.selectedComponent);
            if (index !== -1) {
                simulationState.components.splice(index, 1);
            }

            // إلغاء تحديد العنصر
            simulationState.selectedComponent = null;

            // إخفاء خصائص العنصر
            showComponentProperties(null);

            // إعادة رسم الدائرة
            drawCircuit();

            showMessage('حذف', 'تم حذف العنصر بنجاح.', 'success');
        }
    }

    // التعامل مع أحداث لوحة المفاتيح
    function handleKeyDown(e) {
        // التحقق من عدم وجود عنصر نشط (مثل حقل نص)
        if (document.activeElement.tagName === 'INPUT' || document.activeElement.tagName === 'TEXTAREA') {
            return;
        }

        switch (e.key) {
            case 'Delete':
                // حذف العنصر المحدد
                deleteSelectedComponent();
                break;
            case 'Escape':
                // إلغاء تحديد العنصر
                simulationState.selectedComponent = null;
                showComponentProperties(null);
                drawCircuit();
                break;
            case 'r':
                // تدوير العنصر المحدد
                rotateSelectedComponent();
                break;
            case 'c':
                if (e.ctrlKey || e.metaKey) {
                    // نسخ العنصر المحدد
                    copySelectedComponent();
                    e.preventDefault();
                }
                break;
            case 'x':
                if (e.ctrlKey || e.metaKey) {
                    // قص العنصر المحدد
                    cutSelectedComponent();
                    e.preventDefault();
                }
                break;
            case 'v':
                if (e.ctrlKey || e.metaKey) {
                    // لصق العنصر
                    pasteComponent(simulationState.mousePos.x, simulationState.mousePos.y);
                    e.preventDefault();
                }
                break;
            case 'z':
                if (e.ctrlKey || e.metaKey) {
                    // التراجع عن آخر عملية
                    // (سيتم تنفيذ هذا لاحقاً)
                    e.preventDefault();
                }
                break;
            case 'y':
                if (e.ctrlKey || e.metaKey) {
                    // إعادة آخر عملية
                    // (سيتم تنفيذ هذا لاحقاً)
                    e.preventDefault();
                }
                break;
        }
    }

    // تصفية العناصر حسب الفئة
    function filterComponents(category) {
        // تحديث حالة الأزرار
        document.querySelectorAll('.component-category').forEach(button => {
            button.classList.remove('active');
        });

        document.querySelector(`.component-category[data-category="${category}"]`).classList.add('active');

        // تصفية العناصر
        const items = document.querySelectorAll('.component-item');
        items.forEach(item => {
            if (category === 'all' || item.dataset.category === category) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    }

    // البحث عن العناصر
    function searchComponents() {
        const searchText = componentSearch.value.trim().toLowerCase();
        const items = document.querySelectorAll('.component-item');

        items.forEach(item => {
            const componentName = item.querySelector('.component-name').textContent.toLowerCase();
            if (componentName.includes(searchText)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    }

    // تهيئة السحب والإفلات
    function initDragAndDrop() {
        const items = document.querySelectorAll('.component-item');

        items.forEach(item => {
            item.addEventListener('dragstart', function(e) {
                e.dataTransfer.setData('text/plain', this.dataset.component);
                e.dataTransfer.effectAllowed = 'copy';
            });
        });

        circuitCanvas.addEventListener('dragover', function(e) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'copy';
        });

        circuitCanvas.addEventListener('drop', function(e) {
            e.preventDefault();

            const componentType = e.dataTransfer.getData('text/plain');
            if (!componentType || !componentDefinitions[componentType]) return;

            // حساب موقع الإفلات بالنسبة للكانفاس
            const rect = circuitCanvas.getBoundingClientRect();
            const x = (e.clientX - rect.left) / simulationState.zoom - simulationState.offset.x;
            const y = (e.clientY - rect.top) / simulationState.zoom - simulationState.offset.y;

            // إضافة العنصر الجديد
            addComponent(componentType, x, y);
        });
    }

    // إضافة عنصر جديد
    function addComponent(type, x, y) {
        const def = componentDefinitions[type];
        if (!def) return;

        // إنشاء معرف فريد للعنصر
        const id = `${type}_${Date.now()}`;

        // إنشاء العنصر
        const component = {
            id,
            type,
            x,
            y,
            rotation: 0,
            properties: {}
        };

        // تعيين القيم الافتراضية للخصائص
        for (const [key, prop] of Object.entries(def.properties)) {
            component.properties[key] = prop.default;
        }

        // إضافة العنصر إلى القائمة
        simulationState.components.push(component);

        // تحديد العنصر الجديد
        simulationState.selectedComponent = component;

        // عرض خصائص العنصر
        showComponentProperties(component);

        // إعادة رسم الدائرة
        drawCircuit();

        showMessage('إضافة عنصر', `تم إضافة ${def.name} بنجاح.`, 'success');
    }

    // عرض خصائص العنصر
    function showComponentProperties(component) {
        if (!component) {
            noSelectionMessage.style.display = 'flex';
            componentProperties.style.display = 'none';
            return;
        }

        const def = componentDefinitions[component.type];
        if (!def) return;

        // إخفاء رسالة عدم التحديد وإظهار لوحة الخصائص
        noSelectionMessage.style.display = 'none';
        componentProperties.style.display = 'block';

        // تعيين عنوان العنصر
        componentTitle.textContent = def.name;

        // تعيين معرف العنصر
        document.getElementById('component-id').value = component.id;

        // إنشاء حقول الخصائص الخاصة بالعنصر
        let propertiesHTML = '';

        for (const [key, prop] of Object.entries(def.properties)) {
            const value = component.properties[key];

            propertiesHTML += `<div class="property-item">`;
            propertiesHTML += `<label for="prop-${key}">${prop.label}:</label>`;

            if (prop.type === 'number') {
                propertiesHTML += `<div class="property-input-group">`;
                propertiesHTML += `<input type="number" id="prop-${key}" value="${value}" min="${prop.min}" max="${prop.max}" step="${prop.step}">`;
                if (prop.unit) {
                    propertiesHTML += `<span class="property-unit">${prop.unit}</span>`;
                }
                propertiesHTML += `</div>`;
            } else if (prop.type === 'select') {
                propertiesHTML += `<select id="prop-${key}">`;
                prop.options.forEach(option => {
                    const selected = option === value ? 'selected' : '';
                    propertiesHTML += `<option value="${option}" ${selected}>${option}</option>`;
                });
                propertiesHTML += `</select>`;
            } else {
                propertiesHTML += `<input type="text" id="prop-${key}" value="${value}">`;
            }

            propertiesHTML += `</div>`;
        }

        componentSpecificProperties.innerHTML = propertiesHTML;

        // تعيين وصف وظيفة العنصر
        componentFunction.textContent = def.function;
    }

    // تطبيق خصائص العنصر
    function applyProperties() {
        const component = simulationState.selectedComponent;
        if (!component) return;

        const def = componentDefinitions[component.type];
        if (!def) return;

        // تحديث معرف العنصر
        const newId = document.getElementById('component-id').value;
        if (newId && newId !== component.id) {
            component.id = newId;
        }

        // تحديث خصائص العنصر
        for (const key of Object.keys(def.properties)) {
            const input = document.getElementById(`prop-${key}`);
            if (input) {
                const value = input.type === 'number' ? parseFloat(input.value) : input.value;
                component.properties[key] = value;
            }
        }

        // إعادة رسم الدائرة
        drawCircuit();

        showMessage('تحديث الخصائص', 'تم تحديث خصائص العنصر بنجاح.', 'success');
    }

    // إعادة ضبط خصائص العنصر
    function resetProperties() {
        const component = simulationState.selectedComponent;
        if (!component) return;

        showComponentProperties(component);
    }

    // إغلاق لوحة الخصائص
    function closeProperties() {
        simulationState.selectedComponent = null;
        noSelectionMessage.style.display = 'flex';
        componentProperties.style.display = 'none';
    }

    // تبديل علامة تبويب التحليل
    function switchAnalysisTab(tabId) {
        // تحديث حالة الأزرار
        document.querySelectorAll('.analysis-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        document.querySelector(`.analysis-tab[data-tab="${tabId}"]`).classList.add('active');

        // تحديث المحتوى
        document.querySelectorAll('.analysis-tab-content').forEach(content => {
            content.classList.remove('active');
        });

        document.getElementById(tabId).classList.add('active');
    }

    // إضافة إشارة جديدة للتحليل
    function addWaveform() {
        const nodeSelect = document.getElementById('waveform-node');
        const typeSelect = document.getElementById('waveform-type');

        if (!nodeSelect || !typeSelect) return;

        const node = nodeSelect.value;
        const type = typeSelect.value;

        if (!node) {
            showMessage('خطأ', 'يرجى اختيار نقطة قياس.', 'error');
            return;
        }

        // إضافة الإشارة إلى الرسم البياني
        // (سيتم تنفيذ هذا لاحقًا)

        showMessage('إضافة إشارة', 'تم إضافة الإشارة إلى الرسم البياني.', 'success');
    }

    // مسح جميع الإشارات
    function clearWaveforms() {
        // مسح الرسم البياني
        if (waveformCtx) {
            waveformCtx.clearRect(0, 0, waveformCanvas.width, waveformCanvas.height);
        }

        showMessage('مسح الإشارات', 'تم مسح جميع الإشارات من الرسم البياني.', 'info');
    }

    // حفظ الدائرة
    function saveCircuit() {
        const data = {
            components: simulationState.components,
            wires: simulationState.wires
        };

        const json = JSON.stringify(data);
        const blob = new Blob([json], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = 'circuit.json';
        a.click();

        URL.revokeObjectURL(url);

        showMessage('حفظ الدائرة', 'تم حفظ الدائرة بنجاح.', 'success');
    }

    // تحميل دائرة
    function loadCircuit() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';

        input.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);

                    // تحميل العناصر
                    simulationState.components = data.components || [];

                    // تحميل الأسلاك
                    simulationState.wires = data.wires || [];

                    // إعادة رسم الدائرة
                    drawCircuit();

                    showMessage('تحميل الدائرة', 'تم تحميل الدائرة بنجاح.', 'success');
                } catch (error) {
                    showMessage('خطأ', 'حدث خطأ أثناء تحميل الدائرة.', 'error');
                    console.error(error);
                }
            };

            reader.readAsText(file);
        });

        input.click();
    }

    // عرض رسالة للمستخدم
    function showMessage(title, message, type = 'info') {
        // يمكن استخدام مكتبة خارجية مثل toastr أو إنشاء عنصر مخصص
        console.log(`[${type.toUpperCase()}] ${title}: ${message}`);

        // إنشاء عنصر الرسالة
        const messageElement = document.createElement('div');
        messageElement.className = `message message-${type}`;
        messageElement.innerHTML = `
            <div class="message-header">
                <strong>${title}</strong>
                <button type="button" class="message-close">&times;</button>
            </div>
            <div class="message-body">${message}</div>
        `;

        // إضافة الرسالة إلى الصفحة
        document.body.appendChild(messageElement);

        // إزالة الرسالة بعد فترة
        setTimeout(() => {
            messageElement.classList.add('message-hide');
            setTimeout(() => {
                messageElement.remove();
            }, 300);
        }, 3000);

        // إضافة حدث إغلاق الرسالة
        messageElement.querySelector('.message-close').addEventListener('click', function() {
            messageElement.classList.add('message-hide');
            setTimeout(() => {
                messageElement.remove();
            }, 300);
        });
    }

    // تهيئة أحداث الكانفاس
    function initCanvasEvents() {
        // حدث النقر على الكانفاس
        circuitCanvas.addEventListener('click', function(e) {
            const rect = circuitCanvas.getBoundingClientRect();
            const x = (e.clientX - rect.left) / simulationState.zoom - simulationState.offset.x;
            const y = (e.clientY - rect.top) / simulationState.zoom - simulationState.offset.y;

            // تطبيق الالتصاق بالشبكة إذا كان مفعلاً
            const snapX = simulationState.grid.snap ? Math.round(x / simulationState.grid.size) * simulationState.grid.size : x;
            const snapY = simulationState.grid.snap ? Math.round(y / simulationState.grid.size) * simulationState.grid.size : y;

            // التعامل مع النقر حسب الأداة النشطة
            switch (simulationState.activeTool) {
                case 'select':
                    selectComponentAt(snapX, snapY);
                    break;
                case 'wire':
                    handleWireTool(snapX, snapY);
                    break;
                case 'node':
                    handleNodeTool(snapX, snapY);
                    break;
                case 'text':
                    handleTextTool(snapX, snapY);
                    break;
                case 'delete':
                    deleteComponentAt(snapX, snapY);
                    break;
                case 'rotate':
                    rotateComponentAt(snapX, snapY);
                    break;
                case 'copy':
                    copyComponentAt(snapX, snapY);
                    break;
                case 'paste':
                    pasteComponent(snapX, snapY);
                    break;
            }

            // إغلاق قائمة السياق إذا كانت مفتوحة
            hideContextMenu();
        });

        // حدث النقر بالزر الأيمن على الكانفاس
        circuitCanvas.addEventListener('contextmenu', function(e) {
            e.preventDefault();

            const rect = circuitCanvas.getBoundingClientRect();
            const x = (e.clientX - rect.left) / simulationState.zoom - simulationState.offset.x;
            const y = (e.clientY - rect.top) / simulationState.zoom - simulationState.offset.y;

            // تطبيق الالتصاق بالشبكة إذا كان مفعلاً
            const snapX = simulationState.grid.snap ? Math.round(x / simulationState.grid.size) * simulationState.grid.size : x;
            const snapY = simulationState.grid.snap ? Math.round(y / simulationState.grid.size) * simulationState.grid.size : y;

            // البحث عن العنصر عند موضع النقر
            const component = findComponentAt(snapX, snapY);

            if (component) {
                // تحديد العنصر
                simulationState.selectedComponent = component;

                // عرض قائمة السياق
                showContextMenu(e.clientX, e.clientY, component);
            } else {
                // إغلاق قائمة السياق
                hideContextMenu();
            }

            // إعادة رسم الدائرة
            drawCircuit();
        });

        // حدث تحريك الماوس على الكانفاس
        circuitCanvas.addEventListener('mousemove', function(e) {
            const rect = circuitCanvas.getBoundingClientRect();
            const x = (e.clientX - rect.left) / simulationState.zoom - simulationState.offset.x;
            const y = (e.clientY - rect.top) / simulationState.zoom - simulationState.offset.y;

            // تطبيق الالتصاق بالشبكة إذا كان مفعلاً
            const snapX = simulationState.grid.snap ? Math.round(x / simulationState.grid.size) * simulationState.grid.size : x;
            const snapY = simulationState.grid.snap ? Math.round(y / simulationState.grid.size) * simulationState.grid.size : y;

            simulationState.mousePos.x = snapX;
            simulationState.mousePos.y = snapY;

            // التعامل مع تحريك الماوس حسب الأداة النشطة
            switch (simulationState.activeTool) {
                case 'wire':
                    handleWireToolMove(snapX, snapY);
                    break;
            }

            // إعادة رسم الدائرة لتحديث موضع المؤشر
            drawCircuit();
        });

        // حدث الضغط على زر الماوس
        circuitCanvas.addEventListener('mousedown', function(e) {
            if (e.button !== 0) return; // فقط الزر الأيسر

            const rect = circuitCanvas.getBoundingClientRect();
            const x = (e.clientX - rect.left) / simulationState.zoom - simulationState.offset.x;
            const y = (e.clientY - rect.top) / simulationState.zoom - simulationState.offset.y;

            // تطبيق الالتصاق بالشبكة إذا كان مفعلاً
            const snapX = simulationState.grid.snap ? Math.round(x / simulationState.grid.size) * simulationState.grid.size : x;
            const snapY = simulationState.grid.snap ? Math.round(y / simulationState.grid.size) * simulationState.grid.size : y;

            // التعامل مع الضغط حسب الأداة النشطة
            switch (simulationState.activeTool) {
                case 'select':
                    startDragging(snapX, snapY);
                    break;
            }
        });

        // حدث رفع زر الماوس
        circuitCanvas.addEventListener('mouseup', function(e) {
            if (e.button !== 0) return; // فقط الزر الأيسر

            const rect = circuitCanvas.getBoundingClientRect();
            const x = (e.clientX - rect.left) / simulationState.zoom - simulationState.offset.x;
            const y = (e.clientY - rect.top) / simulationState.zoom - simulationState.offset.y;

            // تطبيق الالتصاق بالشبكة إذا كان مفعلاً
            const snapX = simulationState.grid.snap ? Math.round(x / simulationState.grid.size) * simulationState.grid.size : x;
            const snapY = simulationState.grid.snap ? Math.round(y / simulationState.grid.size) * simulationState.grid.size : y;

            // التعامل مع رفع الزر حسب الأداة النشطة
            switch (simulationState.activeTool) {
                case 'select':
                    stopDragging(snapX, snapY);
                    break;
            }
        });

        // حدث الخروج من الكانفاس
        circuitCanvas.addEventListener('mouseleave', function() {
            // إيقاف السحب إذا كان جارياً
            if (simulationState.dragStart) {
                simulationState.dragStart = null;
            }
        });
    }

    // تهيئة قائمة السياق
    function initContextMenu() {
        const contextMenu = document.getElementById('context-menu');

        // إغلاق قائمة السياق عند النقر في أي مكان آخر
        document.addEventListener('click', function() {
            hideContextMenu();
        });

        // أحداث عناصر قائمة السياق
        document.getElementById('context-copy').addEventListener('click', function() {
            copySelectedComponent();
        });

        document.getElementById('context-cut').addEventListener('click', function() {
            cutSelectedComponent();
        });

        document.getElementById('context-paste').addEventListener('click', function() {
            pasteComponent(simulationState.mousePos.x, simulationState.mousePos.y);
        });

        document.getElementById('context-rotate').addEventListener('click', function() {
            rotateSelectedComponent();
        });

        document.getElementById('context-flip-h').addEventListener('click', function() {
            flipSelectedComponentHorizontally();
        });

        document.getElementById('context-flip-v').addEventListener('click', function() {
            flipSelectedComponentVertically();
        });

        document.getElementById('context-delete').addEventListener('click', function() {
            deleteSelectedComponent();
        });

        document.getElementById('context-properties').addEventListener('click', function() {
            showComponentProperties(simulationState.selectedComponent);
        });
    }

    // تحديد عنصر عند موضع معين
    function selectComponentAt(x, y) {
        // البحث عن العنصر عند الموضع المحدد
        let selectedComponent = null;

        for (let i = simulationState.components.length - 1; i >= 0; i--) {
            const component = simulationState.components[i];

            // التحقق مما إذا كانت النقطة داخل العنصر
            // (هذا مجرد تحقق بسيط، يمكن تحسينه لاحقًا)
            const distance = Math.sqrt(Math.pow(component.x - x, 2) + Math.pow(component.y - y, 2));

            if (distance < 20) {
                selectedComponent = component;
                break;
            }
        }

        // تحديث العنصر المحدد
        simulationState.selectedComponent = selectedComponent;

        // عرض خصائص العنصر المحدد
        showComponentProperties(selectedComponent);

        // إعادة رسم الدائرة
        drawCircuit();
    }

    // تدوير عنصر عند موضع معين
    function rotateComponentAt(x, y) {
        // البحث عن العنصر عند الموضع المحدد
        let component = null;

        for (let i = simulationState.components.length - 1; i >= 0; i--) {
            const comp = simulationState.components[i];

            // التحقق مما إذا كانت النقطة داخل العنصر
            const distance = Math.sqrt(Math.pow(comp.x - x, 2) + Math.pow(comp.y - y, 2));

            if (distance < 20) {
                component = comp;
                break;
            }
        }

        if (component) {
            // تحديد العنصر
            simulationState.selectedComponent = component;

            // تدوير العنصر 90 درجة
            rotateSelectedComponent(90);
        } else {
            showMessage('تدوير', 'لم يتم العثور على عنصر في هذا الموضع.', 'warning');
        }
    }

    // حذف عنصر عند موضع معين
    function deleteComponentAt(x, y) {
        // البحث عن العنصر عند الموضع المحدد
        let componentIndex = -1;

        for (let i = simulationState.components.length - 1; i >= 0; i--) {
            const component = simulationState.components[i];

            // التحقق مما إذا كانت النقطة داخل العنصر
            const distance = Math.sqrt(Math.pow(component.x - x, 2) + Math.pow(component.y - y, 2));

            if (distance < 20) {
                componentIndex = i;
                break;
            }
        }

        if (componentIndex !== -1) {
            // حذف العنصر
            const component = simulationState.components[componentIndex];
            simulationState.components.splice(componentIndex, 1);

            // إذا كان العنصر المحذوف هو العنصر المحدد، إلغاء التحديد
            if (simulationState.selectedComponent === component) {
                simulationState.selectedComponent = null;
                showComponentProperties(null);
            }

            // إعادة رسم الدائرة
            drawCircuit();

            showMessage('حذف', 'تم حذف العنصر بنجاح.', 'success');
        } else {
            showMessage('حذف', 'لم يتم العثور على عنصر في هذا الموضع.', 'warning');
        }
    }

    // نسخ عنصر عند موضع معين
    function copyComponentAt(x, y) {
        // البحث عن العنصر عند الموضع المحدد
        let component = null;

        for (let i = simulationState.components.length - 1; i >= 0; i--) {
            const comp = simulationState.components[i];

            // التحقق مما إذا كانت النقطة داخل العنصر
            const distance = Math.sqrt(Math.pow(comp.x - x, 2) + Math.pow(comp.y - y, 2));

            if (distance < 20) {
                component = comp;
                break;
            }
        }

        if (component) {
            // تحديد العنصر
            simulationState.selectedComponent = component;

            // نسخ العنصر إلى الحافظة
            simulationState.clipboard = JSON.parse(JSON.stringify(component));

            showMessage('نسخ', 'تم نسخ العنصر بنجاح.', 'success');
        } else {
            showMessage('نسخ', 'لم يتم العثور على عنصر في هذا الموضع.', 'warning');
        }
    }

    // تهيئة النظام
    function init() {
        initCanvas();
        initEvents();
        drawCircuit();

        // إضافة أنماط CSS للرسائل
        const style = document.createElement('style');
        style.textContent = `
            .message {
                position: fixed;
                bottom: 20px;
                right: 20px;
                min-width: 250px;
                max-width: 350px;
                background-color: white;
                border-radius: 5px;
                box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
                overflow: hidden;
                z-index: 9999;
                opacity: 1;
                transform: translateY(0);
                transition: opacity 0.3s, transform 0.3s;
            }

            .message-hide {
                opacity: 0;
                transform: translateY(20px);
            }

            .message-header {
                padding: 10px 15px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            }

            .message-body {
                padding: 10px 15px;
            }

            .message-close {
                background: none;
                border: none;
                font-size: 1.2rem;
                cursor: pointer;
                opacity: 0.7;
            }

            .message-close:hover {
                opacity: 1;
            }

            .message-info {
                border-top: 3px solid #2196F3;
            }

            .message-success {
                border-top: 3px solid #4CAF50;
            }

            .message-warning {
                border-top: 3px solid #FFC107;
            }

            .message-error {
                border-top: 3px solid #F44336;
            }
        `;

        document.head.appendChild(style);
    }

    // بدء التهيئة
    init();
});
