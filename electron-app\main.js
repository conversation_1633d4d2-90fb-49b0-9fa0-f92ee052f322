const { app, BrowserWindow, Menu, dialog, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');
const url = require('url');

// Keep a global reference of the window object to avoid garbage collection
let mainWindow;

// Create the browser window
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, 'build/icon.ico')
  });

  // Load the index.html file
  mainWindow.loadFile(path.join(__dirname, 'app', 'index.html'));

  // Create the application menu
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New Circuit',
          accelerator: 'CmdOrCtrl+N',
          click() {
            mainWindow.webContents.send('new-circuit');
          }
        },
        {
          label: 'Open Circuit',
          accelerator: 'CmdOrCtrl+O',
          click() {
            dialog.showOpenDialog({
              properties: ['openFile'],
              filters: [
                { name: 'Circuit Files', extensions: ['json'] }
              ]
            }).then(result => {
              if (!result.canceled && result.filePaths.length > 0) {
                mainWindow.webContents.send('open-circuit', result.filePaths[0]);
              }
            }).catch(err => {
              console.error(err);
            });
          }
        },
        {
          label: 'Save Circuit',
          accelerator: 'CmdOrCtrl+S',
          click() {
            mainWindow.webContents.send('save-circuit');
          }
        },
        {
          label: 'Export as Image',
          accelerator: 'CmdOrCtrl+E',
          click() {
            mainWindow.webContents.send('export-image');
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: 'CmdOrCtrl+Q',
          click() {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'View',
      submenu: [
        {
          label: 'Reload',
          accelerator: 'CmdOrCtrl+R',
          click() {
            mainWindow.reload();
          }
        },
        {
          label: 'Toggle Developer Tools',
          accelerator: process.platform === 'darwin' ? 'Alt+Command+I' : 'Ctrl+Shift+I',
          click() {
            mainWindow.webContents.toggleDevTools();
          }
        },
        { type: 'separator' },
        {
          label: 'Actual Size',
          accelerator: 'CmdOrCtrl+0',
          click() {
            mainWindow.webContents.setZoomLevel(0);
          }
        },
        {
          label: 'Zoom In',
          accelerator: 'CmdOrCtrl+Plus',
          click() {
            const currentZoom = mainWindow.webContents.getZoomLevel();
            mainWindow.webContents.setZoomLevel(currentZoom + 0.5);
          }
        },
        {
          label: 'Zoom Out',
          accelerator: 'CmdOrCtrl+-',
          click() {
            const currentZoom = mainWindow.webContents.getZoomLevel();
            mainWindow.webContents.setZoomLevel(currentZoom - 0.5);
          }
        }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About',
          click() {
            dialog.showMessageBox(mainWindow, {
              title: 'About Electronic Circuits Lab',
              message: 'Electronic Circuits Lab - Virtual Simulation Workbench',
              detail: 'Version 1.0.0\nCreated for educational purposes.\n\nThis application provides a virtual environment for learning and experimenting with electronic circuits.',
              buttons: ['OK'],
              icon: path.join(__dirname, 'build/icon.ico')
            });
          }
        },
        {
          label: 'Documentation',
          click() {
            mainWindow.loadFile(path.join(__dirname, 'app', 'documentation.html'));
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);

  // Handle window close event
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// Create window when Electron has finished initialization
app.whenReady().then(() => {
  createWindow();

  app.on('activate', () => {
    // On macOS, re-create a window when dock icon is clicked and no windows are open
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// Quit when all windows are closed, except on macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Handle IPC messages from renderer process
ipcMain.on('save-file-dialog', (event, data) => {
  dialog.showSaveDialog({
    title: 'Save Circuit',
    defaultPath: path.join(app.getPath('documents'), 'circuit.json'),
    filters: [
      { name: 'Circuit Files', extensions: ['json'] }
    ]
  }).then(result => {
    if (!result.canceled && result.filePath) {
      fs.writeFile(result.filePath, data, (err) => {
        if (err) {
          event.reply('save-file-response', { success: false, message: err.message });
        } else {
          event.reply('save-file-response', { success: true, filePath: result.filePath });
        }
      });
    }
  }).catch(err => {
    event.reply('save-file-response', { success: false, message: err.message });
  });
});

ipcMain.on('export-image-dialog', (event, data) => {
  dialog.showSaveDialog({
    title: 'Export Circuit as Image',
    defaultPath: path.join(app.getPath('pictures'), 'circuit.png'),
    filters: [
      { name: 'PNG Images', extensions: ['png'] }
    ]
  }).then(result => {
    if (!result.canceled && result.filePath) {
      // Remove the data URL prefix
      const base64Data = data.replace(/^data:image\/png;base64,/, '');
      
      fs.writeFile(result.filePath, base64Data, 'base64', (err) => {
        if (err) {
          event.reply('export-image-response', { success: false, message: err.message });
        } else {
          event.reply('export-image-response', { success: true, filePath: result.filePath });
        }
      });
    }
  }).catch(err => {
    event.reply('export-image-response', { success: false, message: err.message });
  });
});
