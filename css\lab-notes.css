/* Lab Notes Specific Styles */

/* Main Layout */
main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

section {
    margin-bottom: 40px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 25px;
}

/* Introduction Section */
#lab-notes-intro {
    background-color: #f8f9fa;
    border-right: 5px solid #007bff;
}

#lab-notes-intro h2 {
    color: #007bff;
    margin-top: 0;
}

#lab-notes-intro p {
    line-height: 1.6;
    margin-bottom: 15px;
}

/* Experiment Navigation */
#experiment-navigation h2 {
    text-align: center;
    margin-bottom: 20px;
}

.experiment-links {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
}

.experiment-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 150px;
    text-decoration: none;
    color: #333;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.experiment-link:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.experiment-icon {
    width: 60px;
    height: 60px;
    background-color: #007bff;
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 10px;
}

.experiment-link span {
    text-align: center;
    font-size: 14px;
}

/* Experiment Sections */
.experiment-section {
    scroll-margin-top: 20px;
}

.experiment-section h2 {
    color: #007bff;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
    margin-top: 0;
}

.experiment-content {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-top: 20px;
}

.circuit-diagram {
    flex: 1;
    min-width: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.circuit-diagram img {
    max-width: 100%;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.circuit-caption {
    margin-top: 10px;
    font-style: italic;
    color: #666;
    text-align: center;
}

.experiment-description {
    flex: 2;
    min-width: 300px;
}

.experiment-description h3 {
    color: #333;
    margin-top: 20px;
    margin-bottom: 10px;
    font-size: 18px;
}

.experiment-description p {
    line-height: 1.6;
    margin-bottom: 15px;
}

.experiment-description ul, 
.experiment-description ol {
    padding-right: 20px;
    margin-bottom: 15px;
}

.experiment-description li {
    margin-bottom: 5px;
    line-height: 1.6;
}

/* Waveform Images */
.waveform-images {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 20px;
}

.waveform-image {
    flex: 1;
    min-width: 250px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.waveform-image img {
    max-width: 100%;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.waveform-caption {
    margin-top: 10px;
    font-style: italic;
    color: #666;
    text-align: center;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .experiment-content {
        flex-direction: column;
    }
    
    .circuit-diagram,
    .experiment-description {
        width: 100%;
    }
    
    .experiment-links {
        justify-content: space-around;
    }
    
    .experiment-link {
        width: 120px;
    }
}

/* Print Styles */
@media print {
    header, footer, #experiment-navigation {
        display: none;
    }
    
    body {
        font-size: 12pt;
    }
    
    section {
        page-break-inside: avoid;
        margin-bottom: 20pt;
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .experiment-content {
        display: block;
    }
    
    .circuit-diagram img {
        max-width: 80%;
        margin: 0 auto;
    }
}
