<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BJT Operating Regions Explorer</title>
    <style>
        body {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 10px; /* Reduced padding for small screens */
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
        }
        .container {
            width: 100%;
            max-width: 1200px;
            background: #fff;
            padding: 15px; /* Reduced padding */
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1a73e8; /* A modern blue */
            text-align: center;
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        h3 {
            color: #333;
            margin-bottom: 10px;
        }
        .controls {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e0e0e0;
        }
        .region-selector, .ib-control {
            margin: 10px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #f9f9f9;
            flex: 1; /* Allow them to grow */
            min-width: 260px; /* Ensure they don't get too small before wrapping */
            box-sizing: border-box;
        }
        .region-selector h3, .ib-control h3 {
            margin-top: 0;
            font-size: 1.1em;
            text-align: left;
        }
        .region-selector label {
            display: block;
            margin-bottom: 8px;
            cursor: pointer;
            font-size: 0.95em;
        }
        .region-selector input[type="radio"] {
            margin-right: 5px;
            vertical-align: middle;
        }
        .ib-control input[type="range"] {
            width: 100%;
            margin-top: 5px;
            cursor: pointer;
        }
        #ibValueDisplay {
            display: block;
            text-align: center;
            font-weight: bold;
            margin-top: 5px;
            color: #1a73e8;
        }
        .ib-control p {
            font-size: 0.85em;
            color: #555;
            margin-top: 10px;
            line-height: 1.4;
        }
        .main-content {
            display: flex;
            flex-wrap: wrap; /* Allow wrapping for smaller screens */
        }
        .graph-container {
            flex: 3; /* Takes more space, e.g., 60% */
            min-width: 280px; /* Minimum width before wrapping */
            padding-right: 15px; /* Space between graph and info */
            box-sizing: border-box;
            margin-bottom: 20px; /* Space if stacked */
        }
        #bjtGraph {
            width: 100%;
            height: auto; /* Will be set by JS */
            border: 1px solid #ccc;
            display: block; /* remove extra space below */
            border-radius: 4px;
        }
        .info-panel {
            flex: 2; /* Takes less space, e.g., 40% */
            min-width: 280px; /* Minimum width */
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
            box-sizing: border-box;
        }
        .info-panel h3, .info-panel h4 {
            margin-top: 0;
            color: #1a73e8; /* Consistent blue for headings */
            font-size: 1.1em;
        }
        #currentQPointRegionDisplay {
            font-size: 1.15em;
            margin-bottom: 10px;
        }
        .info-panel h4 {
            margin-top: 15px;
            margin-bottom: 5px;
            font-size: 1em;
            color: #333; /* Slightly less prominent than h3 */
        }
        .info-panel p {
            font-size: 0.9em;
            margin-bottom: 10px;
        }
        hr {
            border: 0;
            height: 1px;
            background: #e0e0e0;
            margin: 15px 0;
        }

        /* Responsive adjustments */
        @media (max-width: 800px) { /* Adjust breakpoint if needed */
            .main-content {
                flex-direction: column;
            }
            .graph-container {
                padding-right: 0; /* No right padding when stacked */
                flex-basis: auto; /* Reset flex basis */
            }
            .info-panel {
                margin-top: 20px; /* Space above info panel when stacked */
                flex-basis: auto; /* Reset flex basis */
            }
        }
        @media (max-width: 600px) {
            body { padding: 5px; }
            .container { padding: 10px; }
            h1 { font-size: 1.5em; }
            .controls { flex-direction: column; align-items: stretch; }
            .region-selector, .ib-control { margin-left: 0; margin-right: 0; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>BJT Operating Regions Explorer</h1>

        <div class="controls">
            <div class="region-selector">
                <h3>Select Region to Learn About:</h3>
                <label><input type="radio" name="region" value="cutoff" checked> Cut-off</label>
                <label><input type="radio" name="region" value="active"> Active</label>
                <label><input type="radio" name="region" value="saturation"> Saturation</label>
            </div>

            <div class="ib-control">
                <h3>Adjust Base Current (I<sub>B</sub>):</h3>
                <input type="range" id="ibSlider" min="0" max="150" value="0" step="1">
                <span id="ibValueDisplay">I<sub>B</sub>: 0 µA</span>
                <p>
                    <strong>Parameters:</strong>
                    V<sub>CC</sub>: <span id="vccParamDisplay">10</span>V,
                    R<sub>C</sub>: <span id="rcParamDisplay">1</span>kΩ,
                    β: <span id="betaParamDisplay">100</span>,
                    V<sub>CE,sat</sub>: <span id="vceSatParamDisplay">0.2</span>V
                </p>
            </div>
        </div>

        <div class="main-content">
            <div class="graph-container">
                <canvas id="bjtGraph"></canvas>
            </div>
            <div class="info-panel">
                <h3 id="currentQPointRegionDisplay">Current Operating Point: <span id="qPointRegionName">Cut-off</span></h3>
                <p>I<sub>C</sub>: <span id="icValueDisplay">0.00</span> mA</p>
                <p>V<sub>CE</sub>: <span id="vceValueDisplay">10.00</span> V</p>
                <hr>
                <h3 id="selectedRegionNameDisplay">Cut-off Region</h3>
                <p id="selectedRegionDescription">Description will appear here.</p>
                <h4>Conditions:</h4>
                <p id="selectedRegionConditions">Conditions will appear here.</p>
                <h4>I<sub>C</sub> Calculation:</h4>
                <p id="selectedRegionIcCalculation">IC calculation will appear here.</p>
            </div>
        </div>
    </div>

    <script>
        // BJT Parameters (fixed)
        const VCC = 10;       // Volts
        const RC_kOhm = 1;    // kOhms
        const RC = RC_kOhm * 1000; // Ohms
        const BETA = 100;
        const VCE_SAT = 0.2;  // Volts
        const IB_MAX_SLIDER = 150; // uA

        // DOM Elements
        const ibSlider = document.getElementById('ibSlider');
        const ibValueDisplay = document.getElementById('ibValueDisplay');
        
        const vccParamDisplay = document.getElementById('vccParamDisplay');
        const rcParamDisplay = document.getElementById('rcParamDisplay');
        const betaParamDisplay = document.getElementById('betaParamDisplay');
        const vceSatParamDisplay = document.getElementById('vceSatParamDisplay');

        const regionRadioButtons = document.querySelectorAll('input[name="region"]');
        
        const canvas = document.getElementById('bjtGraph');
        const ctx = canvas.getContext('2d');

        const qPointRegionNameDisplay = document.getElementById('qPointRegionName');
        const icValueDisplay = document.getElementById('icValueDisplay');
        const vceValueDisplay = document.getElementById('vceValueDisplay');
        
        const selectedRegionNameDisplay = document.getElementById('selectedRegionNameDisplay');
        const selectedRegionDescription = document.getElementById('selectedRegionDescription');
        const selectedRegionConditions = document.getElementById('selectedRegionConditions');
        const selectedRegionIcCalculation = document.getElementById('selectedRegionIcCalculation');

        const regionData = {
            cutoff: {
                name: "Cut-off Region",
                description: "The transistor acts as an <strong>open switch</strong>. Collector current (I<sub>C</sub>) is approximately zero, and collector-emitter voltage (V<sub>CE</sub>) is high, close to V<sub>CC</sub>.",
                conditions: "Base current (I<sub>B</sub>) is zero or too low to turn the transistor on (e.g., V<sub>BE</sub> < 0.7V for Silicon).<br>" +
                            "I<sub>C</sub> ≈ 0 (ideally, or leakage current I<sub>CEO</sub>).<br>" +
                            "V<sub>CE</sub> ≈ V<sub>CC</sub>.",
                icCalculation: "I<sub>C</sub> ≈ 0. (Practically, I<sub>C</sub> = I<sub>CEO</sub>, a very small leakage current)."
            },
            active: {
                name: "Active Region",
                description: "The transistor acts as a <strong>current-controlled current source</strong> or an <strong>amplifier</strong>. Small changes in base current (I<sub>B</sub>) cause proportionally larger changes in collector current (I<sub>C</sub>).",
                conditions: "Base-Emitter junction is forward biased (V<sub>BE</sub> ≈ 0.7V for Si).<br>" +
                            "Collector-Base junction is reverse biased.<br>" +
                            "I<sub>B</sub> > 0 and transistor is not saturated.<br>" +
                            "V<sub>CE</sub> > V<sub>CE,sat</sub> (typically > 0.2V - 0.3V).",
                icCalculation: "I<sub>C</sub> = β × I<sub>B</sub>.<br>" +
                               "V<sub>CE</sub> = V<sub>CC</sub> - I<sub>C</sub> × R<sub>C</sub>."
            },
            saturation: {
                name: "Saturation Region",
                description: "The transistor acts like a <strong>closed switch</strong>. Collector current (I<sub>C</sub>) is at its maximum, limited by the external circuit (R<sub>C</sub> and V<sub>CC</sub>), and V<sub>CE</sub> is very low (V<sub>CE,sat</sub>).",
                conditions: "Base-Emitter and Collector-Base junctions are forward biased.<br>" +
                            "I<sub>B</sub> is large enough such that β × I<sub>B</sub> > (V<sub>CC</sub> - V<sub>CE,sat</sub>) / R<sub>C</sub>.<br>" +
                            "I<sub>C</sub> ≈ (V<sub>CC</sub> - V<sub>CE,sat</sub>) / R<sub>C</sub> (Circuit limited).<br>" +
                            "V<sub>CE</sub> ≈ V<sub>CE,sat</sub> (typically 0.2V - 0.3V).",
                icCalculation: "I<sub>C</sub> = (V<sub>CC</sub> - V<sub>CE,sat</sub>) / R<sub>C</sub>. (The transistor cannot pass more current due to external circuit limitations)."
            }
        };

        // Graphing constants
        const marginTop = 40, marginRight = 30, marginBottom = 50, marginLeft = 60;
        let graphWidth, graphHeight;
        const VCE_AXIS_MAX = VCC;
        const IC_AXIS_MAX_mA = (VCC / RC) * 1000; // Max IC in mA for axis scaling

        function setupCanvas() {
            const graphContainer = document.querySelector('.graph-container');
            const containerWidth = graphContainer.clientWidth;
            canvas.width = containerWidth;
            canvas.height = Math.min(containerWidth * 0.70, window.innerHeight * 0.6, 400);


            graphWidth = canvas.width - marginLeft - marginRight;
            graphHeight = canvas.height - marginTop - marginBottom;
        }

        function xToPixel(vce) {
            return marginLeft + (vce / VCE_AXIS_MAX) * graphWidth;
        }

        function yToPixel(ic_mA) { // ic is in mA for this function
            return canvas.height - marginBottom - (ic_mA / IC_AXIS_MAX_mA) * graphHeight;
        }

        function drawAxes() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.strokeStyle = '#555'; // Darker grey for axes
            ctx.lineWidth = 1;
            ctx.font = '12px Arial';

            // Y-axis (IC)
            ctx.beginPath();
            ctx.moveTo(marginLeft, marginTop);
            ctx.lineTo(marginLeft, canvas.height - marginBottom);
            ctx.stroke();
            ctx.save();
            ctx.translate(marginLeft - 45, marginTop + graphHeight / 2);
            ctx.rotate(-Math.PI / 2);
            ctx.textAlign = 'center';
            ctx.fillStyle = '#333';
            ctx.fillText('I_C (mA)', 0, 0);
            ctx.restore();
            
            const numYTicks = 5;
            ctx.textAlign = 'right';
            ctx.fillStyle = '#555';
            for (let i = 0; i <= numYTicks; i++) {
                const val = (i / numYTicks) * IC_AXIS_MAX_mA;
                const y = yToPixel(val);
                ctx.fillText(val.toFixed(1), marginLeft - 8, y + 4);
                ctx.beginPath();
                ctx.moveTo(marginLeft - 4, y);
                ctx.lineTo(marginLeft, y);
                ctx.stroke();
            }

            // X-axis (VCE)
            ctx.beginPath();
            ctx.moveTo(marginLeft, canvas.height - marginBottom);
            ctx.lineTo(canvas.width - marginRight, canvas.height - marginBottom);
            ctx.stroke();
            ctx.textAlign = 'center';
            ctx.fillStyle = '#333';
            ctx.fillText('V_CE (V)', marginLeft + graphWidth / 2, canvas.height - marginBottom + 30);
            
            const numXTicks = 5;
            ctx.fillStyle = '#555';
            for (let i = 0; i <= numXTicks; i++) {
                const val = (i / numXTicks) * VCE_AXIS_MAX;
                const x = xToPixel(val);
                ctx.fillText(val.toFixed(1), x, canvas.height - marginBottom + 18);
                ctx.beginPath();
                ctx.moveTo(x, canvas.height - marginBottom);
                ctx.lineTo(x, canvas.height - marginBottom + 4);
                ctx.stroke();
            }
        }

        function drawLoadLine() {
            const p1_vce = VCC;
            const p1_ic_mA = 0;
            const p2_vce = 0; // Theoretical VCE=0 intercept
            const p2_ic_mA = (VCC / RC) * 1000; // Theoretical IC at VCE=0

            ctx.beginPath();
            ctx.moveTo(xToPixel(p1_vce), yToPixel(p1_ic_mA));
            ctx.lineTo(xToPixel(p2_vce), yToPixel(p2_ic_mA));
            
            ctx.strokeStyle = '#007bff'; // Blue for load line
            ctx.lineWidth = 2;
            ctx.stroke();

            // Highlight selected region on the load line
            const selectedRegion = document.querySelector('input[name="region"]:checked').value;
            ctx.lineWidth = 4; // Thicker highlight

            const ic_sat_circuit_mA = ((VCC - VCE_SAT) / RC) * 1000;
            const vce_at_cutoff = VCC;
            const ic_at_cutoff_mA = 0;
            
            const vce_at_saturation_edge = VCE_SAT;
            const ic_at_saturation_edge_mA = ic_sat_circuit_mA;

            // Define small offsets for distinct highlighting of cut-off/saturation ends
            const delta_vce_highlight = (VCC - VCE_SAT) * 0.1; // 10% of active range Vce
            const delta_ic_highlight = ic_sat_circuit_mA * 0.1; // 10% of active range Ic

            if (selectedRegion === 'cutoff') {
                ctx.strokeStyle = '#fd7e14'; // Orange
                ctx.beginPath();
                // Highlight segment near (VCC, 0)
                const vce_highlight_end = Math.max(vce_at_saturation_edge, vce_at_cutoff - delta_vce_highlight);
                const ic_highlight_end_mA = (VCC - vce_highlight_end) / RC * 1000;
                ctx.moveTo(xToPixel(vce_at_cutoff), yToPixel(ic_at_cutoff_mA));
                ctx.lineTo(xToPixel(vce_highlight_end), yToPixel(ic_highlight_end_mA));
                ctx.stroke();
            } else if (selectedRegion === 'saturation') {
                ctx.strokeStyle = '#dc3545'; // Red
                ctx.beginPath();
                // Highlight segment near (VCE_SAT, IC_sat_circuit)
                const vce_highlight_end = Math.min(vce_at_cutoff, vce_at_saturation_edge + delta_vce_highlight);
                const ic_highlight_end_mA = (VCC - vce_highlight_end) / RC * 1000;
                ctx.moveTo(xToPixel(vce_at_saturation_edge), yToPixel(ic_at_saturation_edge_mA));
                ctx.lineTo(xToPixel(vce_highlight_end), yToPixel(ic_highlight_end_mA));
                ctx.stroke();
            } else if (selectedRegion === 'active') {
                ctx.strokeStyle = '#28a745'; // Green
                ctx.beginPath();
                ctx.moveTo(xToPixel(vce_at_saturation_edge), yToPixel(ic_at_saturation_edge_mA));
                ctx.lineTo(xToPixel(vce_at_cutoff), yToPixel(ic_at_cutoff_mA));
                ctx.stroke();
            }
        }

        function drawQPoint(vce_q, ic_q_mA) {
            const q_x = xToPixel(vce_q);
            const q_y = yToPixel(ic_q_mA);

            ctx.beginPath();
            ctx.arc(q_x, q_y, 6, 0, 2 * Math.PI);
            ctx.fillStyle = 'black';
            ctx.fill();
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            ctx.fillStyle = 'black';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = q_x > canvas.width / 2 ? 'right' : 'left'; // Adjust label position
            const labelOffsetX = q_x > canvas.width / 2 ? -8 : 8;
            const labelOffsetY = q_y < canvas.height / 2 ? 15 : -8; // Adjust based on y position
            ctx.fillText('Q', q_x + labelOffsetX, q_y + labelOffsetY);
        }

        function calculateOperatingPoint(ib_uA) {
            const ib_A = ib_uA * 1e-6; // Convert uA to A
            let ic_A, vce_V, region;

            if (ib_A <= 1e-9) { // Consider IB effectively zero for cut-off (1nA threshold)
                ic_A = 0; 
                vce_V = VCC;
                region = "Cut-off";
            } else {
                const ic_active_A = BETA * ib_A;
                const vce_check_V = VCC - ic_active_A * RC;

                if (vce_check_V <= VCE_SAT) { 
                    vce_V = VCE_SAT;
                    ic_A = (VCC - VCE_SAT) / RC;
                    region = "Saturation";
                } else { 
                    ic_A = ic_active_A;
                    vce_V = vce_check_V;
                    region = "Active";
                }
            }
            vce_V = Math.max(VCE_SAT, Math.min(vce_V, VCC)); // Clamp VCE
            if (region === "Cut-off") vce_V = VCC; // Ensure VCE is VCC in pure cutoff
            
            ic_A = Math.max(0, Math.min(ic_A, (VCC - VCE_SAT) / RC)); // Clamp IC

            return { ic_mA: ic_A * 1000, vce_V: vce_V, region: region };
        }

        function updateDisplays(ib_uA, qPoint) {
            ibValueDisplay.innerHTML = `I<sub>B</sub>: ${ib_uA} µA`;
            
            icValueDisplay.textContent = qPoint.ic_mA.toFixed(2);
            vceValueDisplay.textContent = qPoint.vce_V.toFixed(2);
            qPointRegionNameDisplay.textContent = qPoint.region;
        }
        
        function updateSelectedRegionInfo() {
            const selectedValue = document.querySelector('input[name="region"]:checked').value;
            const data = regionData[selectedValue];
            selectedRegionNameDisplay.textContent = data.name;
            selectedRegionDescription.innerHTML = data.description;
            selectedRegionConditions.innerHTML = data.conditions;
            selectedRegionIcCalculation.innerHTML = data.icCalculation;
            
            drawAllGraphElements();
        }

        function drawAllGraphElements() {
            drawAxes();
            drawLoadLine(); 
            const current_ib_uA = parseFloat(ibSlider.value);
            const qPoint = calculateOperatingPoint(current_ib_uA);
            drawQPoint(qPoint.vce_V, qPoint.ic_mA);
        }
        
        function onIbSliderChange() {
            const ib_uA = parseFloat(ibSlider.value);
            const qPoint = calculateOperatingPoint(ib_uA);
            updateDisplays(ib_uA, qPoint);
            drawAllGraphElements(); 
        }

        function initializeApp() {
            vccParamDisplay.textContent = VCC;
            rcParamDisplay.textContent = RC_kOhm;
            betaParamDisplay.textContent = BETA;
            vceSatParamDisplay.textContent = VCE_SAT;
            ibSlider.max = IB_MAX_SLIDER;

            setupCanvas();
            
            updateSelectedRegionInfo(); 
            onIbSliderChange(); 

            ibSlider.addEventListener('input', onIbSliderChange);
            regionRadioButtons.forEach(radio => {
                radio.addEventListener('change', updateSelectedRegionInfo);
            });
            window.addEventListener('resize', () => {
                setupCanvas();
                drawAllGraphElements();
            });
        }

        initializeApp();
    </script>
</body>
</html>
