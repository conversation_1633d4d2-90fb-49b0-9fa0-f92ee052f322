<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NPN Transistor Interactive Simulator</title>
<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    background-color: #f0f2f5;
    color: #333;
    line-height: 1.6;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.app-container {
    flex-grow: 1;
    max-width: 1300px;
    width: 100%;
    margin: 0 auto;
    background-color: #ffffff;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

header, footer {
    text-align: center;
    padding: 15px 20px;
    background-color: #007bff;
    color: white;
}
header h1 {
    margin: 0;
    font-size: 1.6em; /* Adjusted for responsiveness */
}
footer p {
    margin: 0;
    font-size: 0.9em;
}

.main-content {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    padding: 20px;
}

.controls-panel, .info-panel {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    flex: 1;
    min-width: 280px; 
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.visualization-panel {
    flex: 1.5; 
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    min-height: 380px; /* Canvas height + padding */
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

#transistorCanvas {
    max-width: 100%;
    height: auto; 
    display: block;
    background-color: #fdfdfd;
    border-radius: 4px;
    border: 1px solid #eee;
}

.controls-panel h2, .info-panel h2 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #0056b3; 
    font-size: 1.4em;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}

.controls-panel div {
    margin-bottom: 18px;
}
.controls-panel label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}
.controls-panel input[type="range"] {
    width: 100%; 
    cursor: pointer;
}
.controls-panel button {
    background-color: #28a745; 
    color: white;
    border: none;
    padding: 12px 18px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1em;
    font-weight: 500;
    transition: background-color 0.2s ease-in-out;
    width: 100%; 
    margin-top: 10px;
}
.controls-panel button:hover {
    background-color: #218838; 
}

.info-panel p {
    margin: 10px 0;
    font-size: 0.95em;
}
.info-panel strong {
    color: #343a40;
}
#opModeDisplay {
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 4px;
    color: white; /* General white, specific color set by JS */
}
#opModeDescription { /* This is the <p> tag itself */
    font-size: 0.9em;
    color: #495057;
    padding: 10px;
    border-left: 4px solid #007bff; /* Default, JS can change this border color too */
    background-color: #e9ecef; 
    border-radius: 4px;
    margin-top: 5px;
}
.constants-display {
    font-size: 0.85em;
    color: #6c757d;
    background-color: #e9ecef;
    padding: 10px;
    border-radius: 4px;
    margin-top:15px;
    border: 1px solid #dae0e5;
}
.constants-display strong {
    color: #495057;
}


/* Responsive adjustments */
@media (max-width: 992px) { /* Tablet and below */
    .main-content {
        flex-direction: column;
    }
    .visualization-panel {
        order: -1; 
        min-height: 320px; 
    }
}

@media (max-width: 768px) { /* Mobile */
    header h1 {
        font-size: 1.3em;
    }
    .controls-panel h2, .info-panel h2 {
        font-size: 1.2em;
    }
    .controls-panel, .info-panel, .visualization-panel {
        padding: 15px;
    }
    .controls-panel button {
        padding: 10px 15px;
        font-size: 0.95em;
    }
}
</style>
</head>
<body>
    <div class="app-container">
        <header>
            <h1>NPN Transistor Interactive Simulator</h1>
        </header>

        <div class="main-content">
            <div class="controls-panel">
                <h2>Controls</h2>
                <div>
                    <label for="vbeSlider">Base Voltage (V<sub>BE_applied</sub>): <span id="vbeValueDisplay">0.70</span> V</label>
                    <input type="range" id="vbeSlider" min="0" max="1.2" step="0.01" value="0.7">
                </div>
                <div>
                    <label for="betaSlider">Current Gain (β Beta): <span id="betaValueDisplay">100</span></label>
                    <input type="range" id="betaSlider" min="20" max="300" step="1" value="100">
                </div>
                <button id="toggleModeButton">Switch to Circuit Symbol</button>
            </div>

            <div class="visualization-panel">
                <canvas id="transistorCanvas" width="450" height="350"></canvas>
            </div>

            <div class="info-panel">
                <h2>Device Status</h2>
                <p><strong>Operating Mode:</strong> <span id="opModeDisplay">Cut-off</span></p>
                <p id="opModeDescription">Description of the current operating mode.</p>
                <p><strong>V<sub>BE_applied</sub>:</strong> <span id="vbeInfoDisplay">0.00</span> V</p>
                <p><strong>Base Current (I<sub>B</sub>):</strong> <span id="ibInfoDisplay">0.00</span> µA</p>
                <p><strong>Collector Current (I<sub>C</sub>):</strong> <span id="icInfoDisplay">0.00</span> mA</p>
                <p><strong>Collector-Emitter Voltage (V<sub>CE</sub>):</strong> <span id="vceInfoDisplay">5.00</span> V</p>
                <p><strong>Formula (Active Mode):</strong> I<sub>C</sub> ≈ β × I<sub>B</sub></p>
                <div class="constants-display">
                    <strong>Circuit Parameters:</strong> V<sub>CC</sub> = <span id="vccConst">5</span>V, R<sub>B</sub> = <span id="rbConst">10</span>kΩ, R<sub>C</sub> = <span id="rcConst">1</span>kΩ<br>
                    <strong>Transistor Parameters:</strong> V<sub>turn-on</sub> ≈ <span id="vturnonConst">0.7</span>V, V<sub>CE_sat</sub> ≈ <span id="vcesatConst">0.2</span>V
                </div>
            </div>
        </div>
        <footer>
            <p>Learn about NPN transistors interactively!</p>
        </footer>
    </div>

<script>
// Constants
const V_TURN_ON = 0.7; // Volts
const VCE_SAT = 0.2;   // Volts
const VCC = 5.0;       // Volts
const RB = 10000;      // Ohms (10 kΩ)
const RC = 1000;       // Ohms (1 kΩ)

// DOM Elements
let vbeSlider, betaSlider;
let vbeValueDisplay, betaValueDisplay;
let toggleModeButton;
let transistorCanvas, ctx;
let opModeDisplay, opModeDescriptionEl; // Renamed opModeDescription to opModeDescriptionEl
let vbeInfoDisplay, ibInfoDisplay, icInfoDisplay, vceInfoDisplay;
let vccConstDisplay, rbConstDisplay, rcConstDisplay, vturnonConstDisplay, vcesatConstDisplay;

// State
let currentVbeApplied = 0.7;
let currentBeta = 100;
let visualizationMode = 'literal'; // 'literal' or 'symbol'
let Ib_max_viz, Ic_max_viz; // For scaling current visualization

function init() {
    // Sliders and their value displays
    vbeSlider = document.getElementById('vbeSlider');
    betaSlider = document.getElementById('betaSlider');
    vbeValueDisplay = document.getElementById('vbeValueDisplay');
    betaValueDisplay = document.getElementById('betaValueDisplay');

    // Toggle button
    toggleModeButton = document.getElementById('toggleModeButton');

    // Canvas
    transistorCanvas = document.getElementById('transistorCanvas');
    ctx = transistorCanvas.getContext('2d');

    // Info panel displays
    opModeDisplay = document.getElementById('opModeDisplay');
    opModeDescriptionEl = document.getElementById('opModeDescription'); // Corrected element reference
    vbeInfoDisplay = document.getElementById('vbeInfoDisplay');
    ibInfoDisplay = document.getElementById('ibInfoDisplay');
    icInfoDisplay = document.getElementById('icInfoDisplay');
    vceInfoDisplay = document.getElementById('vceInfoDisplay');

    // Constant displays
    vccConstDisplay = document.getElementById('vccConst');
    rbConstDisplay = document.getElementById('rbConst');
    rcConstDisplay = document.getElementById('rcConst');
    vturnonConstDisplay = document.getElementById('vturnonConst');
    vcesatConstDisplay = document.getElementById('vcesatConst');

    // Set initial values from HTML or defaults
    currentVbeApplied = parseFloat(vbeSlider.value);
    currentBeta = parseFloat(betaSlider.value);
    vbeValueDisplay.textContent = currentVbeApplied.toFixed(2);
    betaValueDisplay.textContent = currentBeta;

    // Display constants
    vccConstDisplay.textContent = VCC.toFixed(1);
    rbConstDisplay.textContent = (RB / 1000).toFixed(0);
    rcConstDisplay.textContent = (RC / 1000).toFixed(0);
    vturnonConstDisplay.textContent = V_TURN_ON.toFixed(1);
    vcesatConstDisplay.textContent = VCE_SAT.toFixed(1);

    // Max currents for visualization scaling
    // Handle case where vbeSlider.max < V_TURN_ON (though not in this setup)
    Ib_max_viz = Math.max(1e-9, (parseFloat(vbeSlider.max) - V_TURN_ON) / RB); 
    Ic_max_viz = Math.max(1e-9, (VCC - VCE_SAT) / RC);

    // Event Listeners
    vbeSlider.addEventListener('input', () => {
        currentVbeApplied = parseFloat(vbeSlider.value);
        vbeValueDisplay.textContent = currentVbeApplied.toFixed(2);
        updateSimulation();
    });
    betaSlider.addEventListener('input', () => {
        currentBeta = parseFloat(betaSlider.value);
        betaValueDisplay.textContent = currentBeta;
        updateSimulation();
    });
    toggleModeButton.addEventListener('click', toggleVisualizationMode);

    updateSimulation(); // Initial calculation and draw
}

function updateSimulation() {
    let Ib = 0;
    if (currentVbeApplied >= V_TURN_ON) {
        Ib = (currentVbeApplied - V_TURN_ON) / RB;
    }
    Ib = Math.max(0, Ib); // Ensure Ib is not negative

    let Ic_ideal = currentBeta * Ib;
    let Ic_saturation_limit = (VCC - VCE_SAT) / RC;

    let opMode = "";
    let opDescText = "";
    let Ic = 0;
    let Vce = VCC;
    let modeHighlightColor = "#007bff"; // Default color for description border

    if (Ib <= 1e-9 ) { 
        opMode = "Cut-off";
        Ic = 0;
        Vce = VCC;
        opDescText = `Transistor is OFF. Applied Base Voltage (V<sub>BE_applied</sub> = ${currentVbeApplied.toFixed(2)}V) is below the turn-on threshold (${V_TURN_ON.toFixed(1)}V). Little to no Base Current (I<sub>B</sub>) flows, resulting in negligible Collector Current (I<sub>C</sub>). The transistor acts like an open switch. V<sub>CE</sub> ≈ V<sub>CC</sub>.`;
        opModeDisplay.style.backgroundColor = "#6c757d"; 
        modeHighlightColor = "#6c757d";
    } else if (Ic_ideal >= Ic_saturation_limit && Ic_saturation_limit > 0) { // check Ic_saturation_limit > 0 to avoid issues if RC is huge/zero
        opMode = "Saturation";
        Ic = Ic_saturation_limit;
        Vce = VCE_SAT;
        opDescText = `Transistor is fully ON (saturated). V<sub>BE_applied</sub> (${currentVbeApplied.toFixed(2)}V) and resulting I<sub>B</sub> are high enough that I<sub>C</sub> is limited by the external circuit (V<sub>CC</sub> and R<sub>C</sub>), not by β × I<sub>B</sub>. I<sub>C</sub> reaches its maximum possible value. V<sub>CE</sub> is very low (V<sub>CE_sat</sub> ≈ ${VCE_SAT.toFixed(1)}V). The transistor acts like a closed switch.`;
        opModeDisplay.style.backgroundColor = "#dc3545"; 
        modeHighlightColor = "#dc3545";
    } else {
        opMode = "Active";
        Ic = Ic_ideal;
        Vce = VCC - Ic * RC;
        Vce = Math.max(Vce, VCE_SAT); 
        opDescText = `Transistor is ON and amplifying. V<sub>BE_applied</sub> (${currentVbeApplied.toFixed(2)}V) is sufficient to forward-bias the Base-Emitter junction, allowing Base Current (I<sub>B</sub>) to flow. Collector Current (I<sub>C</sub>) is proportional to I<sub>B</sub> (I<sub>C</sub> = β × I<sub>B</sub>). V<sub>CE</sub> is between V<sub>CE_sat</sub> and V<sub>CC</sub>. The transistor acts as a current-controlled current source.`;
        opModeDisplay.style.backgroundColor = "#28a745"; 
        modeHighlightColor = "#28a745";
    }

    opModeDisplay.textContent = opMode;
    opModeDescriptionEl.innerHTML = opDescText; 
    opModeDescriptionEl.style.borderLeftColor = modeHighlightColor;
    
    vbeInfoDisplay.textContent = currentVbeApplied.toFixed(2);
    ibInfoDisplay.textContent = (Ib * 1e6).toFixed(2); // µA
    icInfoDisplay.textContent = (Ic * 1e3).toFixed(2); // mA
    vceInfoDisplay.textContent = Vce.toFixed(2);

    drawTransistor(opMode, Ib, Ic);
}

function toggleVisualizationMode() {
    if (visualizationMode === 'literal') {
        visualizationMode = 'symbol';
        toggleModeButton.textContent = 'Switch to Literal NPN View';
    } else {
        visualizationMode = 'literal';
        toggleModeButton.textContent = 'Switch to Circuit Symbol';
    }
    updateSimulation(); 
}

function drawArrow(x1, y1, x2, y2, thickness, color, arrowHeadSize = 8) {
    ctx.beginPath();
    ctx.strokeStyle = color;
    ctx.lineWidth = Math.max(1, thickness); 
    ctx.moveTo(x1, y1);
    ctx.lineTo(x2, y2);
    ctx.stroke();

    if (thickness > 1.1) { // Only draw arrowhead if there's significant current
        let angle = Math.atan2(y2 - y1, x2 - x1);
        ctx.beginPath();
        ctx.fillStyle = color;
        ctx.moveTo(x2, y2);
        ctx.lineTo(x2 - arrowHeadSize * Math.cos(angle - Math.PI / 6), y2 - arrowHeadSize * Math.sin(angle - Math.PI / 6));
        ctx.lineTo(x2 - arrowHeadSize * Math.cos(angle + Math.PI / 6), y2 - arrowHeadSize * Math.sin(angle + Math.PI / 6));
        ctx.closePath();
        ctx.fill();
    }
}

function drawTransistor(opMode, Ib, Ic) {
    const w = transistorCanvas.width;
    const h = transistorCanvas.height;
    ctx.clearRect(0, 0, w, h);

    let activeColor = "#007bff";
    let inactiveColor = "#adb5bd";
    let saturationColor = "#c82333"; // Slightly darker red for saturation elements
    let currentPathColor = activeColor;

    if (opMode === "Cut-off") currentPathColor = inactiveColor;
    else if (opMode === "Saturation") currentPathColor = saturationColor;

    let baseCurrentThickness = 1 + Math.min(8, (Ib / Ib_max_viz) * 8);
    let collectorCurrentThickness = 1 + Math.min(10, (Ic / Ic_max_viz) * 10);
    let emitterCurrentSum = Math.max(0, Ic + Ib); // Ensure non-negative
    let emitterCurrentMaxViz = Math.max(1e-9, Ic_max_viz + Ib_max_viz);
    let emitterCurrentThickness = 1 + Math.min(12, (emitterCurrentSum / emitterCurrentMaxViz) * 12);
    
    ctx.font = "bold 14px Segoe UI";
    ctx.textAlign = "center";

    if (visualizationMode === 'literal') {
        const blockHeight = h * 0.45;
        const blockY = (h - blockHeight) / 2;
        const collectorWidth = w * 0.22;
        const baseWidth = w * 0.1;
        const emitterWidth = w * 0.22;
        
        const totalBlocksWidth = collectorWidth + baseWidth + emitterWidth;
        const startX = (w - totalBlocksWidth) / 2;

        let collectorX = startX;
        let baseX = startX + collectorWidth;
        let emitterX = startX + collectorWidth + baseWidth;

        let nColor = (opMode === "Cut-off") ? "#e9ecef" : (opMode === "Saturation" ? "#ffe3e3" : "#e0e9ff"); 
        let pColor = (opMode === "Cut-off") ? "#e9ecef" : (opMode === "Saturation" ? "#ffd1d1" : "#ffe0e0"); 

        ctx.fillStyle = nColor;
        ctx.fillRect(collectorX, blockY, collectorWidth, blockHeight);
        ctx.fillStyle = "#333";
        ctx.fillText("N", collectorX + collectorWidth/2, blockY + blockHeight/2 + 5);
        ctx.fillText("Collector", collectorX + collectorWidth/2, blockY - 15);
        
        ctx.fillStyle = pColor;
        ctx.fillRect(baseX, blockY, baseWidth, blockHeight);
        ctx.fillStyle = "#333";
        ctx.fillText("P", baseX + baseWidth/2, blockY + blockHeight/2 + 5);
        ctx.fillText("Base", baseX + baseWidth/2, blockY - 15);

        ctx.fillStyle = nColor;
        ctx.fillRect(emitterX, blockY, emitterWidth, blockHeight);
        ctx.fillStyle = "#333";
        ctx.fillText("N", emitterX + emitterWidth/2, blockY + blockHeight/2 + 5);
        ctx.fillText("Emitter", emitterX + emitterWidth/2, blockY + blockHeight + 25);

        const terminalLineLength = 40;
        // Collector Terminal
        let C_contactX = collectorX + collectorWidth / 2;
        let C_wireY_end = blockY;
        let C_wireY_start = C_wireY_end - terminalLineLength;
        drawArrow(C_contactX, C_wireY_start, C_contactX, C_wireY_end -5 , collectorCurrentThickness, currentPathColor);
        ctx.fillText("C", C_contactX, C_wireY_start - 10);

        // Base Terminal
        let B_contactY = blockY + blockHeight / 2;
        let B_wireX_end = baseX;
        let B_wireX_start = B_wireX_end - terminalLineLength * 1.5; // Make base wire longer
        drawArrow(B_wireX_start, B_contactY, B_wireX_end - 5, B_contactY, baseCurrentThickness, currentPathColor);
        ctx.fillText("B", B_wireX_start - 10, B_contactY + 5);

        // Emitter Terminal
        let E_contactX = emitterX + emitterWidth / 2;
        let E_wireY_start = blockY + blockHeight;
        let E_wireY_end = E_wireY_start + terminalLineLength;
        drawArrow(E_contactX, E_wireY_start + 5, E_contactX, E_wireY_end, emitterCurrentThickness, currentPathColor);
        ctx.fillText("E", E_contactX, E_wireY_end + 15);

        // Depletion regions
        ctx.fillStyle = 'rgba(150, 150, 150, 0.3)';
        let depletionVisualWidth = (opMode === "Cut-off") ? 10 : (opMode === "Active" ? 5 : 3);
        // C-B junction
        let cbDepletionActual = (opMode === "Active" && Vce > VCE_SAT + 0.4) ? depletionVisualWidth * 1.5 : depletionVisualWidth / (opMode === "Saturation" ? 2 : 1) ;
        if(opMode !== "Saturation") ctx.fillRect(baseX - cbDepletionActual/2, blockY, cbDepletionActual, blockHeight);
        
        // B-E junction
        let beDepletionActual = (opMode === "Cut-off") ? depletionVisualWidth : depletionVisualWidth/2;
        ctx.fillRect(emitterX - beDepletionActual/2, blockY, beDepletionActual, blockHeight);

    } else { // 'symbol' mode
        const centerX = w / 2;
        const centerY = h / 2;
        const radius = Math.min(w,h) * 0.12; 
        const lineLen = Math.min(w,h) * 0.18;  

        ctx.strokeStyle = (opMode === "Cut-off") ? inactiveColor : activeColor;
        ctx.lineWidth = 3;
        
        // Circle
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        ctx.stroke();

        // Base line
        const baseWireStartX = centerX - radius - lineLen;
        const baseWireEndX = centerX - radius;
        ctx.beginPath(); ctx.moveTo(baseWireStartX, centerY); ctx.lineTo(baseWireEndX, centerY); ctx.stroke();
        drawArrow(baseWireStartX, centerY, baseWireEndX - 5, centerY, baseCurrentThickness, currentPathColor);
        ctx.fillStyle = "#333"; ctx.fillText("B", baseWireStartX - 15, centerY + 5);

        // Collector line
        const collAngle = -Math.PI / 3; 
        const collWireStartX = centerX + radius * Math.cos(collAngle);
        const collWireStartY = centerY + radius * Math.sin(collAngle);
        const collWireEndX = collWireStartX + lineLen * Math.cos(collAngle);
        const collWireEndY = collWireStartY + lineLen * Math.sin(collAngle);
        ctx.beginPath(); ctx.moveTo(collWireStartX, collWireStartY); ctx.lineTo(collWireEndX, collWireEndY); ctx.stroke();
        drawArrow(collWireEndX, collWireEndY, collWireStartX + 5 * Math.cos(collAngle), collWireStartY + 5 * Math.sin(collAngle), collectorCurrentThickness, currentPathColor);
        ctx.fillStyle = "#333"; ctx.fillText("C", collWireEndX + 10 * Math.cos(collAngle), collWireEndY + 10 * Math.sin(collAngle) -5);
        
        // Emitter line
        const emitAngle = Math.PI / 3;
        const emitWireStartX = centerX + radius * Math.cos(emitAngle);
        const emitWireStartY = centerY + radius * Math.sin(emitAngle);
        const emitWireEndX = emitWireStartX + lineLen * Math.cos(emitAngle);
        const emitWireEndY = emitWireStartY + lineLen * Math.sin(emitAngle);
        ctx.beginPath(); ctx.moveTo(emitWireStartX, emitWireStartY); ctx.lineTo(emitWireEndX, emitWireEndY); ctx.stroke();
        drawArrow(emitWireStartX, emitWireStartY, emitWireEndX - 5 * Math.cos(emitAngle), emitWireEndY - 5 * Math.sin(emitAngle), emitterCurrentThickness, currentPathColor);
        ctx.fillStyle = "#333"; ctx.fillText("E", emitWireEndX + 10 * Math.cos(emitAngle), emitWireEndY + 10 * Math.sin(emitAngle) + 5);

        // NPN Emitter Arrow Symbol (part of the transistor symbol itself, not current flow)
        const symArrowPosX = emitWireStartX + (emitWireEndX - emitWireStartX) * 0.45;
        const symArrowPosY = emitWireStartY + (emitWireEndY - emitWireStartY) * 0.45;
        const symArrowLength = 12; 
        const symArrowAngleOffset = Math.PI / 8;

        ctx.beginPath();
        ctx.strokeStyle = (opMode === "Cut-off") ? inactiveColor : activeColor; // Match line color
        ctx.lineWidth = 3; // Match line width
        ctx.moveTo(symArrowPosX, symArrowPosY);
        ctx.lineTo(symArrowPosX - symArrowLength * Math.cos(emitAngle - symArrowAngleOffset), symArrowPosY - symArrowLength * Math.sin(emitAngle - symArrowAngleOffset));
        ctx.moveTo(symArrowPosX, symArrowPosY);
        ctx.lineTo(symArrowPosX - symArrowLength * Math.cos(emitAngle + symArrowAngleOffset), symArrowPosY - symArrowLength * Math.sin(emitAngle + symArrowAngleOffset));
        ctx.stroke();
    }
}

document.addEventListener('DOMContentLoaded', init);
</script>
</body>
</html>
