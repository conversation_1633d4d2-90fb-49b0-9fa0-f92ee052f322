/* Main Styles for Electronic Circuits Lab */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #0779e4;
    --accent-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #333;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --text-color: #333;
    --border-radius: 5px;
    --box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', 'Arial', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #f4f4f4;
    direction: rtl; /* Right-to-left for Arabic */
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: 1rem;
    color: var(--primary-color);
    font-weight: 600;
}

h1 {
    font-size: 2rem;
    margin-top: 0;
}

h2 {
    font-size: 1.8rem;
    border-bottom: 2px solid var(--secondary-color);
    padding-bottom: 0.5rem;
    margin-top: 1.5rem;
}

h3 {
    font-size: 1.4rem;
    margin-top: 1.2rem;
}

p {
    margin-bottom: 1rem;
}

a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--accent-color);
}

/* Layout */
.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
    overflow: hidden;
}

header {
    background: var(--dark-color);
    color: white;
    padding-top: 30px;
    min-height: 70px;
    border-bottom: var(--secondary-color) 3px solid;
    text-align: center;
}

header h1 {
    color: white;
    margin: 0;
    padding: 0.5rem;
}

/* Navigation */
nav {
    background: #444;
    color: white;
    padding: 10px 0;
    text-align: center;
}

nav ul {
    display: flex;
    list-style: none;
    justify-content: center;
    flex-wrap: wrap;
    padding: 0;
}

nav ul li {
    margin: 0 15px;
}

nav ul li a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-size: 18px;
}

nav ul li a:hover {
    background-color: var(--secondary-color);
    color: white;
}

/* Main Content */
main {
    padding: 20px;
    background: white;
    margin-top: 20px;
    min-height: calc(100vh - 200px);
}

section {
    margin-bottom: 20px;
    padding: 20px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
}

/* Buttons */
.button, button, input[type="submit"] {
    display: inline-block;
    background: var(--secondary-color);
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
    font-size: 16px;
    text-decoration: none;
}

.button:hover, button:hover, input[type="submit"]:hover {
    background: var(--accent-color);
    text-decoration: none;
}

/* Forms and Controls */
input, select, textarea {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    width: 100%;
    font-family: inherit;
    font-size: 16px;
}

input[type="range"] {
    width: 100%;
    height: 25px;
    -webkit-appearance: none;
    background: #d3d3d3;
    outline: none;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    background: var(--secondary-color);
    cursor: pointer;
    border: 4px solid #fff;
    box-shadow: -407px 0 0 400px var(--secondary-color);
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

/* Experiment Selector */
.experiment-selector {
    margin-bottom: 20px;
    text-align: center;
}

.experiment-selector label {
    display: block;
    margin-bottom: 8px;
    font-size: 18px;
    color: var(--text-color);
}

.experiment-selector select {
    padding: 10px;
    font-size: 16px;
    border: 1px solid #ccc;
    border-radius: var(--border-radius);
    width: 50%;
    max-width: 400px;
}

/* Simulation Area */
.simulation-area {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    margin: 1.5rem 0;
    border: 2px dashed #ccc;
    text-align: center;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.simulation-area iframe {
    width: 100%;
    height: 600px;
    border: 1px solid #ccc;
    border-radius: var(--border-radius);
    display: none; /* Initially hidden */
}

.simulation-area #simulation-placeholder {
    font-size: 18px;
    color: #777;
}

.control-panel {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.control-group {
    margin-bottom: 1rem;
}

.results-panel {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-top: 1.5rem;
    width: 100%;
    text-align: right;
}

.results-panel p {
    margin-bottom: 0.5rem;
}

/* Latest Updates Section */
.update-card {
    background-color: #e7f3fe;
    border-right: 5px solid var(--secondary-color);
    margin-bottom: 15px;
    padding: 15px 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.update-card h3 {
    margin-top: 0;
    color: var(--secondary-color);
}

.update-card p {
    margin-bottom: 5px;
}

.update-card p em {
    font-size: 0.9em;
    color: #555;
}

/* Experiment Grid and Cards */
.experiment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin: 1.5rem 0;
}

.experiment-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius);
    padding: 0;
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.experiment-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.experiment-card h3 {
    padding: 1rem;
    margin-top: 0;
    background-color: var(--primary-color);
    color: white;
}

.experiment-card h3 a {
    color: white;
    text-decoration: none;
}

.experiment-card p {
    padding: 0 1rem;
    font-size: 0.95em;
    color: #555;
    margin: 1rem 0;
    flex-grow: 1;
}

.experiment-thumbnail {
    width: 100%;
    height: 160px;
    object-fit: cover;
    border-bottom: 1px solid #eee;
}

.experiment-card .btn {
    margin: 1rem;
    display: inline-block;
    background-color: var(--secondary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    text-decoration: none;
}

.experiment-card .btn:hover {
    background-color: var(--accent-color);
}

/* Circuit Canvas */
.canvas-container {
    position: relative;
    width: 100%;
    height: 500px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    overflow: hidden;
}

#circuit-canvas {
    width: 100%;
    height: 100%;
    cursor: crosshair;
}

/* Search Bar */
.search-container {
    margin: 1.5rem 0;
    display: flex;
}

.search-container input {
    flex-grow: 1;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    font-size: 1rem;
}

.search-container button {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    margin: 0;
}

/* Category Filters */
.category-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin: 1rem 0;
}

.category-filter {
    background-color: #eee;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition);
}

.category-filter:hover, .category-filter.active {
    background-color: var(--secondary-color);
    color: white;
}

/* Footer */
footer {
    text-align: center;
    padding: 20px;
    background: var(--dark-color);
    color: white;
    margin-top: 20px;
}

footer p {
    margin: 0;
}

/* Additional Utility Classes */
#no-results {
    display: none;
    text-align: center;
    margin: 2rem 0;
}

.workbench-preview-image {
    width: 100%;
    max-width: 300px;
}

/* Hero Section */
#hero-section {
    background-color: var(--primary-color);
    color: white;
    padding: 3rem 0;
    margin-bottom: 2rem;
    text-align: center;
}

#hero-section h2 {
    color: white;
    border-bottom: none;
    font-size: 2.2rem;
    margin-bottom: 1.5rem;
}

#hero-section p {
    font-size: 1.2rem;
    max-width: 800px;
    margin: 0 auto 2rem auto;
}

.hero-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Breadcrumb */
.breadcrumb {
    margin: 1rem 0 2rem 0;
    padding: 0.5rem 1rem;
    background-color: #f8f9fa;
    border-radius: var(--border-radius);
}

.breadcrumb-separator {
    color: #999;
    margin: 0 0.5rem;
}

/* Experiment Procedure Styles */
.procedure-container {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

/* Circuit Diagram and Workbench Styles */
.circuit-diagram-workbench {
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid #007bff;
    background-color: #f0f8ff;
    border-radius: 8px;
}

.circuit-diagram-container {
    text-align: center;
    margin-bottom: 15px;
}

.circuit-diagram-image {
    max-width: 500px;
    border: 1px solid #ccc;
    padding: 10px;
    margin-bottom: 10px;
}

.circuit-diagram-image-sm {
    max-width: 400px;
    border: 1px solid #ccc;
    padding: 10px;
    margin-bottom: 15px;
}

.workbench-container {
    border: 1px solid #0056b3;
    padding: 15px;
    margin-top: 15px;
    background-color: #e7f3fe;
}

.interactive-circuit-area {
    width: 100%;
    height: 200px;
    background-color: #e9ecef;
    margin-top: 10px;
    border: 1px dashed #adb5bd;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.interactive-circuit-area-lg {
    height: 300px;
}

.mode-results {
    margin-top: 20px;
}

.current-mode {
    font-weight: bold;
}

.image-caption {
    font-size: 0.8em;
    text-align: center;
}

/* Enhanced Lab Content Styles */
.theory-box {
    background-color: #e8f4fd;
    border-left: 4px solid #0779e4;
    padding: 15px;
    margin: 15px 0;
    border-radius: 4px;
}

.theory-box h3 {
    color: #0056b3;
    margin-top: 0;
    margin-bottom: 10px;
}

.theory-box ul {
    padding-right: 20px;
}

.theory-box li {
    margin-bottom: 8px;
}

.interactive-demo {
    background-color: #f0f8ff;
    border: 1px solid #b8daff;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

/* Simulation Tabs */
.simulation-tabs {
    display: flex;
    border-bottom: 1px solid #b8daff;
    margin-bottom: 20px;
}

.tab-button {
    padding: 10px 15px;
    background-color: #f0f8ff;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
}

.tab-button:hover {
    background-color: #e3f2fd;
}

.tab-button.active {
    border-bottom-color: #0779e4;
    font-weight: bold;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Simulation Controls */
.simulation-controls {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.simulation-controls > div {
    margin-bottom: 10px;
}

.preset-buttons {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    grid-column: 1 / -1;
}

.preset-button {
    padding: 8px 15px;
    background-color: #e3f2fd;
    border: 1px solid #b8daff;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.preset-button:hover {
    background-color: #b8daff;
}

/* Simulation Visualization */
.simulation-visualization {
    margin: 20px 0;
    display: flex;
    justify-content: center;
}

.transistor-diagram {
    border: 1px solid #ddd;
    background-color: white;
    padding: 10px;
    border-radius: 4px;
}

/* Curve Selection */
.curve-selection {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.curve-button {
    padding: 8px 15px;
    background-color: #e3f2fd;
    border: 1px solid #b8daff;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.curve-button:hover {
    background-color: #b8daff;
}

.curve-button.active {
    background-color: #0779e4;
    color: white;
}

.curve-container {
    border: 1px solid #ddd;
    background-color: white;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.curve-controls {
    margin-top: 15px;
}

.ib-values {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 10px;
}

/* Simulation Help */
.simulation-help {
    margin-top: 20px;
    border-top: 1px solid #b8daff;
    padding-top: 15px;
}

.simulation-help summary {
    cursor: pointer;
    color: #0779e4;
    font-weight: bold;
}

.help-content {
    margin-top: 10px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.troubleshooting-tips {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
    padding: 15px;
    margin: 15px 0;
    border-radius: 4px;
}

.practical-application {
    background-color: #d4edda;
    border-left: 4px solid #28a745;
    padding: 15px;
    margin: 15px 0;
    border-radius: 4px;
}

.assessment-question {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
}

.assessment-question h4 {
    margin-top: 0;
    color: #495057;
}

.assessment-options {
    margin-top: 10px;
}

.assessment-options label {
    display: block;
    margin-bottom: 8px;
    cursor: pointer;
}

.assessment-feedback {
    margin-top: 10px;
    padding: 10px;
    border-radius: 4px;
    display: none;
}

.feedback-correct {
    background-color: #d4edda;
    color: #155724;
}

.feedback-incorrect {
    background-color: #f8d7da;
    color: #721c24;
}

/* Responsive Design */
@media (max-width: 768px) {
    nav ul {
        flex-direction: column;
        align-items: center;
    }

    nav ul li {
        margin: 0.5rem 0;
    }

    .experiment-grid {
        grid-template-columns: 1fr;
    }

    .control-panel {
        grid-template-columns: 1fr;
    }

    .canvas-container {
        height: 400px;
    }

    .experiment-selector select {
        width: 100%;
    }

    #hero-section {
        padding: 2rem 0;
    }

    #hero-section h2 {
        font-size: 1.8rem;
    }

    #hero-section p {
        font-size: 1rem;
    }

    .circuit-diagram-image,
    .circuit-diagram-image-sm {
        max-width: 100%;
    }
}