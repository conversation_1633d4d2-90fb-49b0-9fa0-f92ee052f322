<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="600" height="450" viewBox="0 0 600 450">
  <!-- Background -->
  <rect width="600" height="450" fill="#ffffff"/>
  
  <!-- Title -->
  <text x="300" y="30" font-family="Arial" font-size="20" text-anchor="middle" fill="#333333">Bistable Multivibrator (Flip-Flop) Circuit</text>
  
  <!-- Power Supply (VCC) -->
  <text x="300" y="70" font-family="Arial" font-size="14" text-anchor="middle" fill="#333333">VCC (+5V)</text>
  <line x1="300" y1="80" x2="300" y2="100" stroke="#000000" stroke-width="2"/>
  
  <!-- Collector Resistors -->
  <!-- RC1 -->
  <line x1="200" y1="100" x2="200" y2="120" stroke="#000000" stroke-width="2"/>
  <rect x="190" y="120" width="20" height="40" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <text x="170" y="140" font-family="Arial" font-size="12" fill="#333333">RC1</text>
  <text x="170" y="155" font-family="Arial" font-size="10" fill="#666666">1kΩ</text>
  <line x1="200" y1="160" x2="200" y2="180" stroke="#000000" stroke-width="2"/>
  
  <!-- RC2 -->
  <line x1="400" y1="100" x2="400" y2="120" stroke="#000000" stroke-width="2"/>
  <rect x="390" y="120" width="20" height="40" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <text x="420" y="140" font-family="Arial" font-size="12" fill="#333333">RC2</text>
  <text x="420" y="155" font-family="Arial" font-size="10" fill="#666666">1kΩ</text>
  <line x1="400" y1="160" x2="400" y2="180" stroke="#000000" stroke-width="2"/>
  
  <!-- Connect VCC to RC1 and RC2 -->
  <line x1="200" y1="100" x2="400" y2="100" stroke="#000000" stroke-width="2"/>
  <line x1="300" y1="100" x2="300" y2="80" stroke="#000000" stroke-width="2"/>
  
  <!-- Transistors -->
  <!-- Q1 -->
  <!-- Base -->
  <line x1="150" y1="200" x2="180" y2="200" stroke="#000000" stroke-width="2"/>
  <!-- Collector -->
  <line x1="200" y1="180" x2="200" y2="190" stroke="#000000" stroke-width="2"/>
  <!-- Emitter -->
  <line x1="200" y1="210" x2="200" y2="230" stroke="#000000" stroke-width="2"/>
  <!-- Transistor Symbol -->
  <circle cx="190" cy="200" r="15" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <line x1="180" y1="200" x2="190" y2="190" stroke="#000000" stroke-width="2"/>
  <line x1="190" y1="190" x2="190" y2="210" stroke="#000000" stroke-width="2"/>
  <line x1="190" y1="210" x2="200" y2="220" stroke="#000000" stroke-width="2"/>
  <line x1="190" y1="190" x2="200" y2="180" stroke="#000000" stroke-width="2"/>
  <text x="170" y="200" font-family="Arial" font-size="12" fill="#333333">Q1</text>
  
  <!-- Q2 -->
  <!-- Base -->
  <line x1="450" y1="200" x2="420" y2="200" stroke="#000000" stroke-width="2"/>
  <!-- Collector -->
  <line x1="400" y1="180" x2="400" y2="190" stroke="#000000" stroke-width="2"/>
  <!-- Emitter -->
  <line x1="400" y1="210" x2="400" y2="230" stroke="#000000" stroke-width="2"/>
  <!-- Transistor Symbol -->
  <circle cx="410" cy="200" r="15" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <line x1="420" y1="200" x2="410" y2="190" stroke="#000000" stroke-width="2"/>
  <line x1="410" y1="190" x2="410" y2="210" stroke="#000000" stroke-width="2"/>
  <line x1="410" y1="210" x2="400" y2="220" stroke="#000000" stroke-width="2"/>
  <line x1="410" y1="190" x2="400" y2="180" stroke="#000000" stroke-width="2"/>
  <text x="430" y="200" font-family="Arial" font-size="12" fill="#333333">Q2</text>
  
  <!-- Ground -->
  <line x1="200" y1="230" x2="200" y2="250" stroke="#000000" stroke-width="2"/>
  <line x1="400" y1="230" x2="400" y2="250" stroke="#000000" stroke-width="2"/>
  <line x1="180" y1="250" x2="420" y2="250" stroke="#000000" stroke-width="2"/>
  <line x1="295" y1="250" x2="295" y2="270" stroke="#000000" stroke-width="2"/>
  <line x1="305" y1="250" x2="305" y2="270" stroke="#000000" stroke-width="2"/>
  <line x1="280" y1="270" x2="320" y2="270" stroke="#000000" stroke-width="2"/>
  <line x1="285" y1="275" x2="315" y2="275" stroke="#000000" stroke-width="2"/>
  <line x1="290" y1="280" x2="310" y2="280" stroke="#000000" stroke-width="2"/>
  <line x1="295" y1="285" x2="305" y2="285" stroke="#000000" stroke-width="2"/>
  
  <!-- Cross-Coupling Resistors -->
  <!-- R1 -->
  <line x1="200" y1="180" x2="300" y2="180" stroke="#000000" stroke-width="2"/>
  <line x1="300" y1="180" x2="300" y2="200" stroke="#000000" stroke-width="2"/>
  <rect x="290" y="200" width="20" height="30" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <text x="320" y="215" font-family="Arial" font-size="12" fill="#333333">R1</text>
  <text x="320" y="230" font-family="Arial" font-size="10" fill="#666666">10kΩ</text>
  <line x1="300" y1="230" x2="300" y2="250" stroke="#000000" stroke-width="2"/>
  
  <!-- R2 -->
  <line x1="400" y1="180" x2="500" y2="180" stroke="#000000" stroke-width="2"/>
  <line x1="500" y1="180" x2="500" y2="200" stroke="#000000" stroke-width="2"/>
  <rect x="490" y="200" width="20" height="30" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <text x="520" y="215" font-family="Arial" font-size="12" fill="#333333">R2</text>
  <text x="520" y="230" font-family="Arial" font-size="10" fill="#666666">10kΩ</text>
  <line x1="500" y1="230" x2="500" y2="250" stroke="#000000" stroke-width="2"/>
  <line x1="500" y1="250" x2="420" y2="250" stroke="#000000" stroke-width="2"/>
  
  <!-- Cross-Coupling Connections -->
  <line x1="200" y1="180" x2="150" y2="180" stroke="#000000" stroke-width="2"/>
  <line x1="150" y1="180" x2="150" y2="200" stroke="#000000" stroke-width="2"/>
  <line x1="400" y1="180" x2="450" y2="180" stroke="#000000" stroke-width="2"/>
  <line x1="450" y1="180" x2="450" y2="200" stroke="#000000" stroke-width="2"/>
  
  <!-- Set Button -->
  <line x1="100" y1="200" x2="120" y2="200" stroke="#000000" stroke-width="2"/>
  <rect x="120" y="190" width="20" height="20" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <text x="130" y="205" font-family="Arial" font-size="12" text-anchor="middle" fill="#333333">S</text>
  <line x1="140" y1="200" x2="150" y2="200" stroke="#000000" stroke-width="2"/>
  <text x="100" y="180" font-family="Arial" font-size="12" fill="#333333">SET</text>
  
  <!-- Reset Button -->
  <line x1="500" y1="200" x2="480" y2="200" stroke="#000000" stroke-width="2"/>
  <rect x="460" y="190" width="20" height="20" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <text x="470" y="205" font-family="Arial" font-size="12" text-anchor="middle" fill="#333333">R</text>
  <line x1="460" y1="200" x2="450" y2="200" stroke="#000000" stroke-width="2"/>
  <text x="500" y="180" font-family="Arial" font-size="12" fill="#333333">RESET</text>
  
  <!-- Output LEDs -->
  <!-- LED1 (Q) -->
  <line x1="200" y1="180" x2="200" y2="160" stroke="#000000" stroke-width="2"/>
  <polygon points="190,140 210,140 200,160" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <line x1="190" y1="130" x2="210" y2="130" stroke="#000000" stroke-width="2"/>
  <line x1="200" y1="130" x2="200" y2="120" stroke="#000000" stroke-width="2"/>
  <text x="180" y="120" font-family="Arial" font-size="12" fill="#333333">Q</text>
  
  <!-- LED2 (Q̅) -->
  <line x1="400" y1="180" x2="400" y2="160" stroke="#000000" stroke-width="2"/>
  <polygon points="390,140 410,140 400,160" fill="#ffffff" stroke="#000000" stroke-width="2"/>
  <line x1="390" y1="130" x2="410" y2="130" stroke="#000000" stroke-width="2"/>
  <line x1="400" y1="130" x2="400" y2="120" stroke="#000000" stroke-width="2"/>
  <text x="420" y="120" font-family="Arial" font-size="12" fill="#333333">Q̅</text>
  
  <!-- Truth Table -->
  <rect x="150" y="320" width="300" height="110" fill="none" stroke="#000000" stroke-width="1"/>
  <text x="300" y="340" font-family="Arial" font-size="14" text-anchor="middle" fill="#333333">Truth Table</text>
  
  <!-- Table Headers -->
  <line x1="150" y1="350" x2="450" y2="350" stroke="#000000" stroke-width="1"/>
  <line x1="250" y1="320" x2="250" y2="430" stroke="#000000" stroke-width="1"/>
  <line x1="350" y1="320" x2="350" y2="430" stroke="#000000" stroke-width="1"/>
  
  <text x="200" y="365" font-family="Arial" font-size="12" text-anchor="middle" fill="#333333">S</text>
  <text x="300" y="365" font-family="Arial" font-size="12" text-anchor="middle" fill="#333333">R</text>
  <text x="400" y="365" font-family="Arial" font-size="12" text-anchor="middle" fill="#333333">Q</text>
  
  <!-- Table Rows -->
  <line x1="150" y1="375" x2="450" y2="375" stroke="#000000" stroke-width="1"/>
  <text x="200" y="390" font-family="Arial" font-size="12" text-anchor="middle" fill="#333333">0</text>
  <text x="300" y="390" font-family="Arial" font-size="12" text-anchor="middle" fill="#333333">0</text>
  <text x="400" y="390" font-family="Arial" font-size="12" text-anchor="middle" fill="#333333">No Change</text>
  
  <line x1="150" y1="400" x2="450" y2="400" stroke="#000000" stroke-width="1"/>
  <text x="200" y="415" font-family="Arial" font-size="12" text-anchor="middle" fill="#333333">0</text>
  <text x="300" y="415" font-family="Arial" font-size="12" text-anchor="middle" fill="#333333">1</text>
  <text x="400" y="415" font-family="Arial" font-size="12" text-anchor="middle" fill="#333333">0</text>
  
  <line x1="150" y1="425" x2="450" y2="425" stroke="#000000" stroke-width="1"/>
  <text x="200" y="440" font-family="Arial" font-size="12" text-anchor="middle" fill="#333333">1</text>
  <text x="300" y="440" font-family="Arial" font-size="12" text-anchor="middle" fill="#333333">0</text>
  <text x="400" y="440" font-family="Arial" font-size="12" text-anchor="middle" fill="#333333">1</text>
</svg>
