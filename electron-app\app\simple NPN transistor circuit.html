<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BJT Transistor Switch Simulator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
            display: flex;
            justify-content: center;
        }

        .app-container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 900px;
            display: flex;
            flex-wrap: wrap; /* Allow wrapping for smaller screens */
        }

        .schematic-container {
            flex: 1 1 400px; /* Grow, shrink, basis 400px */
            padding-right: 20px;
            min-width: 300px; /* Ensure SVG is not too squished */
        }

        .controls-info-container {
            flex: 1 1 300px; /* Grow, shrink, basis 300px */
            min-width: 280px;
        }

        #circuit-diagram {
            width: 100%;
            height: auto;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        .label {
            font-size: 10px;
            font-family: monospace;
        }

        .component {
            stroke: black;
            stroke-width: 1.5;
            fill: #fff;
        }
        .resistor { fill: #eee; }
        .transistor-base-circle { fill: #fff; }

        .wire {
            stroke: black;
            stroke-width: 1;
        }

        .current-path {
            stroke-width: 3.5;
            fill: none;
            transition: opacity 0.3s ease;
        }

        #ic-flow-path { stroke: dodgerblue; }
        #ib-flow-path { stroke: crimson; }

        .controls label, .info p, .explanation h4, .explanation p {
            margin-bottom: 10px;
            display: block;
        }
        
        .controls input[type="range"], .controls input[type="number"] {
            width: calc(100% - 100px); /* Adjust based on label width */
            max-width: 200px;
            margin-bottom: 15px;
        }
        .controls input[type="number"] {
            width: 60px;
        }

        .info {
            background-color: #e9f5ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .info h3 { margin-top: 0; }

        .explanation {
            background-color: #fffbe6;
            padding: 15px;
            border-radius: 5px;
            font-size: 0.9em;
        }
        .explanation h4 { margin-top: 0; }

        #led-status.on { color: green; font-weight: bold; }
        #led-status.off { color: red; font-weight: bold; }

        .beta-explanation {
            font-size: 0.85em;
            color: #555;
            margin-top: -10px;
            margin-bottom: 15px;
        }
        .led-threshold-note {
            font-size: 0.85em;
            color: #555;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
            }
            .schematic-container {
                padding-right: 0;
                margin-bottom: 20px;
            }
            .controls input[type="range"] {
                width: calc(100% - 80px); /* Adjust for smaller screens */
            }
        }

    </style>
</head>
<body>
    <div class="app-container">
        <div class="schematic-container">
            <svg id="circuit-diagram" viewBox="0 0 300 320"> <!-- Adjusted viewBox for better layout -->
                <!-- VCC -->
                <text x="125" y="20" class="label">VCC (+5V)</text>
                <line x1="125" y1="25" x2="125" y2="40" class="wire" id="wire-vcc-rc"/>
                
                <!-- RC -->
                <rect x="110" y="40" width="30" height="15" class="component resistor"/>
                <text x="145" y="52" class="label">RC (220Ω)</text>
                <line x1="125" y1="55" x2="125" y2="70" class="wire" id="wire-rc-led"/>
                
                <!-- LED -->
                <g id="led-symbol-group" transform="translate(125, 85)"> <!-- Centered at 125, 85 -->
                    <path d="M 0 -10 L -8 2 L 8 2 Z" stroke="black" stroke-width="1.5" fill="lightgray" id="led-diode-triangle"/>
                    <line x1="-8" y1="10" x2="8" y2="10" stroke="black" stroke-width="1.5"/>
                    <line x1="0" y1="-10" x2="0" y2="-15" stroke="black" stroke-width="1.5"/> <!-- Anode connection point -->
                    <line x1="0" y1="10" x2="0" y2="15" stroke="black" stroke-width="1.5"/>  <!-- Cathode connection point -->
                    <g id="led-light-rays" visibility="hidden">
                        <line x1="-10" y1="-8" x2="-17" y2="-13" stroke="orange" stroke-width="2"/>
                        <line x1="10" y1="-8" x2="17" y2="-13" stroke="orange" stroke-width="2"/>
                        <line x1="-12" y1="0" x2="-20" y2="0" stroke="orange" stroke-width="2"/>
                        <line x1="12" y1="0" x2="20" y2="0" stroke="orange" stroke-width="2"/>
                    </g>
                </g>
                <text x="150" y="90" class="label">LED</text>
                <line x1="125" y1="85+15" x2="125" y2="115" class="wire" id="wire-led-c"/> <!-- LED cathode to Collector -->

                <!-- Transistor (NPN) -->
                <!-- Collector wire -->
                <line x1="125" y1="115" x2="125" y2="130" class="wire" id="wire-c-transistor"/>
                <!-- Base circle -->
                <circle cx="125" cy="150" r="20" class="component transistor-base-circle"/>
                <!-- Emitter Arrow and line -->
                <line x1="125" y1="170" x2="125" y2="190" class="wire" id="wire-e-transistor"/>
                <polygon points="122,180 128,180 125,187" fill="black" /> <!-- Emitter arrow -->
                <!-- Base wire -->
                <line x1="70" y1="150" x2="105" y2="150" class="wire" id="wire-b-transistor-base"/>
                <text x="90" y="147" class="label">B</text>
                <text x="135" y="130" class="label">C</text>
                <text x="135" y="190" class="label">E</text>

                <!-- Ground -->
                <line x1="125" y1="190" x2="125" y2="210" class="wire" id="wire-e-gnd"/>
                <line x1="110" y1="210" x2="140" y2="210" stroke="black" stroke-width="2"/>
                <line x1="115" y1="215" x2="135" y2="215" stroke="black" stroke-width="2"/>
                <line x1="120" y1="220" x2="130" y2="220" stroke="black" stroke-width="2"/>
                <text x="130" y="225" class="label">GND</text>

                <!-- Vin and RB (on the left) -->
                <text x="20" y="110" class="label">Vin</text>
                <line x1="35" y1="115" x2="35" y2="135" class="wire" id="wire-vin-rb"/>
                <rect x="20" y="135" width="30" height="15" class="component resistor"/>
                <text x="5" y="147" class="label">RB (10kΩ)</text>
                <line x1="35" y1="150" x2="70" y2="150" class="wire" id="wire-rb-b"/> <!-- RB to Base wire -->
                
                <!-- Current Flow Paths -->
                <!-- Ic Path: VCC -> RC -> LED -> C -> E -> GND -->
                <path id="ic-flow-path" class="current-path"
                      d="M125,25 V40 M125,55 V70 M125,100 V115 M125,115 V130 M125,130 L125,170 M125,170 V190 M125,190 V210"
                      opacity="0"/>
                <!-- Ib Path: Vin -> RB -> B -> E -> GND -->
                <path id="ib-flow-path" class="current-path"
                      d="M35,115 V135 M35,150 H70 M70,150 L105,150 M105,150 Q115,160 125,170 M125,170 V190 M125,190 V210"
                      opacity="0"/>
            </svg>
        </div>

        <div class="controls-info-container">
            <div class="controls">
                <label for="vin-slider">Vin: <span id="vin-value">0.00</span> V</label>
                <input type="range" id="vin-slider" min="0" max="5" step="0.01" value="0">
                
                <label for="beta-input">Beta (β): <span id="beta-value">100</span></label>
                <input type="number" id="beta-input" min="10" max="500" step="10" value="100">
                <p class="beta-explanation">Beta (β), or hFE, is the DC current gain. It's the ratio of collector current (Ic) to base current (Ib) when the transistor is in the active region: Ic = β * Ib.</p>
            </div>

            <div class="info">
                <h3>Circuit Status:</h3>
                <p>Base Current (Ib): <span id="ib-value">0.00</span> mA</p>
                <p>Collector Current (Ic/LED Current): <span id="ic-value">0.00</span> mA</p>
                <p>LED Status: <span id="led-status" class="off">OFF</span></p>
                <p class="led-threshold-note">LED lights up if Ic > <span id="led-threshold-display">5.0</span> mA.</p>
                <p>Collector-Emitter Voltage (VCE): <span id="vce-value">5.00</span> V</p>
                <p>Transistor State: <span id="transistor-state">CUTOFF</span></p>
            </div>

            <div class="explanation">
                <h4>How it works as a switch:</h4>
                <p>When the base current (IB) is sufficient (i.e., the voltage at the base is at least 0.7V greater than the voltage at the emitter, Vbe > 0.7V), the transistor "turns on". This allows a larger current (IC) to flow from the collector to the emitter, turning on the LED. In this state, the transistor acts like a <strong>closed switch</strong>.</p>
                <p>When the base current is insufficient (Vbe < 0.7V), the transistor "turns off", blocking current flow from collector to emitter. The LED remains off. In this state, the transistor acts like an <strong>open switch</strong>.</p>
            </div>
        </div>
    </div>

    <script>
        // --- DOM Elements ---
        const vinSlider = document.getElementById('vin-slider');
        const betaInput = document.getElementById('beta-input');
        const vinValueSpan = document.getElementById('vin-value');
        const betaValueSpan = document.getElementById('beta-value');
        const ibValueSpan = document.getElementById('ib-value');
        const icValueSpan = document.getElementById('ic-value');
        const ledStatusSpan = document.getElementById('led-status');
        const ledThresholdDisplaySpan = document.getElementById('led-threshold-display');
        const vceValueSpan = document.getElementById('vce-value');
        const transistorStateSpan = document.getElementById('transistor-state');
        const ledDiodeTriangle = document.getElementById('led-diode-triangle');
        const ledLightRays = document.getElementById('led-light-rays');
        const icFlowPath = document.getElementById('ic-flow-path');
        const ibFlowPath = document.getElementById('ib-flow-path');

        // --- Circuit Parameters (Constants) ---
        const VCC = 5.0;        // Volts
        const RC = 220.0;       // Ohms
        const RB_default = 10000.0; // Ohms (can be changed if RB input is added)
        let RB = RB_default;    // Ohms for RB, fixed for this version as per schematic label
        const VBE_ON = 0.7;     // Volts (Base-Emitter turn-on voltage)
        const VCE_SAT = 0.2;    // Volts (Collector-Emitter saturation voltage)
        const V_LED_ON = 2.0;   // Volts (Forward voltage drop of LED when ON)
        const LED_THRESHOLD_CURRENT = 0.005; // Amps (5mA)

        // --- Initial Setup ---
        function initializeApp() {
            vinSlider.addEventListener('input', updateCircuit);
            betaInput.addEventListener('input', updateCircuit);
            ledThresholdDisplaySpan.textContent = (LED_THRESHOLD_CURRENT * 1000).toFixed(1);
            updateCircuit(); // Initial calculation and display
        }

        // --- Main Calculation Logic ---
        function updateCircuit() {
            const Vin = parseFloat(vinSlider.value);
            const Beta = parseFloat(betaInput.value);

            vinValueSpan.textContent = Vin.toFixed(2);
            betaValueSpan.textContent = Beta;

            let Ib = 0;
            let Ic = 0;
            let Vce = VCC;
            let transistorState = "CUTOFF";
            let isLedOn = false;

            // Calculate Base Current (Ib)
            if (Vin > VBE_ON) {
                Ib = (Vin - VBE_ON) / RB;
            }
            Ib = Math.max(0, Ib); // Ensure Ib is not negative

            // Determine Transistor State and Currents
            if (Ib <= 1e-9) { // Effectively zero base current (cutoff)
                transistorState = "CUTOFF";
                Ic = 0;
                Vce = VCC;
                isLedOn = false;
            } else {
                // Assume Active mode initially
                let Ic_active_trial = Beta * Ib;
                
                // Calculate Vce if it were in active mode
                // To do this, we need to know if the LED would be on with Ic_active_trial
                let V_LED_drop_active_trial = (Ic_active_trial >= LED_THRESHOLD_CURRENT) ? V_LED_ON : 0;
                let Vce_active_trial = VCC - Ic_active_trial * RC - V_LED_drop_active_trial;

                if (Vce_active_trial < VCE_SAT) {
                    // Transistor is Saturated
                    transistorState = "SATURATION";
                    Vce = VCE_SAT;

                    // Calculate Ic in saturation, considering LED state
                    const V_available_for_RC_LED = VCC - VCE_SAT;
                    
                    // Potential Ic if LED is on (drops V_LED_ON)
                    let Ic_sat_if_led_on = (V_available_for_RC_LED - V_LED_ON) / RC;
                    
                    if (Ic_sat_if_led_on >= LED_THRESHOLD_CURRENT) {
                        Ic = Ic_sat_if_led_on;
                        isLedOn = true;
                    } else {
                        // LED likely off or current too low for full V_LED_ON drop
                        // Calculate Ic assuming LED drop is minimal (or 0)
                        Ic = V_available_for_RC_LED / RC;
                        isLedOn = (Ic >= LED_THRESHOLD_CURRENT); // Check if it still meets threshold
                    }
                } else {
                    // Transistor is in Active Mode
                    transistorState = "ACTIVE";
                    Ic = Ic_active_trial;
                    Vce = Vce_active_trial;
                    isLedOn = (Ic >= LED_THRESHOLD_CURRENT);
                }
            }

            // Ensure Ic and Vce are within logical bounds
            Ic = Math.max(0, Ic);
            if (transistorState === "CUTOFF") Vce = VCC;
            else if (transistorState === "SATURATION") Vce = VCE_SAT;
            else Vce = Math.max(VCE_SAT, Math.min(VCC, Vce));


            // --- Update UI Elements ---
            ibValueSpan.textContent = (Ib * 1000).toFixed(2); // Display in mA
            icValueSpan.textContent = (Ic * 1000).toFixed(2); // Display in mA
            vceValueSpan.textContent = Vce.toFixed(2);
            transistorStateSpan.textContent = transistorState;

            if (isLedOn) {
                ledStatusSpan.textContent = "ON";
                ledStatusSpan.className = "on";
                ledDiodeTriangle.style.fill = "lime"; // Brighter green for ON
                ledLightRays.style.visibility = "visible";
            } else {
                ledStatusSpan.textContent = "OFF";
                ledStatusSpan.className = "off";
                ledDiodeTriangle.style.fill = "lightgray";
                ledLightRays.style.visibility = "hidden";
            }

            // Update current flow visualization
            // Normalize opacity based on typical max currents (e.g., Ib_max ~0.5mA, Ic_max ~15mA)
            const max_expected_ib = 0.0005; // 0.5 mA
            const max_expected_ic = 0.015;  // 15 mA
            
            ibFlowPath.style.opacity = (Ib > 1e-7) ? Math.min(1, Ib / max_expected_ib) * 0.8 + 0.2 : 0;
            icFlowPath.style.opacity = (Ic > 1e-5) ? Math.min(1, Ic / max_expected_ic) * 0.8 + 0.2 : 0;
        }

        // --- Start the application ---
        document.addEventListener('DOMContentLoaded', initializeApp);
    </script>
</body>
</html>
