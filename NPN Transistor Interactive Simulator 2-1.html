<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NPN Transistor Simulator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .app-container {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 800px;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }

        .main-layout {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .circuit-column {
            flex: 1;
            min-width: 280px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .controls-info-column {
            flex: 1.5;
            min-width: 300px;
        }

        .circuit-diagram {
            border: 1px solid #ccc;
            padding: 15px;
            border-radius: 5px;
            background-color: #f9f9f9;
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 250px; /* Fixed width for diagram simplicity */
            margin: 0 auto; /* Center it if column is wider */
        }

        .component {
            border: 1px solid #666;
            padding: 5px 10px;
            margin: 5px 0;
            text-align: center;
            font-size: 0.9em;
            background-color: #e9e9e9;
            border-radius: 3px;
            position: relative; /* For pin labels */
        }

        .resistor { width: 80px; }
        .led {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            line-height: 40px; /* Center text vertically */
            background-color: #555; /* LED OFF color */
            color: white;
            transition: background-color 0.3s, box-shadow 0.3s;
        }
        .led.on {
            background-color: #ff0000; /* LED ON color */
            box-shadow: 0 0 15px #ff0000, 0 0 20px #ff0000;
        }

        .transistor {
            width: 70px;
            height: 70px;
            border: 2px solid #333;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            position: relative;
            background-color: #fff;
        }
        .pin-line {
            position: absolute;
            background-color: #333;
            height: 2px; /* For horizontal lines */
            width: 20px;  /* For horizontal lines */
        }
        .pin-line.vertical {
            width: 2px;
            height: 20px;
        }
        .collector-pin { top: -22px; left: 50%; transform: translateX(-50%); }
        .emitter-pin   { bottom: -22px; left: 50%; transform: translateX(-50%); }
        .base-pin      { left: -22px; top: 50%; transform: translateY(-50%); width:20px; height:2px; } /* Horizontal */

        .pin-label {
            position: absolute;
            font-size: 0.8em;
            color: #333;
        }
        .label-c { top: -18px; left: calc(50% + 10px); }
        .label-b { top: calc(50% - 8px); left: -30px; }
        .label-e { bottom: -18px; left: calc(50% + 10px); }


        .wire {
            width: 2px;
            background-color: #999;
            margin: 0 auto; /* Center vertical wires */
        }
        .wire-vertical { height: 20px; }
        .wire-horizontal-container {
            display: flex;
            align-items: center;
            width: 100%;
            justify-content: center; /* Center items for base path */
        }
        .wire-horizontal {
            height: 2px;
            background-color: #999;
            flex-grow: 1;
        }
        .connection-point {
             width: 6px; height: 6px; background-color: #333; border-radius: 50%; margin: -2px; /* Overlap wire slightly */
        }


        .controls, .info-display, .explanation {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }

        .controls label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        .controls input[type="range"] {
            width: 100%;
            cursor: pointer;
        }

        .info-display p {
            margin: 8px 0;
            font-size: 1.1em;
        }
        .info-display span {
            font-weight: bold;
            padding: 2px 6px;
            border-radius: 3px;
        }

        #region-value.cut-off { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb;}
        #region-value.active { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb;}
        #region-value.saturation { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;}

        #led-state-text.on { color: #ff0000; font-weight: bold;}
        #led-state-text.off { color: #555; }

        .explanation h3 {
            margin-top: 0;
            color: #0056b3;
        }
        .explanation ul { padding-left: 20px; }
        .explanation li { margin-bottom: 8px; }

        /* Responsive adjustments */
        @media (max-width: 600px) {
            .main-layout {
                flex-direction: column;
            }
            .circuit-column, .controls-info-column {
                width: 100%;
            }
            .circuit-diagram {
                width: 90%; /* Allow diagram to scale a bit */
            }
        }

    </style>
</head>
<body>
    <div class="app-container">
        <h1>NPN Transistor Interactive Simulator</h1>

        <div class="main-layout">
            <div class="circuit-column">
                <h3>Circuit Diagram</h3>
                <div class="circuit-diagram">
                    <div class="component" id="vcc-source">VCC (+5V)</div>
                    <div class="wire wire-vertical" style="height: 15px;"></div>
                    <div class="component resistor" id="rc">R<sub>C</sub> (220&Omega;)</div>
                    <div class="wire wire-vertical" style="height: 15px;"></div>
                    <div class="component led" id="led-visual">LED</div>
                    <div class="wire wire-vertical" style="height: 15px;"></div>
                    
                    <div style="display: flex; align-items: center; width: 100%; margin: 5px 0;">
                        <div style="display: flex; flex-direction: column; align-items: center; flex-grow: 1;"> <!-- Left side of transistor -->
                            <div class="component" id="vin-source" style="width: auto; padding: 5px;">V<sub>IN</sub> (Slider)</div>
                            <div class="wire wire-vertical" style="height: 10px;"></div>
                            <div class="component resistor" id="rb" style="width:60px;">R<sub>B</sub> (10k&Omega;)</div>
                             <div class="wire wire-vertical" style="height: 10px; margin-bottom: -2px;"></div>
                        </div>
                        <div class="wire wire-horizontal" style="width: 20px; margin-right: -2px;"></div> <!-- Connects RB path to Base -->
                        <div class="transistor">
                            NPN
                            <div class="pin-line vertical collector-pin"></div>
                            <div class="pin-label label-c">C</div>
                            <div class="pin-line base-pin"></div>
                            <div class="pin-label label-b">B</div>
                            <div class="pin-line vertical emitter-pin"></div>
                            <div class="pin-label label-e">E</div>
                        </div>
                         <div style="width: 20px;"></div> <!-- Spacer -->
                    </div>

                    <div class="wire wire-vertical" style="height: 15px;"></div>
                    <div class="component" id="gnd">GND</div>
                </div>
            </div>

            <div class="controls-info-column">
                <div class="controls">
                    <label for="vin-slider">V<sub>IN</sub> (Voltage to R<sub>B</sub>): <span id="vin-value-display">0.00</span> V</label>
                    <input type="range" id="vin-slider" min="0" max="5" step="0.01" value="0">
                </div>

                <div class="info-display">
                    <h3>Live Values</h3>
                    <p>Base Voltage (V<sub>B</sub>): <span id="vb-value">0.00</span> V</p>
                    <p>Collector Current (I<sub>C</sub>): <span id="ic-value">0.00</span> mA</p>
                    <p>Operating Region: <span id="region-value" class="cut-off">Cut-off</span></p>
                    <p>LED State: <span id="led-state-text" class="off">OFF</span></p>
                </div>

                <div class="explanation">
                    <h3>How it Works</h3>
                    <p>This simulation demonstrates an NPN transistor as a switch or amplifier for an LED.</p>
                    <ul>
                        <li><strong>V<sub>IN</sub>:</strong> Input voltage controlled by the slider. This voltage is applied to the base resistor (R<sub>B</sub>).</li>
                        <li><strong>V<sub>B</sub>:</strong> Voltage at the base of the transistor. The transistor needs V<sub>B</sub> to be around 0.7V (V<sub>BE(on)</sub>) to start conducting.</li>
                        <li><strong>I<sub>C</sub>:</strong> Current flowing through the collector, R<sub>C</sub>, and the LED. This current determines LED brightness.</li>
                        <li><strong>LED State:</strong> The LED turns ON when I<sub>C</sub> exceeds a threshold (approx. 1mA).</li>
                    </ul>
                    <h4>Operating Regions:</h4>
                    <ul>
                        <li>
                            <strong>Cut-off Region:</strong> When V<sub>IN</sub> is too low (typically V<sub>B</sub> &lt; 0.7V), the base-emitter junction is not forward-biased.
                            No significant base current (I<sub>B</sub>) flows, so no collector current (I<sub>C</sub>) flows. The transistor acts like an open switch, and the LED is OFF.
                        </li>
                        <li>
                            <strong>Active Region:</strong> As V<sub>IN</sub> increases such that V<sub>B</sub> is around 0.7V, a small I<sub>B</sub> starts to flow.
                            The transistor amplifies this I<sub>B</sub>, resulting in a larger I<sub>C</sub> (I<sub>C</sub> = &beta; * I<sub>B</sub>).
                            In this region, I<sub>C</sub> is proportional to I<sub>B</sub> (and thus related to V<sub>IN</sub>). The transistor acts like a variable current source. The LED brightens as I<sub>C</sub> increases.
                            The collector-emitter voltage (V<sub>CE</sub>) is greater than V<sub>CE(sat)</sub> (approx 0.2V).
                        </li>
                        <li>
                            <strong>Saturation Region:</strong> When V<sub>IN</sub> (and thus I<sub>B</sub>) is high enough, the transistor tries to conduct more I<sub>C</sub> than the external circuit (R<sub>C</sub>, LED, V<sub>CC</sub>) allows.
                            I<sub>C</sub> reaches its maximum possible value, limited by V<sub>CC</sub>, R<sub>C</sub>, LED forward voltage (V<sub>F</sub>), and V<sub>CE(sat)</sub>.
                            Further increases in V<sub>IN</sub> or I<sub>B</sub> do not significantly increase I<sub>C</sub>. The transistor acts like a closed switch with a small voltage drop (V<sub>CE(sat)</sub> &approx; 0.2V). The LED is fully lit.
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- Circuit Parameters (Constants) ---
        const VCC = 5.0;        // Volts
        const RC = 220;         // Ohms (Collector Resistor)
        const RB = 10000;       // Ohms (Base Resistor, 10k)
        const BETA = 100;       // DC Current Gain (hFE)
        const VBE_ON = 0.7;     // Volts (Base-Emitter turn-on voltage)
        const VCE_SAT = 0.2;    // Volts (Collector-Emitter saturation voltage)
        const LED_VF = 2.0;     // Volts (LED Forward Voltage drop when ON)
        const LED_I_THRESHOLD = 0.001; // Amps (1mA, LED turn-on current threshold)

        // --- DOM Elements ---
        const vinSlider = document.getElementById('vin-slider');
        const vinValueDisplay = document.getElementById('vin-value-display');
        const vbValueDisplay = document.getElementById('vb-value');
        const icValueDisplay = document.getElementById('ic-value');
        const regionValueDisplay = document.getElementById('region-value');
        const ledVisual = document.getElementById('led-visual');
        const ledStateText = document.getElementById('led-state-text');

        // --- Update Function ---
        function updateCircuit() {
            const vin = parseFloat(vinSlider.value);
            vinValueDisplay.textContent = vin.toFixed(2);

            let vb = 0;
            let ib = 0;
            let ic = 0;
            let vce = VCC; // Default VCE is VCC when in cut-off
            let region = "Cut-off";
            let ledIsOn = false;

            // 1. Calculate Base Voltage (VB) and Base Current (IB)
            if (vin <= VBE_ON) {
                ib = 0;
                vb = vin; // VB follows VIN until VBE_ON is reached
            } else {
                vb = VBE_ON;
                ib = (vin - VBE_ON) / RB;
                if (ib < 0) ib = 0; // Should not happen if vin > VBE_ON
            }

            // 2. Determine Operating Region and Collector Current (IC)
            if (ib <= 0) { // CUT-OFF REGION
                region = "Cut-off";
                ic = 0;
                vce = VCC; // No current through RC or LED
                ledIsOn = false;
            } else {
                // Potentially Active or Saturation
                let icActiveCalc = BETA * ib;
                
                // Determine actual LED forward voltage based on potential current
                let actualLedVf = (icActiveCalc > LED_I_THRESHOLD) ? LED_VF : 0;

                // Calculate VCE assuming active region
                let vceCalcActive = VCC - (icActiveCalc * RC) - actualLedVf;

                if (vceCalcActive < VCE_SAT) { // SATURATION REGION
                    region = "Saturation";
                    vce = VCE_SAT;

                    // Recalculate IC for saturation, considering if LED is on
                    // Max current if LED is on and its Vf applies:
                    let icSatLedOn = (VCC - LED_VF - VCE_SAT) / RC;
                    // Max current if LED is off (no Vf) or VCC not enough for LED_VF:
                    let icSatLedOff = (VCC - VCE_SAT) / RC;

                    if (icSatLedOn > LED_I_THRESHOLD) {
                        // Sufficient VCC to overcome LED_VF and VCE_SAT and light LED
                        ic = icSatLedOn;
                        ledIsOn = true;
                    } else {
                        // Not enough current to light LED robustly with its Vf, or VCC too low.
                        // Calculate saturation current as if LED has no significant Vf drop.
                        ic = icSatLedOff;
                        ledIsOn = (ic > LED_I_THRESHOLD); // Check if this current lights LED
                    }
                    if (ic < 0) ic = 0; // Ensure non-negative current

                } else { // ACTIVE REGION
                    region = "Active";
                    ic = icActiveCalc;
                    vce = vceCalcActive;
                    ledIsOn = (ic > LED_I_THRESHOLD);
                }
            }
            
            // Final check for LED state based on actual IC
            ledIsOn = (ic > LED_I_THRESHOLD);

            // 3. Update UI
            vbValueDisplay.textContent = vb.toFixed(2);
            icValueDisplay.textContent = (ic * 1000).toFixed(2); // Display in mA

            regionValueDisplay.textContent = region;
            regionValueDisplay.className = region.toLowerCase(); // Applies 'cut-off', 'active', 'saturation' class

            if (ledIsOn) {
                ledVisual.classList.add('on');
                ledStateText.textContent = "ON";
                ledStateText.className = 'on';
            } else {
                ledVisual.classList.remove('on');
                ledStateText.textContent = "OFF";
                ledStateText.className = 'off';
            }
        }

        // --- Event Listener ---
        vinSlider.addEventListener('input', updateCircuit);

        // --- Initial Call ---
        updateCircuit();

    </script>
</body>
</html>
