<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مضخم الباعث المشترك (NPN)</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .simulation-container {
            display: flex;
            flex-wrap: wrap;
            gap: 25px; /* زيادة الفجوة قليلاً */
            justify-content: space-around;
            padding: 20px; /* زيادة الحشوة */
            border: 1px solid #ccc;
            border-radius: 8px; /* إضافة حواف دائرية للحاوية الرئيسية */
            margin-bottom: 25px;
            background-color: #f0f4f8; /* لون خلفية خفيف للحاوية */
        }
        .controls-section, .output-section, .visualization-section, #theory, #experiment-intro {
            flex: 1;
            min-width: 300px; /* زيادة الحد الأدنى للعرض */
            padding: 20px;
            border: 1px solid #d1d9e6; /* تغيير لون الحدود */
            border-radius: 8px;
            background-color: #ffffff; /* خلفية بيضاء للأقسام */
            box-shadow: 0 2px 5px rgba(0,0,0,0.05); /* إضافة ظل خفيف */
            margin-bottom: 20px; /* إضافة هامش سفلي للأقسام النظرية والمقدمة */
        }
        .controls-section h3, .output-section h3, .visualization-section h3, #theory h3, #experiment-intro h3 {
            color: #0056b3; /* لون مميز للعناوين الفرعية */
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 8px;
            margin-top: 0;
        }
        .controls-section label {
            display: block;
            margin: 12px 0 6px;
            font-weight: 500; /* خط أثقل قليلاً للتسميات */
        }
        .controls-section input[type="range"], .controls-section input[type="number"] {
            width: calc(100% - 20px); /* تعديل العرض ليناسب الحشوة */
            margin-bottom: 15px;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .output-section p {
            font-size: 1.1em;
            margin: 10px 0;
            line-height: 1.6;
        }
        .output-section span {
            font-weight: bold;
            color: #2a7aaf; /* تعديل لون النص المميز */
            background-color: #e7f3fe; /* خلفية خفيفة للنص المميز */
            padding: 2px 5px;
            border-radius: 3px;
        }
        #signalGraphContainer {
            width: 100%;
            height: 320px; /* زيادة ارتفاع المخطط قليلاً */
            border: 1px solid #d1d9e6;
            background-color: #fdfdfd;
            border-radius: 5px;
        }
        #opRegion.active {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        #opRegion.cutoff {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        #opRegion.saturation {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }
        /* تنسيقات إضافية لتحسين المظهر العام */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #e9ecef; /* لون خلفية أفتح للصفحة */
            margin: 0;
            padding: 0;
        }
        header {
            background-color: #0056b3;
            color: white;
            padding: 1.5em 0;
            text-align: center;
            border-bottom: 5px solid #003d82;
        }
        header h1 {
            margin: 0;
            font-size: 2.2em;
        }
        nav ul {
            background-color: #337ab7;
        }
        nav ul li a {
            color: white;
        }
        nav ul li a:hover {
            background-color: #286090;
        }
        main {
            padding: 20px;
            max-width: 1200px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
        }
        footer {
            background-color: #343a40;
            color: #f8f9fa;
            text-align: center;
            padding: 1.5em 0;
            margin-top: 30px;
            border-top: 5px solid #23272b;
        }
        h2 {
            color: #004085;
            border-bottom: 2px solid #b8daff;
            padding-bottom: 10px;
            margin-top: 25px;
            margin-bottom: 15px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 10px;
            text-align: right;
        }
        th {
            background-color: #e9ecef;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <header>
        <h1>تجربة: مضخم الباعث المشترك (NPN)</h1>
    </header>

    <nav>
        <ul>
            <li><a href="index.html">الرئيسية</a></li>
            <li><a href="simulation.html">بدء المحاكاة</a></li>
            <li><a href="NPN Transistor Behavior Explorer.html">سلوك NPN</a></li>
            <li><a href="NPN BJT Operating Modes Explorer.html">أوضاع تشغيل NPN</a></li>
            <li><a href="NPN Transistor Interactive Explorer.html">المستكشف التفاعلي</a></li>
            <li><a href="Transistor as a Switch.html">الترانزستور كمفتاح</a></li>
        </ul>
    </nav>

    <main>
        <section id="experiment-intro">
            <h2>مقدمة التجربة</h2>
            <p>مضخم الباعث المشترك هو أحد التكوينات الأساسية لمضخمات الترانزستور. يتميز هذا التكوين بقدرته على توفير كسب جيد للجهد والتيار، وعادة ما يقوم بعكس طور إشارة الخرج بالنسبة لإشارة الدخل (قلب الطور 180 درجة).</p>
            <p>في هذه التجربة، سنقوم بمحاكاة دائرة مضخم باعث مشترك بسيطة، ونتحكم في بعض معلمات الدائرة ونلاحظ تأثيرها على نقطة التشغيل (Q-point) وكسب الجهد.</p>
        </section>

        <section id="circuit-diagram-workbench-ce-amplifier" class="circuit-diagram-workbench">
            <h2>رسم دائرة مضخم الباعث المشترك ومنطقة العمل</h2>
            <div class="circuit-diagram-container">
                <p><strong>رسم الدائرة:</strong></p>
                <!-- سيتم استبدال هذا برسم SVG أو صورة للدائرة لاحقًا -->
                <img src="images/placeholder_circuit_ce_amplifier.png" alt="رسم دائرة مضخم الباعث المشترك" class="circuit-diagram-image">
            </div>
            <div class="workbench-container">
                <p><strong>منطقة العمل (Workbench):</strong></p>
                <p><em>(هنا يمكنك تصميم وتعديل دائرة مضخم الباعث المشترك قبل محاكاتها)</em></p>
                <div>
                    <label for="component-select-ce-amp">اختر مكونًا:</label>
                    <select id="component-select-ce-amp">
                        <option value="resistor">مقاومة</option>
                        <option value="capacitor_coupling">مكثف ربط</option>
                        <option value="capacitor_bypass">مكثف تجاوز</option>
                        <option value="transistor_npn">ترانزستور NPN</option>
                        <option value="voltage_source_dc">مصدر جهد مستمر (Vcc)</option>
                        <option value="voltage_source_ac">مصدر إشارة مترددة (Vin)</option>
                        <option value="ground">أرضي</option>
                    </select>
                    <button id="addComponentButtonCeAmp" type="button">إضافة المكون</button>
                </div>
                <div id="interactive-circuit-drawing-area" class="interactive-circuit-area interactive-circuit-area-lg">
                    <p>مساحة رسم دائرة المضخم (قيد التطوير)</p>
                </div>
            </div>
        </section>

        <div class="simulation-container">
            <section class="controls-section">
                <h3>عناصر التحكم بالدائرة:</h3>
                <div>
                    <label for="vcc">جهد المصدر (V<sub>CC</sub>): <span id="vccVal">12</span> V</label>
                    <input type="range" id="vcc" min="5" max="20" step="1" value="12">
                </div>
                <div>
                    <label for="r1">مقاومة R1 (مقسم الجهد): <span id="r1Val">22</span> kΩ</label>
                    <input type="range" id="r1" min="1" max="100" step="1" value="22">
                </div>
                <div>
                    <label for="r2">مقاومة R2 (مقسم الجهد): <span id="r2Val">4.7</span> kΩ</label>
                    <input type="range" id="r2" min="0.1" max="20" step="0.1" value="4.7">
                </div>
                <div>
                    <label for="rc">مقاومة المجمع (R<sub>C</sub>): <span id="rcVal">2.2</span> kΩ</label>
                    <input type="range" id="rc" min="0.1" max="10" step="0.1" value="2.2">
                </div>
                <div>
                    <label for="re">مقاومة الباعث (R<sub>E</sub>): <span id="reVal">1</span> kΩ</label>
                    <input type="range" id="re" min="0.1" max="5" step="0.1" value="1">
                </div>
                <div>
                    <label for="beta">كسب التيار (β أو h<sub>FE</sub>): <span id="betaVal">100</span></label>
                    <input type="range" id="beta" min="50" max="300" step="10" value="100">
                </div>
                 <div>
                    <label for="vin_amplitude">سعة إشارة الدخل (V<sub>in,peak</sub>): <span id="vinAmpVal">0.01</span> V</label>
                    <input type="range" id="vin_amplitude" min="0.001" max="0.1" step="0.001" value="0.01">
                </div>
            </section>

            <section class="output-section">
                <h3>نقطة التشغيل (Q-point) والمخرجات:</h3>
                <p>جهد القاعدة (V<sub>B</sub>): <span id="vbQ">--</span> V</p>
                <p>جهد الباعث (V<sub>E</sub>): <span id="veQ">--</span> V</p>
                <p>جهد المجمع (V<sub>C</sub>): <span id="vcQ">--</span> V</p>
                <p>تيار القاعدة (I<sub>BQ</sub>): <span id="ibQ">--</span> µA</p>
                <p>تيار الباعث (I<sub>EQ</sub>): <span id="ieQ">--</span> mA</p>
                <p>تيار المجمع (I<sub>CQ</sub>): <span id="icQ">--</span> mA</p>
                <p>جهد المجمع-الباعث (V<sub>CEQ</sub>): <span id="vceqQ">--</span> V</p>
                <p><strong>كسب الجهد التقريبي (A<sub>v</sub>): <span id="avApprox">--</span></strong></p>
                <p>منطقة العمل: <span id="opRegion">--</span></p>
            </section>
        </div>

        <section class="visualization-section">
            <h3>تصور إشارة الدخل والخرج:</h3>
            <div id="signalGraphContainer">
                <!-- سيتم الرسم هنا بواسطة Chart.js أو مكتبة مشابهة -->
            </div>
        </section>

        <section id="theory">
            <h2>الأساس النظري لمضخم الباعث المشترك</h2>
            <p>يتم تصميم دائرة انحياز مقسم الجهد (R1 و R2) لتوفير جهد قاعدة مستقر (V<sub>B</sub>). مقاومة الباعث (R<sub>E</sub>) توفر استقرارًا حراريًا وتؤثر على كسب الجهد.</p>
            <h4>حسابات نقطة التشغيل (DC Analysis):</h4>
            <ol>
                <li>جهد القاعدة: \( V_B = V_{CC} \times \frac{R_2}{R_1 + R_2} \)</li>
                <li>جهد الباعث: \( V_E = V_B - V_{BE,on} \) (حيث \( V_{BE,on} \approx 0.7V \))</li>
                <li>تيار الباعث: \( I_{EQ} = \frac{V_E}{R_E} \)</li>
                <li>تيار المجمع: \( I_{CQ} \approx I_{EQ} \) (بافتراض \( \beta \) كبير)</li>
                <li>تيار القاعدة: \( I_{BQ} = \frac{I_{CQ}}{\beta} \)</li>
                <li>جهد المجمع: \( V_C = V_{CC} - I_{CQ} R_C \)</li>
                <li>جهد المجمع-الباعث: \( V_{CEQ} = V_C - V_E = V_{CC} - I_{CQ}(R_C + R_E) \) (إذا \(I_C \approx I_E\))</li>
            </ol>
            <p>للتأكد من أن الترانزستور في المنطقة الفعالة، يجب أن يكون \( V_{CEQ} > V_{CE,sat} \) (عادة \( V_{CE,sat} \approx 0.2V \)) وأن يكون \( V_{CEQ} < V_{CC} \).</p>
            <h4>كسب الجهد (AC Analysis):</h4>
            <p>كسب الجهد التقريبي لمضخم الباعث المشترك بدون مكثف تجاوز الباعث (bypass capacitor) هو: \( A_v \approx -\frac{R_C}{R_E} \).</p>
            <p>إذا كان هناك مكثف تجاوز على التوازي مع \(R_E\)، فإن \(R_E\) يتم استبعاده من حساب الكسب للترددات العالية، ويصبح الكسب \( A_v \approx -\frac{R_C}{r_e'} \)، حيث \( r_e' = \frac{V_T}{I_{EQ}} \approx \frac{25mV}{I_{EQ}} \) عند درجة حرارة الغرفة. هذه التجربة لا تتضمن مكثف تجاوز لتبسيط الأمور.</p>
            <p>الإشارة السالبة في كسب الجهد تعني أن إشارة الخرج مقلوبة الطور (180 درجة) بالنسبة لإشارة الدخل.</p>
        </section>

        <section id="experiment-procedure">
            <h2>إجراءات التجربة المعملية</h2>

            <div class="procedure-container">
                <h3>الهدف من التجربة</h3>
                <p>دراسة خصائص مضخم الباعث المشترك وفهم تأثير مكونات الدائرة على نقطة التشغيل وكسب الجهد.</p>

                <h3>المتطلبات النظرية</h3>
                <p>قبل البدء في هذه التجربة، يجب أن يكون لديك فهم أساسي لما يلي:</p>
                <ul>
                    <li>مبادئ عمل الترانزستور ثنائي القطبية (BJT) وأوضاع تشغيله.</li>
                    <li>مفهوم نقطة التشغيل (Q-point) وأهميتها في تصميم المضخمات.</li>
                    <li>دوائر انحياز الترانزستور، خاصة دائرة انحياز مقسم الجهد.</li>
                    <li>مفهوم كسب الجهد وكيفية حسابه في دوائر المضخمات.</li>
                    <li>مفهوم قلب الطور في مضخم الباعث المشترك.</li>
                </ul>

                <h3>الأدوات والمعدات</h3>
                <p>في هذه التجربة الافتراضية، سنستخدم:</p>
                <ul>
                    <li>محاكي مضخم الباعث المشترك مع عناصر تحكم لضبط معلمات الدائرة.</li>
                    <li>منزلقات تحكم لضبط قيم المقاومات (R1, R2, RC, RE) وجهد المصدر (VCC).</li>
                    <li>منزلق لضبط كسب التيار (β) للترانزستور.</li>
                    <li>منزلق لضبط سعة إشارة الدخل.</li>
                    <li>مؤشرات لقراءة قيم نقطة التشغيل وكسب الجهد.</li>
                    <li>رسم بياني لتصور إشارتي الدخل والخرج.</li>
                </ul>

                <h3>خطوات التجربة</h3>
                <ol>
                    <li><strong>الجزء الأول: دراسة تأثير مقاومات مقسم الجهد على نقطة التشغيل</strong>
                        <ul>
                            <li>اضبط القيم الأولية كما يلي: VCC = 12V، R1 = 22kΩ، R2 = 4.7kΩ، RC = 2.2kΩ، RE = 1kΩ، β = 100.</li>
                            <li>سجل قيم نقطة التشغيل (VB, VE, VC, IB, IC, VCE) في الجدول 1.</li>
                            <li>غيّر قيمة R1 (10kΩ، 22kΩ، 47kΩ، 100kΩ) مع الحفاظ على باقي القيم ثابتة.</li>
                            <li>سجل قيم نقطة التشغيل لكل حالة.</li>
                            <li>أعد R1 إلى 22kΩ، ثم غيّر قيمة R2 (2.2kΩ، 4.7kΩ، 10kΩ، 15kΩ).</li>
                            <li>سجل قيم نقطة التشغيل لكل حالة.</li>
                            <li>لاحظ كيف تؤثر نسبة R2/(R1+R2) على جهد القاعدة VB ونقطة التشغيل.</li>
                        </ul>
                    </li>

                    <li><strong>الجزء الثاني: دراسة تأثير مقاومة المجمع (RC) ومقاومة الباعث (RE) على كسب الجهد</strong>
                        <ul>
                            <li>أعد ضبط القيم الأولية: VCC = 12V، R1 = 22kΩ، R2 = 4.7kΩ، RC = 2.2kΩ، RE = 1kΩ، β = 100.</li>
                            <li>سجل قيمة كسب الجهد (Av) في الجدول 2.</li>
                            <li>غيّر قيمة RC (1kΩ، 2.2kΩ، 3.3kΩ، 4.7kΩ) مع الحفاظ على باقي القيم ثابتة.</li>
                            <li>سجل قيمة كسب الجهد لكل حالة.</li>
                            <li>أعد RC إلى 2.2kΩ، ثم غيّر قيمة RE (0.5kΩ، 1kΩ، 1.5kΩ، 2kΩ).</li>
                            <li>سجل قيمة كسب الجهد لكل حالة.</li>
                            <li>تحقق من العلاقة Av ≈ -RC/RE بمقارنة القيم المحسوبة مع القيم المقاسة.</li>
                        </ul>
                    </li>

                    <li><strong>الجزء الثالث: دراسة تأثير جهد المصدر (VCC) على نقطة التشغيل</strong>
                        <ul>
                            <li>أعد ضبط القيم الأولية: R1 = 22kΩ، R2 = 4.7kΩ، RC = 2.2kΩ، RE = 1kΩ، β = 100.</li>
                            <li>غيّر قيمة VCC (5V، 9V، 12V، 15V، 18V).</li>
                            <li>سجل قيم نقطة التشغيل لكل حالة في الجدول 3.</li>
                            <li>لاحظ كيف يؤثر VCC على جهد القاعدة VB وباقي قيم نقطة التشغيل.</li>
                            <li>حدد منطقة التشغيل (الفعال، التشبع، القطع) لكل حالة.</li>
                        </ul>
                    </li>

                    <li><strong>الجزء الرابع: دراسة تأثير سعة إشارة الدخل على شكل إشارة الخرج</strong>
                        <ul>
                            <li>أعد ضبط القيم الأولية: VCC = 12V، R1 = 22kΩ، R2 = 4.7kΩ، RC = 2.2kΩ، RE = 1kΩ، β = 100.</li>
                            <li>ابدأ بسعة إشارة دخل صغيرة (0.01V) ولاحظ شكل إشارة الخرج على الرسم البياني.</li>
                            <li>زد سعة إشارة الدخل تدريجياً (0.01V، 0.02V، 0.05V، 0.1V).</li>
                            <li>لاحظ متى تبدأ إشارة الخرج في التشوه (clipping) وسجل سعة إشارة الدخل عند هذه النقطة.</li>
                            <li>احسب أقصى سعة لإشارة الخرج غير المشوهة نظرياً باستخدام قيم نقطة التشغيل.</li>
                            <li>قارن بين القيمة النظرية والقيمة المقاسة.</li>
                        </ul>
                    </li>

                    <li><strong>الجزء الخامس: دراسة تأثير كسب التيار (β) على أداء المضخم</strong>
                        <ul>
                            <li>أعد ضبط القيم الأولية: VCC = 12V، R1 = 22kΩ، R2 = 4.7kΩ، RC = 2.2kΩ، RE = 1kΩ.</li>
                            <li>غيّر قيمة β (50، 100، 150، 200، 250).</li>
                            <li>سجل قيم نقطة التشغيل وكسب الجهد لكل حالة في الجدول 4.</li>
                            <li>لاحظ مدى تأثير β على نقطة التشغيل وكسب الجهد.</li>
                            <li>ناقش أهمية تصميم دائرة انحياز مستقرة لا تتأثر كثيراً بتغير β.</li>
                        </ul>
                    </li>
                </ol>

                <h3>تحليل النتائج</h3>
                <p>بعد إكمال التجربة، قم بتحليل النتائج من خلال الإجابة على الأسئلة التالية:</p>
                <ol>
                    <li>كيف تؤثر نسبة مقاومات مقسم الجهد (R1 و R2) على نقطة التشغيل؟</li>
                    <li>ما هي العلاقة بين RC و RE وكسب الجهد؟ هل تتوافق النتائج العملية مع العلاقة النظرية Av ≈ -RC/RE؟</li>
                    <li>لماذا يكون كسب الجهد سالباً في مضخم الباعث المشترك؟ وما أهمية ذلك في التطبيقات العملية؟</li>
                    <li>ما هي العوامل التي تحدد أقصى سعة لإشارة الخرج غير المشوهة؟</li>
                    <li>كيف يمكن تحسين استقرار نقطة التشغيل ضد تغيرات درجة الحرارة وتغيرات β؟</li>
                    <li>قارن بين مضخم الباعث المشترك ومضخمات الترانزستور الأخرى (مثل المجمع المشترك والقاعدة المشتركة) من حيث كسب الجهد والتيار والمقاومة.</li>
                </ol>

                <h3>الاستنتاجات</h3>
                <p>اكتب استنتاجاتك حول:</p>
                <ul>
                    <li>العوامل المؤثرة على أداء مضخم الباعث المشترك.</li>
                    <li>أهمية اختيار نقطة التشغيل المناسبة.</li>
                    <li>المفاضلة بين كسب الجهد العالي واستقرار نقطة التشغيل.</li>
                    <li>التطبيقات العملية لمضخم الباعث المشترك.</li>
                </ul>
            </div>
        </section>
    </main>

    <footer>
        <p>&copy; 2024 معمل الترانزستور الافتراضي. جميع الحقوق محفوظة.</p>
    </footer>

    <!-- تحميل مكتبة Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const VBE_ON = 0.7; // فولت
            const VCE_SAT = 0.2; // فولت

            const sliders = {
                vcc: document.getElementById('vcc'),
                r1: document.getElementById('r1'),
                r2: document.getElementById('r2'),
                rc: document.getElementById('rc'),
                re: document.getElementById('re'),
                beta: document.getElementById('beta'),
                vin_amplitude: document.getElementById('vin_amplitude')
            };

            const spans = {
                vccVal: document.getElementById('vccVal'),
                r1Val: document.getElementById('r1Val'),
                r2Val: document.getElementById('r2Val'),
                rcVal: document.getElementById('rcVal'),
                reVal: document.getElementById('reVal'),
                betaVal: document.getElementById('betaVal'),
                vinAmpVal: document.getElementById('vinAmpVal'),
                vbQ: document.getElementById('vbQ'),
                veQ: document.getElementById('veQ'),
                vcQ: document.getElementById('vcQ'),
                ibQ: document.getElementById('ibQ'),
                ieQ: document.getElementById('ieQ'),
                icQ: document.getElementById('icQ'),
                vceqQ: document.getElementById('vceqQ'),
                avApprox: document.getElementById('avApprox'),
                opRegion: document.getElementById('opRegion')
            };

            let signalChart;

            function calculateAndDisplay() {
                let vcc = parseFloat(sliders.vcc.value);
                let r1_kohm = parseFloat(sliders.r1.value);
                let r2_kohm = parseFloat(sliders.r2.value);
                let rc_kohm = parseFloat(sliders.rc.value);
                let re_kohm = parseFloat(sliders.re.value);
                let beta = parseFloat(sliders.beta.value);
                let vin_amp_V = parseFloat(sliders.vin_amplitude.value);

                spans.vccVal.textContent = vcc.toFixed(1);
                spans.r1Val.textContent = r1_kohm.toFixed(1);
                spans.r2Val.textContent = r2_kohm.toFixed(1);
                spans.rcVal.textContent = rc_kohm.toFixed(1);
                spans.reVal.textContent = re_kohm.toFixed(1);
                spans.betaVal.textContent = beta.toFixed(0);
                spans.vinAmpVal.textContent = vin_amp_V.toFixed(3);

                // حسابات نقطة التشغيل
                let vb_q = vcc * (r2_kohm / (r1_kohm + r2_kohm));
                let ve_q = vb_q - VBE_ON;
                if (ve_q < 0) ve_q = 0; // لا يمكن أن يكون جهد الباعث سالبًا في هذا السياق

                let ie_q_mA = (re_kohm > 0) ? (ve_q / re_kohm) : 0;
                if (ie_q_mA < 0) ie_q_mA = 0;

                let ic_q_mA = (beta / (beta + 1)) * ie_q_mA; // أدق قليلاً من IC = IE
                let ib_q_uA = (ic_q_mA / beta) * 1000; // تحويل إلى ميكرو أمبير

                let vc_q = vcc - (ic_q_mA * rc_kohm);
                let vceq_q = vc_q - ve_q;

                let region = 'الفعال (Active)';
                if (vceq_q < VCE_SAT) {
                    region = 'التشبع (Saturation)';
                    // إعادة حساب للتشبع (تبسيط: نفترض VCE=VCE_SAT)
                    vceq_q = VCE_SAT;
                    ic_q_mA = (vcc - VCE_SAT) / (rc_kohm + re_kohm); // إذا افترضنا أن VCE هو VCE_SAT
                    if (ic_q_mA < 0) ic_q_mA = 0;
                    vc_q = vcc - ic_q_mA * rc_kohm;
                    ve_q = ic_q_mA * re_kohm;
                    vb_q = ve_q + VBE_ON;
                    ib_q_uA = (ic_q_mA / beta) * 1000;
                } else if (vb_q < VBE_ON || ie_q_mA <= 0) { // أو ib_q_uA <= 0
                    region = 'القطع (Cutoff)';
                    ic_q_mA = 0;
                    ib_q_uA = 0;
                    ie_q_mA = 0;
                    vc_q = vcc;
                    ve_q = 0;
                    vb_q = vcc * (r2_kohm / (r1_kohm + r2_kohm)); // Vb لا يزال موجودًا
                    vceq_q = vcc;
                }

                spans.vbQ.textContent = vb_q.toFixed(2);
                spans.veQ.textContent = ve_q.toFixed(2);
                spans.vcQ.textContent = vc_q.toFixed(2);
                spans.ibQ.textContent = ib_q_uA.toFixed(2);
                spans.ieQ.textContent = ie_q_mA.toFixed(2);
                spans.icQ.textContent = ic_q_mA.toFixed(2);
                spans.vceqQ.textContent = vceq_q.toFixed(2);
                spans.opRegion.textContent = region;

                // كسب الجهد التقريبي
                let av_approx = (re_kohm > 0 && region === 'الفعال (Active)') ? (-rc_kohm / re_kohm) : 0;
                spans.avApprox.textContent = av_approx.toFixed(2);

                updateSignalGraph(vin_amp_V, av_approx, vc_q, region);
            }

            function updateSignalGraph(vin_amplitude, gain, v_offset, region) {
                const canvas = document.getElementById('signalGraphContainer');
                const ctx = canvas.getContext('2d'); // نحصل على سياق الرسم ثنائي الأبعاد

                if (signalChart) {
                    signalChart.destroy(); // تدمير المخطط القديم إذا كان موجودًا
                }

                const numPoints = 100;
                const labels = Array.from({length: numPoints}, (_, i) => (i * 2 * Math.PI / numPoints).toFixed(2));
                const vin_data = labels.map(x => vin_amplitude * Math.sin(parseFloat(x)));

                let vout_data;
                if (region === 'الفعال (Active)') {
                     vout_data = vin_data.map(v_in => {
                        let v_out_signal = v_in * gain; // إشارة الخرج المترددة
                        let v_out_dc_plus_ac = v_offset + v_out_signal; // إضافة مركبة DC
                        // قص الإشارة إذا تجاوزت حدود VCC أو الأرضي (تبسيط)
                        v_out_dc_plus_ac = Math.max(0, Math.min(parseFloat(sliders.vcc.value), v_out_dc_plus_ac));
                        return v_out_dc_plus_ac;
                    });
                } else if (region === 'التشبع (Saturation)') {
                    // في التشبع، الخرج ثابت عند VCE_SAT + VEQ (أو VCQ)
                    vout_data = Array(numPoints).fill(v_offset);
                } else { // القطع
                    // في القطع، الخرج ثابت عند VCC (أو VCQ)
                    vout_data = Array(numPoints).fill(v_offset);
                }

                // إشارة الدخل هي إشارة AC فقط، لذا نرسمها حول الصفر
                const vin_plot_data = vin_data.map(val => val);

                signalChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [
                            {
                                label: 'إشارة الدخل (Vin)',
                                data: vin_plot_data,
                                borderColor: 'blue',
                                borderWidth: 1.5,
                                fill: false,
                                pointRadius: 0, // إخفاء النقاط
                                yAxisID: 'yVin'
                            },
                            {
                                label: 'إشارة الخرج (Vout)',
                                data: vout_data,
                                borderColor: 'red',
                                borderWidth: 1.5,
                                fill: false,
                                pointRadius: 0, // إخفاء النقاط
                                yAxisID: 'yVout'
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        animation: { duration: 0 }, // تعطيل الرسوم المتحركة للتحديث الفوري
                        scales: {
                            x: {
                                display: true,
                                title: { display: true, text: 'الزمن (نسبي)' }
                            },
                            yVin: { // محور Y لإشارة الدخل
                                type: 'linear',
                                display: true,
                                position: 'left',
                                title: { display: true, text: 'Vin (V)' },
                                suggestedMin: -vin_amplitude * 1.2,
                                suggestedMax: vin_amplitude * 1.2
                            },
                            yVout: { // محور Y لإشارة الخرج
                                type: 'linear',
                                display: true,
                                position: 'right',
                                title: { display: true, text: 'Vout (V)' },
                                suggestedMin: 0,
                                suggestedMax: parseFloat(sliders.vcc.value),
                                grid: { drawOnChartArea: false } // عدم رسم شبكة هذا المحور على منطقة الرسم الرئيسية
                            }
                        },
                        plugins: {
                            tooltip: {
                                mode: 'index',
                                intersect: false,
                            }
                        }
                    }
                });
            }

            Object.values(sliders).forEach(slider => {
                slider.addEventListener('input', calculateAndDisplay);
            });

            // الحساب والعرض الأولي
            calculateAndDisplay();
        });
    </script>
<script src="js/main.js" defer></script>
<script>
document.addEventListener('DOMContentLoaded', function () {
    // ... existing script for simulation and graph ...

    const addButtonCeAmp = document.getElementById('addComponentButtonCeAmp');
    const componentSelectCeAmp = document.getElementById('component-select-ce-amp');

    if (addButtonCeAmp && componentSelectCeAmp) {
        addButtonCeAmp.addEventListener('click', function() {
            const selectedComponent = componentSelectCeAmp.value;
            if (typeof addComponent === 'function') {
                addComponent(selectedComponent);
            } else {
                console.error('addComponent function is not defined. Make sure js/main.js is loaded.');
            }
        });
    }

    // Initialize simulation logic from the original script if it was here
    // For example, if there was a setupAmplifierSimulation() function:
    // if (typeof setupAmplifierSimulation === 'function') { setupAmplifierSimulation(); }
});
</script>
</body>
</html>