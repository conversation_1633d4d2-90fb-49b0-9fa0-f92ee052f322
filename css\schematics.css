/* Schematic Diagrams Styles */

/* Schematics Container */
.schematics-container {
    margin: 30px 0;
}

/* Schematics Grid */
.schematics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

/* Schematic Card */
.schematic-card {
    background-color: var(--card-bg);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px var(--shadow-color);
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
}

.schematic-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-hover-shadow);
}

/* Schematic Badge */
.schematic-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--gradient-accent);
    color: var(--dark-color);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

/* Schematic Preview */
.schematic-preview {
    position: relative;
    height: 200px;
    overflow: hidden;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
}

.schematic-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    transition: transform 0.5s ease;
}

.schematic-card:hover .schematic-preview img {
    transform: scale(1.05);
}

/* Schematic Overlay */
.schematic-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.schematic-card:hover .schematic-overlay {
    opacity: 1;
}

/* Schematic Info */
.schematic-info {
    padding: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.schematic-info h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1.2rem;
    color: var(--primary-color);
}

.schematic-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.schematic-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
    background-color: var(--light-color);
    padding: 5px 10px;
    border-radius: 15px;
    color: var(--primary-color);
}

.schematic-meta span i {
    color: var(--secondary-color);
}

.schematic-info p {
    margin-bottom: 15px;
    font-size: 0.95rem;
    color: var(--text-color);
    opacity: 0.9;
    line-height: 1.5;
    flex-grow: 1;
}

.schematic-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

/* Schematic Modal */
.schematic-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.schematic-modal.active {
    opacity: 1;
    visibility: visible;
}

.schematic-modal-content {
    background-color: var(--card-bg);
    border-radius: 15px;
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    overflow: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    position: relative;
    display: flex;
    flex-direction: column;
}

.schematic-modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.schematic-modal-header h3 {
    margin: 0;
    color: var(--primary-color);
}

.schematic-modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-color);
    cursor: pointer;
    transition: color 0.3s ease;
}

.schematic-modal-close:hover {
    color: var(--accent-color);
}

.schematic-modal-body {
    padding: 20px;
    overflow: auto;
}

.schematic-modal-image-container {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
}

.schematic-modal-image {
    max-width: 100%;
    max-height: 500px;
}

.schematic-modal-description {
    margin-bottom: 20px;
}

.schematic-modal-components {
    background-color: var(--light-color);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.schematic-modal-components h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.component-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
}

.component-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px;
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 0.5);
}

.component-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--secondary-color);
}

.schematic-modal-footer {
    padding: 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

/* Schematic Categories */
.schematic-categories {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 30px;
}

.schematic-category {
    padding: 8px 20px;
    background-color: var(--light-color);
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.schematic-category:hover {
    background-color: var(--secondary-color);
    color: white;
    transform: translateY(-3px);
}

.schematic-category.active {
    background: var(--gradient-primary);
    color: white;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .schematics-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
    
    .component-list {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .schematics-grid {
        grid-template-columns: 1fr;
    }
}
