<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معمل الدوائر الإلكترونية الافتراضي - الترانزستور</title>
    <meta name="description" content="معمل افتراضي تفاعلي لتعلم وفهم مبادئ عمل الترانزستور والدوائر الإلكترونية. تطوير د. محمد يعقوب إسماعيل">
    <meta name="keywords" content="ترانزستور, دوائر إلكترونية, معمل افتراضي, تعليم إلكترونيات, محاكاة دوائر, محمد يعقوب إسماعيل">
    <meta name="author" content="د. محمد يعقوب إسماعيل">
    <meta name="contact" content="<EMAIL>">
    <meta name="copyright" content="د. محمد يعقوب إسماعيل">
    <meta property="og:title" content="معمل الدوائر الإلكترونية الافتراضي - الترانزستور">
    <meta property="og:description" content="معمل افتراضي تفاعلي لتعلم وفهم مبادئ عمل الترانزستور والدوائر الإلكترونية. تطوير د. محمد يعقوب إسماعيل">
    <meta property="og:image" content="images/components/transistor-npn.png">
    <meta property="og:url" content="https://electronics-lab.example.com">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/theme.css">
    <link rel="stylesheet" href="css/enhanced.css">
    <link rel="stylesheet" href="css/templates.css">
    <link rel="stylesheet" href="css/schematics.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="icon" href="images/components/transistor-npn.png" type="image/png">
</head>
<body>
    <div class="top-bar">
        <div class="container">
            <div class="top-bar-content">
                <div class="top-bar-contact">
                    <span><i class="far fa-envelope"></i> <EMAIL></span>
                    <span><i class="fas fa-phone-alt"></i> +249912867327 / +966538076790</span>
                </div>
                <div class="top-bar-author">
                    <span><i class="fas fa-user-graduate"></i> د. محمد يعقوب إسماعيل</span>
                </div>
            </div>
        </div>
    </div>

    <header>
        <div class="container">
            <h1>معمل الدوائر الإلكترونية الافتراضي</h1>
            <div class="theme-toggle">
                <button id="theme-toggle-btn" type="button" title="تبديل الوضع المظلم/الفاتح">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </div>
    </header>

    <nav>
        <div class="container">
            <ul>
                <li><a href="index.html" class="active">الرئيسية</a></li>
                <li><a href="simulation.html">المحاكاة</a></li>
                <li><a href="workbench.html">مساحة العمل</a></li>
                <li><a href="lab-notes.html">ملاحظات المعمل</a></li>
                <li><a href="#" id="nav-menu-toggle" class="mobile-menu-toggle" title="القائمة"><i class="fas fa-bars"></i><span class="sr-only">القائمة</span></a></li>
            </ul>
        </div>
    </nav>

    <main>
        <section id="hero-section">
            <div class="container">
                <div class="hero-content">
                    <div class="hero-text">
                        <h2>استكشف عالم الترانزستور بطريقة تفاعلية</h2>
                        <p>مرحباً بك في معمل الدوائر الإلكترونية الافتراضي، بيئة تعليمية متكاملة لفهم مبادئ عمل الترانزستور والدوائر الإلكترونية من خلال تجارب تفاعلية ومحاكاة واقعية.</p>
                        <div class="hero-features">
                            <div class="feature"><i class="fas fa-flask"></i> تجارب تفاعلية</div>
                            <div class="feature"><i class="fas fa-microchip"></i> محاكاة واقعية</div>
                            <div class="feature"><i class="fas fa-graduation-cap"></i> شرح مفصل</div>
                        </div>
                        <div class="hero-buttons">
                            <a href="#featured-experiments" class="button">استكشف التجارب</a>
                            <a href="workbench.html" class="button button-primary">ابدأ تصميم الدوائر</a>
                            <a href="#learning-path" class="button button-secondary"><i class="fas fa-road"></i> مسار التعلم</a>
                        </div>
                    </div>
                    <div class="hero-image">
                        <img src="images/hero-transistor-circuit.png" alt="دائرة ترانزستور تفاعلية" onerror="this.src='images/placeholder_circuit_workbench.svg'">
                    </div>
                </div>
                <div class="hero-stats">
                    <div class="stat">
                        <span class="stat-number">20+</span>
                        <span class="stat-label">تجربة تفاعلية</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">1000+</span>
                        <span class="stat-label">مستخدم نشط</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">50+</span>
                        <span class="stat-label">مكون إلكتروني</span>
                    </div>
                </div>
            </div>
        </section>

        <section id="intro" class="container">
            <h2>مقدمة عن الترانزستور</h2>
            <p>الترانزستور هو عنصر إلكتروني أساسي يُستخدم لتضخيم الإشارات الكهربائية أو للتحكم في تدفق التيار الكهربائي (كمفتاح). يعتبر الترانزستور حجر الزاوية في الإلكترونيات الحديثة، حيث يوجد في كل الأجهزة الإلكترونية تقريباً من الهواتف الذكية إلى الحواسيب العملاقة.</p>
            <p>يهدف هذا المعمل الافتراضي إلى توفير بيئة تفاعلية لتعلم وفهم مبادئ عمل الترانزستور للمستويات المبتدئة والمتوسطة، مع إمكانية تصميم ومحاكاة الدوائر الإلكترونية بشكل عملي.</p>
        </section>

        <section id="search-section" class="container">
            <h2>ابحث عن التجارب</h2>
            <div class="search-container">
                <div class="search-input-wrapper">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" id="experiment-search" placeholder="ابحث عن تجربة...">
                    <button type="button" id="search-button">بحث</button>
                </div>
            </div>

            <div class="advanced-search-toggle">
                <button type="button" id="advanced-search-toggle" class="text-button">
                    <i class="fas fa-sliders-h"></i> خيارات بحث متقدمة
                </button>
            </div>

            <div id="advanced-search-options" class="advanced-search-options">
                <div class="filter-group">
                    <h3>المستوى</h3>
                    <div class="filter-options">
                        <label class="filter-option"><input type="checkbox" name="level" value="beginner" checked> مبتدئ</label>
                        <label class="filter-option"><input type="checkbox" name="level" value="intermediate" checked> متوسط</label>
                        <label class="filter-option"><input type="checkbox" name="level" value="advanced" checked> متقدم</label>
                    </div>
                </div>

                <div class="filter-group">
                    <h3>المدة</h3>
                    <div class="filter-options">
                        <label class="filter-option"><input type="checkbox" name="duration" value="short" checked> قصيرة (< 15 دقيقة)</label>
                        <label class="filter-option"><input type="checkbox" name="duration" value="medium" checked> متوسطة (15-30 دقيقة)</label>
                        <label class="filter-option"><input type="checkbox" name="duration" value="long" checked> طويلة (> 30 دقيقة)</label>
                    </div>
                </div>
            </div>

            <div class="category-filters">
                <span class="category-filter active" data-category="all">جميع التجارب</span>
                <span class="category-filter" data-category="npn">NPN</span>
                <span class="category-filter" data-category="pnp">PNP</span>
                <span class="category-filter" data-category="amplifier">المضخمات</span>
                <span class="category-filter" data-category="switch">المفاتيح</span>
                <span class="category-filter" data-category="basics">أساسيات</span>
                <span class="category-filter" data-category="applications">تطبيقات عملية</span>
                <span class="category-filter" data-category="interactive">تفاعلية</span>
            </div>

            <div class="search-results-info">
                <span id="results-count">عرض جميع التجارب</span>
                <div class="sort-options">
                    <label for="sort-experiments">ترتيب حسب:</label>
                    <select id="sort-experiments">
                        <option value="newest">الأحدث</option>
                        <option value="popular">الأكثر شعبية</option>
                        <option value="az">أبجدي (أ-ي)</option>
                        <option value="difficulty">مستوى الصعوبة</option>
                    </select>
                </div>
            </div>
        </section>

        <section id="featured-experiments" class="featured-section">
            <div class="container">
                <div class="section-header">
                    <h2>التجارب المميزة</h2>
                    <a href="#all-experiments" class="view-all">عرض جميع التجارب <i class="fas fa-arrow-left"></i></a>
                </div>

                <div class="experiment-grid">
                    <div class="experiment-card" data-category="npn basics" data-level="beginner" data-duration="medium">
                        <div class="experiment-badge"><i class="fas fa-star"></i> الأكثر شعبية</div>
                        <img src="images/placeholder_circuit_npn_modes.png" alt="مستكشف أوضاع تشغيل NPN" class="experiment-thumbnail">
                        <div class="experiment-meta">
                            <span class="experiment-level beginner"><i class="fas fa-signal-1"></i> مبتدئ</span>
                            <span class="experiment-duration"><i class="far fa-clock"></i> 20 دقيقة</span>
                        </div>
                        <h3><a href="NPN BJT Operating Modes Explorer.html">مستكشف أوضاع تشغيل NPN</a></h3>
                        <p>فهم مناطق القطع، الفعال، والتشبع للترانزستور NPN من خلال تجربة تفاعلية.</p>
                        <div class="experiment-details">
                            <div class="detail-item"><i class="fas fa-graduation-cap"></i> المهارات: فهم خصائص الترانزستور، تحليل منحنيات الخرج</div>
                            <div class="detail-item"><i class="fas fa-tools"></i> الأدوات: محاكي تفاعلي، راسم بياني</div>
                            <div class="detail-item"><i class="fas fa-tasks"></i> التطبيقات: دوائر التضخيم، دوائر التحكم</div>
                        </div>
                        <div class="experiment-actions">
                            <a href="NPN BJT Operating Modes Explorer.html" class="btn"><i class="fas fa-play"></i> بدء التجربة</a>
                            <button type="button" class="btn-icon" title="حفظ للمشاهدة لاحقاً"><i class="far fa-bookmark"></i></button>
                            <button type="button" class="btn-icon" title="مشاركة"><i class="fas fa-share-alt"></i></button>
                        </div>
                    </div>

                    <div class="experiment-card" data-category="amplifier" data-level="intermediate" data-duration="medium">
                        <div class="experiment-badge new-badge"><i class="fas fa-certificate"></i> جديد</div>
                        <img src="images/placeholder_circuit_ce_amplifier.png" alt="مضخم الباعث المشترك" class="experiment-thumbnail">
                        <div class="experiment-meta">
                            <span class="experiment-level intermediate"><i class="fas fa-signal-2"></i> متوسط</span>
                            <span class="experiment-duration"><i class="far fa-clock"></i> 25 دقيقة</span>
                        </div>
                        <h3><a href="Common Emitter Amplifier.html">مضخم الباعث المشترك</a></h3>
                        <p>فهم كيفية عمل الترانزستور NPN كمضخم للإشارة وحساب كسب الجهد والتيار.</p>
                        <div class="experiment-details">
                            <div class="detail-item"><i class="fas fa-graduation-cap"></i> المهارات: تصميم دوائر التضخيم، حساب كسب الجهد والتيار</div>
                            <div class="detail-item"><i class="fas fa-tools"></i> الأدوات: محاكي دوائر، راسم إشارة، مولد إشارة</div>
                            <div class="detail-item"><i class="fas fa-tasks"></i> التطبيقات: مكبرات الصوت، معالجة الإشارات</div>
                        </div>
                        <div class="experiment-actions">
                            <a href="Common Emitter Amplifier.html" class="btn"><i class="fas fa-play"></i> بدء التجربة</a>
                            <button type="button" class="btn-icon" title="حفظ للمشاهدة لاحقاً"><i class="far fa-bookmark"></i></button>
                            <button type="button" class="btn-icon" title="مشاركة"><i class="fas fa-share-alt"></i></button>
                        </div>
                    </div>

                    <div class="experiment-card" data-category="switch" data-level="beginner" data-duration="short">
                        <img src="images/placeholder_circuit_transistor_switch.png" alt="الترانزستور كمفتاح" class="experiment-thumbnail">
                        <div class="experiment-meta">
                            <span class="experiment-level beginner"><i class="fas fa-signal-1"></i> مبتدئ</span>
                            <span class="experiment-duration"><i class="far fa-clock"></i> 15 دقيقة</span>
                        </div>
                        <h3><a href="Transistor as a Switch.html">الترانزستور كمفتاح</a></h3>
                        <p>استكشاف كيف يمكن استخدام الترانزستور للتحكم في الدوائر كمفتاح إلكتروني.</p>
                        <div class="experiment-details">
                            <div class="detail-item"><i class="fas fa-graduation-cap"></i> المهارات: تصميم دوائر التحكم، فهم مناطق القطع والتشبع</div>
                            <div class="detail-item"><i class="fas fa-tools"></i> الأدوات: محاكي تفاعلي، مصابيح LED، مقاومات</div>
                            <div class="detail-item"><i class="fas fa-tasks"></i> التطبيقات: دوائر التحكم، أنظمة الإنذار، التحكم بالأجهزة</div>
                        </div>
                        <div class="experiment-actions">
                            <a href="Transistor as a Switch.html" class="btn"><i class="fas fa-play"></i> بدء التجربة</a>
                            <button type="button" class="btn-icon" title="حفظ للمشاهدة لاحقاً"><i class="far fa-bookmark"></i></button>
                            <button type="button" class="btn-icon" title="مشاركة"><i class="fas fa-share-alt"></i></button>
                        </div>
                    </div>

                    <div class="experiment-card" data-category="pnp" data-level="intermediate" data-duration="medium">
                        <div class="experiment-badge new-badge"><i class="fas fa-certificate"></i> جديد</div>
                        <img src="images/placeholder_circuit_pnp_regions.png" alt="مستكشف أوضاع تشغيل PNP" class="experiment-thumbnail">
                        <div class="experiment-meta">
                            <span class="experiment-level intermediate"><i class="fas fa-signal-2"></i> متوسط</span>
                            <span class="experiment-duration"><i class="far fa-clock"></i> 20 دقيقة</span>
                        </div>
                        <h3><a href="PNP BJT Operating Regions Explorer.html">مستكشف أوضاع تشغيل PNP</a></h3>
                        <p>استكشاف سلوك الترانزستور PNP في مختلف أوضاع التشغيل مع مقارنة بالنوع NPN.</p>
                        <div class="experiment-details">
                            <div class="detail-item"><i class="fas fa-graduation-cap"></i> المهارات: فهم الفرق بين PNP و NPN، تحليل منحنيات الخرج</div>
                            <div class="detail-item"><i class="fas fa-tools"></i> الأدوات: محاكي تفاعلي، راسم بياني، مقارن أداء</div>
                            <div class="detail-item"><i class="fas fa-tasks"></i> التطبيقات: دوائر التضخيم التكميلية، مصادر التيار</div>
                        </div>
                        <div class="experiment-actions">
                            <a href="PNP BJT Operating Regions Explorer.html" class="btn"><i class="fas fa-play"></i> بدء التجربة</a>
                            <button type="button" class="btn-icon" title="حفظ للمشاهدة لاحقاً"><i class="far fa-bookmark"></i></button>
                            <button type="button" class="btn-icon" title="مشاركة"><i class="fas fa-share-alt"></i></button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="learning-path" class="container">
            <div class="section-header">
                <h2>مسار التعلم</h2>
                <p>اتبع هذا المسار التعليمي المنظم لفهم الترانزستور من الأساسيات إلى التطبيقات المتقدمة</p>
            </div>

            <div class="learning-path-container">
                <div class="learning-path-timeline">
                    <div class="learning-path-item active">
                        <div class="path-item-number">1</div>
                        <div class="path-item-content">
                            <h3>أساسيات الترانزستور</h3>
                            <p>تعرف على المبادئ الأساسية للترانزستور وتركيبه الداخلي</p>
                            <div class="path-item-resources">
                                <a href="Basic TRANSISTOR OPERATION .html" class="path-resource">
                                    <i class="fas fa-flask"></i> تجربة: أساسيات عمل الترانزستور
                                </a>
                                <a href="Transistor Water Tap Analogy.html" class="path-resource">
                                    <i class="fas fa-lightbulb"></i> تشبيه الترانزستور بصنبور الماء
                                </a>
                            </div>
                            <a href="Basic TRANSISTOR OPERATION .html" class="btn btn-sm">ابدأ هنا</a>
                        </div>
                    </div>

                    <div class="learning-path-item">
                        <div class="path-item-number">2</div>
                        <div class="path-item-content">
                            <h3>أوضاع تشغيل الترانزستور NPN</h3>
                            <p>فهم مناطق القطع، الفعال، والتشبع للترانزستور NPN</p>
                            <div class="path-item-resources">
                                <a href="NPN BJT Operating Modes Explorer.html" class="path-resource">
                                    <i class="fas fa-flask"></i> تجربة: مستكشف أوضاع تشغيل NPN
                                </a>
                            </div>
                            <span class="path-completion">اكتمال المرحلة السابقة مطلوب</span>
                        </div>
                    </div>

                    <div class="learning-path-item">
                        <div class="path-item-number">3</div>
                        <div class="path-item-content">
                            <h3>تطبيقات الترانزستور</h3>
                            <p>استكشاف استخدامات الترانزستور كمضخم ومفتاح</p>
                            <div class="path-item-resources">
                                <a href="Common Emitter Amplifier.html" class="path-resource">
                                    <i class="fas fa-flask"></i> تجربة: مضخم الباعث المشترك
                                </a>
                                <a href="Transistor as a Switch.html" class="path-resource">
                                    <i class="fas fa-flask"></i> تجربة: الترانزستور كمفتاح
                                </a>
                            </div>
                            <span class="path-completion">اكتمال المرحلة السابقة مطلوب</span>
                        </div>
                    </div>

                    <div class="learning-path-item">
                        <div class="path-item-number">4</div>
                        <div class="path-item-content">
                            <h3>الترانزستور PNP</h3>
                            <p>فهم الاختلافات بين ترانزستور NPN و PNP</p>
                            <div class="path-item-resources">
                                <a href="PNP BJT Operating Regions Explorer.html" class="path-resource">
                                    <i class="fas fa-flask"></i> تجربة: مستكشف أوضاع تشغيل PNP
                                </a>
                            </div>
                            <span class="path-completion">اكتمال المرحلة السابقة مطلوب</span>
                        </div>
                    </div>

                    <div class="learning-path-item">
                        <div class="path-item-number">5</div>
                        <div class="path-item-content">
                            <h3>تصميم الدوائر المتقدمة</h3>
                            <p>تصميم وتحليل دوائر الترانزستور المتقدمة</p>
                            <div class="path-item-resources">
                                <a href="BJT Common-Emitter Circuit Analyzer.html" class="path-resource">
                                    <i class="fas fa-flask"></i> تجربة: محلل دائرة الباعث المشترك
                                </a>
                                <a href="workbench.html" class="path-resource">
                                    <i class="fas fa-tools"></i> مساحة العمل
                                </a>
                            </div>
                            <span class="path-completion">اكتمال المرحلة السابقة مطلوب</span>
                        </div>
                    </div>
                </div>

                <div class="learning-path-progress">
                    <div class="progress-bar">
                        <div class="progress-fill progress-20"></div>
                    </div>
                    <div class="progress-text">اكتملت 1 من 5 مراحل</div>
                </div>
            </div>
        </section>

        <section id="latest-updates" class="container">
            <div class="section-header">
                <h2>آخر التحديثات والميزات الجديدة</h2>
                <a href="#" class="view-all">عرض جميع التحديثات <i class="fas fa-arrow-left"></i></a>
            </div>

            <div class="updates-grid">
                <div class="update-card">
                    <div class="update-icon"><i class="fas fa-pencil-ruler"></i></div>
                    <div class="update-content">
                        <h3>تحسين مساحة رسم الدوائر!</h3>
                        <p>تم تحديث جميع التجارب التفاعلية الآن بميزة جديدة تسمح لك بإضافة المكونات مباشرة إلى مساحة رسم الدائرة. جربها الآن لجعل تجربتك التعليمية أكثر تفاعلية وعملية.</p>
                        <div class="update-meta">
                            <span class="update-date"><i class="far fa-calendar-alt"></i> يوليو 2024</span>
                            <a href="#" class="update-link">قراءة المزيد <i class="fas fa-angle-left"></i></a>
                        </div>
                    </div>
                </div>

                <div class="update-card">
                    <div class="update-icon"><i class="fas fa-microchip"></i></div>
                    <div class="update-content">
                        <h3>إضافة محاكاة الترانزستور PNP</h3>
                        <p>تمت إضافة تجارب جديدة لمحاكاة سلوك الترانزستور من نوع PNP ومقارنته بالنوع NPN. استكشف الاختلافات بين النوعين وكيفية استخدامهما في الدوائر المختلفة.</p>
                        <div class="update-meta">
                            <span class="update-date"><i class="far fa-calendar-alt"></i> يونيو 2024</span>
                            <a href="#" class="update-link">قراءة المزيد <i class="fas fa-angle-left"></i></a>
                        </div>
                    </div>
                </div>

                <div class="update-card">
                    <div class="update-icon"><i class="fas fa-mobile-alt"></i></div>
                    <div class="update-content">
                        <h3>تحسين تجربة المستخدم على الأجهزة المحمولة</h3>
                        <p>تم تحسين واجهة المستخدم لتعمل بشكل أفضل على الأجهزة المحمولة، مما يتيح لك الوصول إلى التجارب والمحاكاة من أي مكان وفي أي وقت.</p>
                        <div class="update-meta">
                            <span class="update-date"><i class="far fa-calendar-alt"></i> مايو 2024</span>
                            <a href="#" class="update-link">قراءة المزيد <i class="fas fa-angle-left"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="all-experiments" class="container">
            <div class="section-header">
                <h2>جميع التجارب المتاحة</h2>
                <div class="experiment-count">
                    <span id="total-experiments-count">12</span> تجربة متاحة
                </div>
            </div>
            <p>استكشف مجموعة متنوعة من التجارب التفاعلية لفهم جوانب مختلفة من عمل الترانزستور. تم تصميم هذه التجارب بعناية لتغطي جميع المفاهيم الأساسية والمتقدمة في مجال الترانزستورات والدوائر الإلكترونية.</p>

            <div class="experiment-overview">
                <div class="overview-item">
                    <div class="overview-icon"><i class="fas fa-microchip"></i></div>
                    <div class="overview-count">4</div>
                    <div class="overview-label">أساسيات الترانزستور</div>
                </div>
                <div class="overview-item">
                    <div class="overview-icon"><i class="fas fa-project-diagram"></i></div>
                    <div class="overview-count">3</div>
                    <div class="overview-label">دوائر NPN</div>
                </div>
                <div class="overview-item">
                    <div class="overview-icon"><i class="fas fa-exchange-alt"></i></div>
                    <div class="overview-count">2</div>
                    <div class="overview-label">دوائر PNP</div>
                </div>
                <div class="overview-item">
                    <div class="overview-icon"><i class="fas fa-cogs"></i></div>
                    <div class="overview-count">3</div>
                    <div class="overview-label">تطبيقات عملية</div>
                </div>
            </div>

            <div class="tabs">
                <div class="tab active" data-tab="tab-basics">أساسيات الترانزستور</div>
                <div class="tab" data-tab="tab-npn">NPN</div>
                <div class="tab" data-tab="tab-pnp">PNP</div>
                <div class="tab" data-tab="tab-applications">تطبيقات</div>
            </div>

            <div id="tab-basics" class="tab-content active">
                <div class="tab-header">
                    <h3>أساسيات الترانزستور</h3>
                    <p>تعرف على المفاهيم الأساسية للترانزستور وكيفية عمله من خلال هذه التجارب التفاعلية المصممة للمبتدئين.</p>
                </div>

                <div class="experiment-grid">
                    <div class="experiment-card" data-category="basics" data-level="beginner" data-duration="short">
                        <img src="images/placeholder_circuit_workbench.svg" alt="أساسيات الترانزستور" class="experiment-thumbnail">
                        <div class="experiment-meta">
                            <span class="experiment-level beginner"><i class="fas fa-signal-1"></i> مبتدئ</span>
                            <span class="experiment-duration"><i class="far fa-clock"></i> 15 دقيقة</span>
                        </div>
                        <h3><a href="Basic TRANSISTOR OPERATION .html">أساسيات عمل الترانزستور</a></h3>
                        <p>تعرف على المبادئ الأساسية لعمل الترانزستور وتركيبه الداخلي وكيفية توصيله في الدوائر.</p>
                        <div class="experiment-details">
                            <div class="detail-item"><i class="fas fa-graduation-cap"></i> المهارات: فهم بنية الترانزستور، التعرف على الأطراف</div>
                            <div class="detail-item"><i class="fas fa-tools"></i> الأدوات: نموذج تفاعلي ثلاثي الأبعاد، محاكي بسيط</div>
                            <div class="detail-item"><i class="fas fa-tasks"></i> التطبيقات: أساس لجميع دوائر الترانزستور</div>
                        </div>
                        <div class="experiment-actions">
                            <a href="Basic TRANSISTOR OPERATION .html" class="btn"><i class="fas fa-play"></i> بدء التجربة</a>
                            <button type="button" class="btn-icon" title="حفظ للمشاهدة لاحقاً"><i class="far fa-bookmark"></i></button>
                            <button type="button" class="btn-icon" title="مشاركة"><i class="fas fa-share-alt"></i></button>
                        </div>
                    </div>

                    <div class="experiment-card" data-category="basics" data-level="beginner" data-duration="medium">
                        <img src="images/placeholder_circuit_workbench.svg" alt="مستكشف أساسيات الترانزستور" class="experiment-thumbnail">
                        <div class="experiment-meta">
                            <span class="experiment-level beginner"><i class="fas fa-signal-1"></i> مبتدئ</span>
                            <span class="experiment-duration"><i class="far fa-clock"></i> 20 دقيقة</span>
                        </div>
                        <h3><a href="Transistor Basics Explorer.html">مستكشف أساسيات الترانزستور</a></h3>
                        <p>تجربة تفاعلية لفهم المفاهيم الأساسية للترانزستور من خلال محاكاة تفاعلية.</p>
                        <div class="experiment-details">
                            <div class="detail-item"><i class="fas fa-graduation-cap"></i> المهارات: فهم مبادئ عمل الترانزستور، تحليل الدوائر البسيطة</div>
                            <div class="detail-item"><i class="fas fa-tools"></i> الأدوات: محاكي تفاعلي، أدوات قياس افتراضية</div>
                            <div class="detail-item"><i class="fas fa-tasks"></i> التطبيقات: تصميم دوائر الترانزستور الأساسية</div>
                        </div>
                        <div class="experiment-actions">
                            <a href="Transistor Basics Explorer.html" class="btn"><i class="fas fa-play"></i> بدء التجربة</a>
                            <button type="button" class="btn-icon" title="حفظ للمشاهدة لاحقاً"><i class="far fa-bookmark"></i></button>
                            <button type="button" class="btn-icon" title="مشاركة"><i class="fas fa-share-alt"></i></button>
                        </div>
                    </div>

                    <div class="experiment-card" data-category="basics" data-level="beginner" data-duration="short">
                        <img src="images/placeholder_circuit_workbench.svg" alt="تشبيه الترانزستور بصنبور الماء" class="experiment-thumbnail">
                        <div class="experiment-meta">
                            <span class="experiment-level beginner"><i class="fas fa-signal-1"></i> مبتدئ</span>
                            <span class="experiment-duration"><i class="far fa-clock"></i> 10 دقيقة</span>
                        </div>
                        <h3><a href="Transistor Water Tap Analogy.html">تشبيه الترانزستور بصنبور الماء</a></h3>
                        <p>فهم عمل الترانزستور من خلال تشبيهه بصنبور الماء لتبسيط مفهوم التحكم بالتيار.</p>
                        <div class="experiment-details">
                            <div class="detail-item"><i class="fas fa-graduation-cap"></i> المهارات: فهم مبدأ التحكم بالتيار، مفهوم الكسب</div>
                            <div class="detail-item"><i class="fas fa-tools"></i> الأدوات: محاكاة تفاعلية، رسوم متحركة</div>
                            <div class="detail-item"><i class="fas fa-tasks"></i> التطبيقات: فهم أساسي لمبدأ عمل الترانزستور</div>
                        </div>
                        <div class="experiment-actions">
                            <a href="Transistor Water Tap Analogy.html" class="btn"><i class="fas fa-play"></i> بدء التجربة</a>
                            <button type="button" class="btn-icon" title="حفظ للمشاهدة لاحقاً"><i class="far fa-bookmark"></i></button>
                            <button type="button" class="btn-icon" title="مشاركة"><i class="fas fa-share-alt"></i></button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="tab-npn" class="tab-content">
                <div class="tab-header">
                    <h3>دوائر ترانزستور NPN</h3>
                    <p>استكشف خصائص وتطبيقات ترانزستور NPN من خلال هذه التجارب التفاعلية المتخصصة.</p>
                </div>

                <div class="experiment-grid">
                    <div class="experiment-card" data-category="npn" data-level="beginner" data-duration="medium">
                        <div class="experiment-badge"><i class="fas fa-star"></i> الأكثر شعبية</div>
                        <img src="images/placeholder_circuit_npn_modes.png" alt="مستكشف أوضاع تشغيل NPN" class="experiment-thumbnail">
                        <div class="experiment-meta">
                            <span class="experiment-level beginner"><i class="fas fa-signal-1"></i> مبتدئ</span>
                            <span class="experiment-duration"><i class="far fa-clock"></i> 20 دقيقة</span>
                        </div>
                        <h3><a href="NPN BJT Operating Modes Explorer.html">مستكشف أوضاع تشغيل NPN</a></h3>
                        <p>فهم مناطق القطع، الفعال، والتشبع للترانزستور NPN وكيفية التحكم في نقطة التشغيل.</p>
                        <div class="experiment-details">
                            <div class="detail-item"><i class="fas fa-graduation-cap"></i> المهارات: تحديد مناطق التشغيل، تحليل منحنيات الخرج</div>
                            <div class="detail-item"><i class="fas fa-tools"></i> الأدوات: محاكي تفاعلي، راسم بياني، مقياس متعدد</div>
                            <div class="detail-item"><i class="fas fa-tasks"></i> التطبيقات: تصميم دوائر التضخيم والتحكم</div>
                        </div>
                        <div class="experiment-actions">
                            <a href="NPN BJT Operating Modes Explorer.html" class="btn"><i class="fas fa-play"></i> بدء التجربة</a>
                            <button type="button" class="btn-icon" title="حفظ للمشاهدة لاحقاً"><i class="far fa-bookmark"></i></button>
                            <button type="button" class="btn-icon" title="مشاركة"><i class="fas fa-share-alt"></i></button>
                        </div>
                    </div>

                    <div class="experiment-card" data-category="npn" data-level="intermediate" data-duration="medium">
                        <img src="images/placeholder_circuit_npn_interactive.png" alt="مستكشف NPN التفاعلي" class="experiment-thumbnail">
                        <div class="experiment-meta">
                            <span class="experiment-level intermediate"><i class="fas fa-signal-2"></i> متوسط</span>
                            <span class="experiment-duration"><i class="far fa-clock"></i> 25 دقيقة</span>
                        </div>
                        <h3><a href="NPN Transistor Interactive Explorer.html">مستكشف NPN التفاعلي</a></h3>
                        <p>تغيير معلمات الدائرة وملاحظة التأثير على سلوك الترانزستور NPN في الوقت الفعلي.</p>
                        <div class="experiment-details">
                            <div class="detail-item"><i class="fas fa-graduation-cap"></i> المهارات: ضبط نقطة التشغيل، تحليل تأثير المكونات</div>
                            <div class="detail-item"><i class="fas fa-tools"></i> الأدوات: محاكي متقدم، أدوات قياس متعددة</div>
                            <div class="detail-item"><i class="fas fa-tasks"></i> التطبيقات: تصميم دوائر مستقرة، تحسين الأداء</div>
                        </div>
                        <div class="experiment-actions">
                            <a href="NPN Transistor Interactive Explorer.html" class="btn"><i class="fas fa-play"></i> بدء التجربة</a>
                            <button type="button" class="btn-icon" title="حفظ للمشاهدة لاحقاً"><i class="far fa-bookmark"></i></button>
                            <button type="button" class="btn-icon" title="مشاركة"><i class="fas fa-share-alt"></i></button>
                        </div>
                    </div>

                    <div class="experiment-card" data-category="npn" data-level="advanced" data-duration="long">
                        <div class="experiment-badge new-badge"><i class="fas fa-certificate"></i> جديد</div>
                        <img src="images/placeholder_circuit_npn_interactive.png" alt="محاكي سلوك NPN" class="experiment-thumbnail">
                        <div class="experiment-meta">
                            <span class="experiment-level advanced"><i class="fas fa-signal-3"></i> متقدم</span>
                            <span class="experiment-duration"><i class="far fa-clock"></i> 35 دقيقة</span>
                        </div>
                        <h3><a href="NPN Transistor Behavior Simulator.html">محاكي سلوك NPN</a></h3>
                        <p>محاكاة متقدمة لسلوك الترانزستور NPN في مختلف ظروف التشغيل مع تحليل الأداء.</p>
                        <div class="experiment-details">
                            <div class="detail-item"><i class="fas fa-graduation-cap"></i> المهارات: تحليل متقدم للدوائر، محاكاة الظروف المختلفة</div>
                            <div class="detail-item"><i class="fas fa-tools"></i> الأدوات: محاكي احترافي، أدوات تحليل متقدمة</div>
                            <div class="detail-item"><i class="fas fa-tasks"></i> التطبيقات: تصميم دوائر معقدة، تحليل الأعطال</div>
                        </div>
                        <div class="experiment-actions">
                            <a href="NPN Transistor Behavior Simulator.html" class="btn"><i class="fas fa-play"></i> بدء التجربة</a>
                            <button type="button" class="btn-icon" title="حفظ للمشاهدة لاحقاً"><i class="far fa-bookmark"></i></button>
                            <button type="button" class="btn-icon" title="مشاركة"><i class="fas fa-share-alt"></i></button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="tab-pnp" class="tab-content">
                <div class="tab-header">
                    <h3>دوائر ترانزستور PNP</h3>
                    <p>استكشف خصائص وتطبيقات ترانزستور PNP وتعرف على الاختلافات بينه وبين ترانزستور NPN.</p>
                </div>

                <div class="experiment-grid">
                    <div class="experiment-card" data-category="pnp" data-level="intermediate" data-duration="medium">
                        <div class="experiment-badge new-badge"><i class="fas fa-certificate"></i> جديد</div>
                        <img src="images/placeholder_circuit_pnp_regions.png" alt="مستكشف أوضاع تشغيل PNP" class="experiment-thumbnail">
                        <div class="experiment-meta">
                            <span class="experiment-level intermediate"><i class="fas fa-signal-2"></i> متوسط</span>
                            <span class="experiment-duration"><i class="far fa-clock"></i> 20 دقيقة</span>
                        </div>
                        <h3><a href="PNP BJT Operating Regions Explorer.html">مستكشف أوضاع تشغيل PNP</a></h3>
                        <p>استكشاف سلوك الترانزستور PNP في مختلف أوضاع التشغيل مع مقارنة بالنوع NPN.</p>
                        <div class="experiment-details">
                            <div class="detail-item"><i class="fas fa-graduation-cap"></i> المهارات: فهم الفرق بين PNP و NPN، تحليل منحنيات الخرج</div>
                            <div class="detail-item"><i class="fas fa-tools"></i> الأدوات: محاكي تفاعلي، راسم بياني، مقارن أداء</div>
                            <div class="detail-item"><i class="fas fa-tasks"></i> التطبيقات: دوائر التضخيم التكميلية، مصادر التيار</div>
                        </div>
                        <div class="experiment-actions">
                            <a href="PNP BJT Operating Regions Explorer.html" class="btn"><i class="fas fa-play"></i> بدء التجربة</a>
                            <button type="button" class="btn-icon" title="حفظ للمشاهدة لاحقاً"><i class="far fa-bookmark"></i></button>
                            <button type="button" class="btn-icon" title="مشاركة"><i class="fas fa-share-alt"></i></button>
                        </div>
                    </div>

                    <div class="experiment-card" data-category="pnp" data-level="intermediate" data-duration="medium">
                        <img src="images/placeholder_circuit_pnp_regions.png" alt="دوائر PNP الأساسية" class="experiment-thumbnail">
                        <div class="experiment-meta">
                            <span class="experiment-level intermediate"><i class="fas fa-signal-2"></i> متوسط</span>
                            <span class="experiment-duration"><i class="far fa-clock"></i> 25 دقيقة</span>
                        </div>
                        <h3><a href="PNP Basic Circuits.html">دوائر PNP الأساسية</a></h3>
                        <p>تعرف على الدوائر الأساسية لترانزستور PNP وكيفية تصميمها وتحليلها.</p>
                        <div class="experiment-details">
                            <div class="detail-item"><i class="fas fa-graduation-cap"></i> المهارات: تصميم دوائر PNP، حساب قيم المكونات</div>
                            <div class="detail-item"><i class="fas fa-tools"></i> الأدوات: محاكي دوائر، أدوات قياس، حاسبة تصميم</div>
                            <div class="detail-item"><i class="fas fa-tasks"></i> التطبيقات: مصادر التيار، دوائر التحكم</div>
                        </div>
                        <div class="experiment-actions">
                            <a href="PNP Basic Circuits.html" class="btn"><i class="fas fa-play"></i> بدء التجربة</a>
                            <button type="button" class="btn-icon" title="حفظ للمشاهدة لاحقاً"><i class="far fa-bookmark"></i></button>
                            <button type="button" class="btn-icon" title="مشاركة"><i class="fas fa-share-alt"></i></button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="tab-applications" class="tab-content">
                <div class="tab-header">
                    <h3>تطبيقات الترانزستور</h3>
                    <p>استكشف التطبيقات العملية للترانزستور في مختلف الدوائر الإلكترونية وتعلم كيفية تصميمها وتحليلها.</p>
                </div>

                <div class="experiment-grid">
                    <div class="experiment-card" data-category="amplifier" data-level="intermediate" data-duration="medium">
                        <div class="experiment-badge new-badge"><i class="fas fa-certificate"></i> جديد</div>
                        <img src="images/placeholder_circuit_ce_amplifier.png" alt="مضخم الباعث المشترك" class="experiment-thumbnail">
                        <div class="experiment-meta">
                            <span class="experiment-level intermediate"><i class="fas fa-signal-2"></i> متوسط</span>
                            <span class="experiment-duration"><i class="far fa-clock"></i> 25 دقيقة</span>
                        </div>
                        <h3><a href="Common Emitter Amplifier.html">مضخم الباعث المشترك</a></h3>
                        <p>فهم كيفية عمل الترانزستور NPN كمضخم للإشارة وحساب كسب الجهد والتيار.</p>
                        <div class="experiment-details">
                            <div class="detail-item"><i class="fas fa-graduation-cap"></i> المهارات: تصميم دوائر التضخيم، حساب كسب الجهد والتيار</div>
                            <div class="detail-item"><i class="fas fa-tools"></i> الأدوات: محاكي دوائر، راسم إشارة، مولد إشارة</div>
                            <div class="detail-item"><i class="fas fa-tasks"></i> التطبيقات: مكبرات الصوت، معالجة الإشارات</div>
                        </div>
                        <div class="experiment-actions">
                            <a href="Common Emitter Amplifier.html" class="btn"><i class="fas fa-play"></i> بدء التجربة</a>
                            <button type="button" class="btn-icon" title="حفظ للمشاهدة لاحقاً"><i class="far fa-bookmark"></i></button>
                            <button type="button" class="btn-icon" title="مشاركة"><i class="fas fa-share-alt"></i></button>
                        </div>
                    </div>

                    <div class="experiment-card" data-category="switch" data-level="beginner" data-duration="short">
                        <img src="images/placeholder_circuit_transistor_switch.png" alt="الترانزستور كمفتاح" class="experiment-thumbnail">
                        <div class="experiment-meta">
                            <span class="experiment-level beginner"><i class="fas fa-signal-1"></i> مبتدئ</span>
                            <span class="experiment-duration"><i class="far fa-clock"></i> 15 دقيقة</span>
                        </div>
                        <h3><a href="Transistor as a Switch.html">الترانزستور كمفتاح</a></h3>
                        <p>استكشاف كيف يمكن استخدام الترانزستور للتحكم في الدوائر كمفتاح إلكتروني.</p>
                        <div class="experiment-details">
                            <div class="detail-item"><i class="fas fa-graduation-cap"></i> المهارات: تصميم دوائر التحكم، فهم مناطق القطع والتشبع</div>
                            <div class="detail-item"><i class="fas fa-tools"></i> الأدوات: محاكي تفاعلي، مصابيح LED، مقاومات</div>
                            <div class="detail-item"><i class="fas fa-tasks"></i> التطبيقات: دوائر التحكم، أنظمة الإنذار، التحكم بالأجهزة</div>
                        </div>
                        <div class="experiment-actions">
                            <a href="Transistor as a Switch.html" class="btn"><i class="fas fa-play"></i> بدء التجربة</a>
                            <button type="button" class="btn-icon" title="حفظ للمشاهدة لاحقاً"><i class="far fa-bookmark"></i></button>
                            <button type="button" class="btn-icon" title="مشاركة"><i class="fas fa-share-alt"></i></button>
                        </div>
                    </div>

                    <div class="experiment-card" data-category="amplifier" data-level="advanced" data-duration="long">
                        <img src="images/placeholder_circuit_ce_amplifier.png" alt="محلل دائرة الباعث المشترك" class="experiment-thumbnail">
                        <div class="experiment-meta">
                            <span class="experiment-level advanced"><i class="fas fa-signal-3"></i> متقدم</span>
                            <span class="experiment-duration"><i class="far fa-clock"></i> 30 دقيقة</span>
                        </div>
                        <h3><a href="BJT Common-Emitter Circuit Analyzer.html">محلل دائرة الباعث المشترك</a></h3>
                        <p>تحليل متقدم لدائرة مضخم الباعث المشترك وحساب نقاط التشغيل وتحسين الأداء.</p>
                        <div class="experiment-details">
                            <div class="detail-item"><i class="fas fa-graduation-cap"></i> المهارات: تحليل متقدم للدوائر، حساب نقاط التشغيل</div>
                            <div class="detail-item"><i class="fas fa-tools"></i> الأدوات: محلل دوائر متقدم، أدوات قياس متعددة</div>
                            <div class="detail-item"><i class="fas fa-tasks"></i> التطبيقات: تصميم مضخمات عالية الأداء، تحسين الاستقرار</div>
                        </div>
                        <div class="experiment-actions">
                            <a href="BJT Common-Emitter Circuit Analyzer.html" class="btn"><i class="fas fa-play"></i> بدء التجربة</a>
                            <button type="button" class="btn-icon" title="حفظ للمشاهدة لاحقاً"><i class="far fa-bookmark"></i></button>
                            <button type="button" class="btn-icon" title="مشاركة"><i class="fas fa-share-alt"></i></button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="no-results">
                <p>لم يتم العثور على نتائج مطابقة للبحث. يرجى تجربة كلمات بحث أخرى.</p>
            </div>
        </section>

        <section id="workbench-section" class="container">
            <h2>مساحة عمل رسم الدوائر</h2>
            <div class="card">
                <div class="grid">
                    <div>
                        <h3>صمم دوائرك الخاصة</h3>
                        <p>استخدم مساحة العمل لتصميم ورسم دوائر الترانزستور الخاصة بك، وإجراء المحاكاة عليها، وتحليل النتائج بشكل تفاعلي.</p>
                        <p>يمكنك إضافة مكونات مختلفة مثل الترانزستورات والمقاومات والمكثفات ومصادر الجهد، وتوصيلها معًا لإنشاء دوائر متكاملة.</p>
                        <a href="workbench.html" class="button">انتقل إلى مساحة العمل</a>
                    </div>
                    <div>
                        <img src="images/placeholder_circuit_workbench.svg" alt="مساحة عمل الدوائر" class="workbench-preview-image">
                    </div>
                </div>
            </div>
        </section>

        <section id="circuit-templates" class="container">
            <div class="section-header">
                <h2>قوالب تصميم الدوائر الإلكترونية</h2>
                <a href="circuit-templates.html" class="view-all">عرض جميع القوالب <i class="fas fa-arrow-left"></i></a>
            </div>
            <p>ابدأ تصميم دوائر الترانزستور بسرعة باستخدام مجموعة من القوالب الجاهزة المصممة مسبقاً. يمكنك تحميل هذه القوالب واستخدامها كنقطة انطلاق لمشاريعك الخاصة.</p>

            <div class="templates-container">
                <div class="templates-grid">
                    <div class="template-card">
                        <div class="template-preview">
                            <img src="images/template_ce_amplifier.png" alt="قالب مضخم الباعث المشترك" onerror="this.src='images/placeholder_circuit_ce_amplifier.png'">
                            <div class="template-overlay">
                                <a href="workbench.html?template=ce_amplifier" class="btn-overlay"><i class="fas fa-edit"></i> تحرير</a>
                                <button type="button" class="btn-overlay template-preview-btn" data-template="ce_amplifier"><i class="fas fa-search"></i> معاينة</button>
                            </div>
                        </div>
                        <div class="template-info">
                            <h3>مضخم الباعث المشترك</h3>
                            <div class="template-meta">
                                <span><i class="fas fa-microchip"></i> ترانزستور NPN</span>
                                <span><i class="fas fa-signal"></i> تضخيم</span>
                            </div>
                            <p>دائرة مضخم أساسية بترانزستور NPN في تكوين الباعث المشترك مع تحيز مناسب.</p>
                            <div class="template-actions">
                                <a href="workbench.html?template=ce_amplifier" class="btn btn-sm"><i class="fas fa-play"></i> استخدام القالب</a>
                                <a href="templates/ce_amplifier.json" class="btn-icon" title="تنزيل" download><i class="fas fa-download"></i></a>
                            </div>
                        </div>
                    </div>

                    <div class="template-card">
                        <div class="template-preview">
                            <img src="images/template_transistor_switch.png" alt="قالب مفتاح ترانزستور" onerror="this.src='images/placeholder_circuit_transistor_switch.png'">
                            <div class="template-overlay">
                                <a href="workbench.html?template=transistor_switch" class="btn-overlay"><i class="fas fa-edit"></i> تحرير</a>
                                <button type="button" class="btn-overlay template-preview-btn" data-template="transistor_switch"><i class="fas fa-search"></i> معاينة</button>
                            </div>
                        </div>
                        <div class="template-info">
                            <h3>مفتاح ترانزستور</h3>
                            <div class="template-meta">
                                <span><i class="fas fa-microchip"></i> ترانزستور NPN</span>
                                <span><i class="fas fa-toggle-on"></i> تحكم</span>
                            </div>
                            <p>دائرة مفتاح بسيطة باستخدام ترانزستور NPN للتحكم في تشغيل وإيقاف حمل (LED).</p>
                            <div class="template-actions">
                                <a href="workbench.html?template=transistor_switch" class="btn btn-sm"><i class="fas fa-play"></i> استخدام القالب</a>
                                <a href="templates/transistor_switch.json" class="btn-icon" title="تنزيل" download><i class="fas fa-download"></i></a>
                            </div>
                        </div>
                    </div>

                    <div class="template-card">
                        <div class="template-preview">
                            <img src="images/template_darlington_pair.png" alt="قالب زوج دارلنجتون" onerror="this.src='images/placeholder_circuit_workbench.svg'">
                            <div class="template-overlay">
                                <a href="workbench.html?template=darlington_pair" class="btn-overlay"><i class="fas fa-edit"></i> تحرير</a>
                                <button type="button" class="btn-overlay template-preview-btn" data-template="darlington_pair"><i class="fas fa-search"></i> معاينة</button>
                            </div>
                        </div>
                        <div class="template-info">
                            <h3>زوج دارلنجتون</h3>
                            <div class="template-meta">
                                <span><i class="fas fa-microchip"></i> ترانزستور NPN</span>
                                <span><i class="fas fa-tachometer-alt"></i> كسب عالي</span>
                            </div>
                            <p>دائرة زوج دارلنجتون لتحقيق كسب تيار عالي باستخدام ترانزستورين NPN متصلين معاً.</p>
                            <div class="template-actions">
                                <a href="workbench.html?template=darlington_pair" class="btn btn-sm"><i class="fas fa-play"></i> استخدام القالب</a>
                                <a href="templates/darlington_pair.json" class="btn-icon" title="تنزيل" download><i class="fas fa-download"></i></a>
                            </div>
                        </div>
                    </div>

                    <div class="template-card">
                        <div class="template-badge new-badge"><i class="fas fa-certificate"></i> جديد</div>
                        <div class="template-preview">
                            <img src="images/template_differential_amplifier.png" alt="قالب مضخم تفاضلي" onerror="this.src='images/placeholder_circuit_workbench.svg'">
                            <div class="template-overlay">
                                <a href="workbench.html?template=differential_amplifier" class="btn-overlay"><i class="fas fa-edit"></i> تحرير</a>
                                <button type="button" class="btn-overlay template-preview-btn" data-template="differential_amplifier"><i class="fas fa-search"></i> معاينة</button>
                            </div>
                        </div>
                        <div class="template-info">
                            <h3>مضخم تفاضلي</h3>
                            <div class="template-meta">
                                <span><i class="fas fa-microchip"></i> ترانزستور NPN</span>
                                <span><i class="fas fa-wave-square"></i> تضخيم تفاضلي</span>
                            </div>
                            <p>دائرة مضخم تفاضلي أساسية باستخدام زوج من ترانزستورات NPN لتضخيم الفرق بين إشارتين.</p>
                            <div class="template-actions">
                                <a href="workbench.html?template=differential_amplifier" class="btn btn-sm"><i class="fas fa-play"></i> استخدام القالب</a>
                                <a href="templates/differential_amplifier.json" class="btn-icon" title="تنزيل" download><i class="fas fa-download"></i></a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="templates-categories">
                    <h3>تصفح حسب الفئة</h3>
                    <div class="category-buttons">
                        <a href="circuit-templates.html?category=amplifiers" class="category-button">
                            <i class="fas fa-volume-up"></i>
                            <span>مضخمات</span>
                        </a>
                        <a href="circuit-templates.html?category=switches" class="category-button">
                            <i class="fas fa-toggle-on"></i>
                            <span>مفاتيح</span>
                        </a>
                        <a href="circuit-templates.html?category=oscillators" class="category-button">
                            <i class="fas fa-wave-square"></i>
                            <span>مذبذبات</span>
                        </a>
                        <a href="circuit-templates.html?category=power" class="category-button">
                            <i class="fas fa-bolt"></i>
                            <span>دوائر الطاقة</span>
                        </a>
                        <a href="circuit-templates.html?category=sensors" class="category-button">
                            <i class="fas fa-thermometer-half"></i>
                            <span>حساسات</span>
                        </a>
                        <a href="circuit-templates.html?category=logic" class="category-button">
                            <i class="fas fa-microchip"></i>
                            <span>دوائر منطقية</span>
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <section id="schematic-diagrams" class="container">
            <div class="section-header">
                <h2>الرسومات التخطيطية لدوائر الترانزستور</h2>
                <a href="schematics.html" class="view-all">عرض جميع الرسومات <i class="fas fa-arrow-left"></i></a>
            </div>
            <p>استكشف مجموعة من الرسومات التخطيطية (Schematic Diagrams) لدوائر الترانزستور المختلفة، مع شرح تفصيلي لكل مكون ووظيفته في الدائرة.</p>

            <div class="schematic-categories">
                <div class="schematic-category active" data-category="all">جميع الدوائر</div>
                <div class="schematic-category" data-category="amplifiers">دوائر التضخيم</div>
                <div class="schematic-category" data-category="switches">دوائر المفاتيح</div>
                <div class="schematic-category" data-category="oscillators">دوائر المذبذبات</div>
                <div class="schematic-category" data-category="power">دوائر الطاقة</div>
            </div>

            <div class="schematics-container">
                <div class="schematics-grid">
                    <!-- Common Emitter Amplifier -->
                    <div class="schematic-card" data-category="amplifiers">
                        <div class="schematic-preview">
                            <img src="images/schematics/common_emitter_amplifier.png" alt="دائرة مضخم الباعث المشترك" onerror="this.src='images/placeholder_circuit_ce_amplifier.png'">
                            <div class="schematic-overlay">
                                <button type="button" class="btn-overlay schematic-view-btn" data-schematic="common_emitter_amplifier"><i class="fas fa-search-plus"></i> تكبير</button>
                                <a href="schematics/common_emitter_amplifier.html" class="btn-overlay"><i class="fas fa-info-circle"></i> تفاصيل</a>
                            </div>
                        </div>
                        <div class="schematic-info">
                            <h3>دائرة مضخم الباعث المشترك</h3>
                            <div class="schematic-meta">
                                <span><i class="fas fa-microchip"></i> NPN</span>
                                <span><i class="fas fa-volume-up"></i> تضخيم</span>
                            </div>
                            <p>دائرة تضخيم أساسية باستخدام ترانزستور NPN في تكوين الباعث المشترك، تستخدم لتضخيم الإشارات الصغيرة.</p>
                            <div class="schematic-actions">
                                <a href="schematics/common_emitter_amplifier.html" class="btn btn-sm"><i class="fas fa-info-circle"></i> تفاصيل الدائرة</a>
                                <a href="images/schematics/common_emitter_amplifier.png" class="btn-icon" title="تنزيل" download><i class="fas fa-download"></i></a>
                            </div>
                        </div>
                    </div>

                    <!-- Transistor Switch -->
                    <div class="schematic-card" data-category="switches">
                        <div class="schematic-preview">
                            <img src="images/schematics/transistor_switch.png" alt="دائرة مفتاح الترانزستور" onerror="this.src='images/placeholder_circuit_transistor_switch.png'">
                            <div class="schematic-overlay">
                                <button type="button" class="btn-overlay schematic-view-btn" data-schematic="transistor_switch"><i class="fas fa-search-plus"></i> تكبير</button>
                                <a href="schematics/transistor_switch.html" class="btn-overlay"><i class="fas fa-info-circle"></i> تفاصيل</a>
                            </div>
                        </div>
                        <div class="schematic-info">
                            <h3>دائرة مفتاح الترانزستور</h3>
                            <div class="schematic-meta">
                                <span><i class="fas fa-microchip"></i> NPN</span>
                                <span><i class="fas fa-toggle-on"></i> تحكم</span>
                            </div>
                            <p>دائرة تستخدم الترانزستور كمفتاح للتحكم في تشغيل وإيقاف حمل كهربائي مثل LED أو محرك.</p>
                            <div class="schematic-actions">
                                <a href="schematics/transistor_switch.html" class="btn btn-sm"><i class="fas fa-info-circle"></i> تفاصيل الدائرة</a>
                                <a href="images/schematics/transistor_switch.png" class="btn-icon" title="تنزيل" download><i class="fas fa-download"></i></a>
                            </div>
                        </div>
                    </div>

                    <!-- Astable Multivibrator -->
                    <div class="schematic-card" data-category="oscillators">
                        <div class="schematic-badge new-badge"><i class="fas fa-certificate"></i> جديد</div>
                        <div class="schematic-preview">
                            <img src="images/schematics/astable_multivibrator.png" alt="دائرة المذبذب متعدد الاستقرار" onerror="this.src='images/placeholder_circuit_workbench.svg'">
                            <div class="schematic-overlay">
                                <button type="button" class="btn-overlay schematic-view-btn" data-schematic="astable_multivibrator"><i class="fas fa-search-plus"></i> تكبير</button>
                                <a href="schematics/astable_multivibrator.html" class="btn-overlay"><i class="fas fa-info-circle"></i> تفاصيل</a>
                            </div>
                        </div>
                        <div class="schematic-info">
                            <h3>دائرة المذبذب متعدد الاستقرار</h3>
                            <div class="schematic-meta">
                                <span><i class="fas fa-microchip"></i> NPN</span>
                                <span><i class="fas fa-wave-square"></i> تذبذب</span>
                            </div>
                            <p>دائرة مذبذب تستخدم ترانزستورين NPN لإنتاج إشارة مربعة متناوبة دون الحاجة إلى إشارة دخل.</p>
                            <div class="schematic-actions">
                                <a href="schematics/astable_multivibrator.html" class="btn btn-sm"><i class="fas fa-info-circle"></i> تفاصيل الدائرة</a>
                                <a href="images/schematics/astable_multivibrator.png" class="btn-icon" title="تنزيل" download><i class="fas fa-download"></i></a>
                            </div>
                        </div>
                    </div>

                    <!-- Voltage Regulator -->
                    <div class="schematic-card" data-category="power">
                        <div class="schematic-preview">
                            <img src="images/schematics/voltage_regulator.png" alt="دائرة منظم الجهد" onerror="this.src='images/placeholder_circuit_workbench.svg'">
                            <div class="schematic-overlay">
                                <button type="button" class="btn-overlay schematic-view-btn" data-schematic="voltage_regulator"><i class="fas fa-search-plus"></i> تكبير</button>
                                <a href="schematics/voltage_regulator.html" class="btn-overlay"><i class="fas fa-info-circle"></i> تفاصيل</a>
                            </div>
                        </div>
                        <div class="schematic-info">
                            <h3>دائرة منظم الجهد بالترانزستور</h3>
                            <div class="schematic-meta">
                                <span><i class="fas fa-microchip"></i> NPN</span>
                                <span><i class="fas fa-bolt"></i> تنظيم</span>
                            </div>
                            <p>دائرة بسيطة لتنظيم الجهد باستخدام ترانزستور NPN وديود زينر للحصول على جهد خرج ثابت.</p>
                            <div class="schematic-actions">
                                <a href="schematics/voltage_regulator.html" class="btn btn-sm"><i class="fas fa-info-circle"></i> تفاصيل الدائرة</a>
                                <a href="images/schematics/voltage_regulator.png" class="btn-icon" title="تنزيل" download><i class="fas fa-download"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="lab-notes-section" class="container">
            <div class="section-header">
                <h2>ملاحظات المعمل</h2>
                <a href="lab-notes.html" class="view-all">عرض جميع الملاحظات <i class="fas fa-arrow-left"></i></a>
            </div>
            <p>استكشف مجموعة من الملاحظات المعملية التي تشرح النظرية وراء دوائر الترانزستور المختلفة وتطبيقاتها.</p>

            <div class="lab-notes-preview">
                <div class="lab-note-card">
                    <div class="lab-note-icon"><i class="fas fa-book"></i></div>
                    <h3>مقدمة في نظرية أشباه الموصلات</h3>
                    <p>فهم أساسيات أشباه الموصلات وكيفية عملها في الترانزستورات</p>
                    <a href="lab-notes.html#semiconductors" class="btn btn-sm">قراءة الملاحظة</a>
                </div>

                <div class="lab-note-card">
                    <div class="lab-note-icon"><i class="fas fa-book"></i></div>
                    <h3>منحنيات خصائص الترانزستور</h3>
                    <p>شرح تفصيلي لمنحنيات الخصائص وكيفية تفسيرها</p>
                    <a href="lab-notes.html#characteristics" class="btn btn-sm">قراءة الملاحظة</a>
                </div>

                <div class="lab-note-card">
                    <div class="lab-note-icon"><i class="fas fa-book"></i></div>
                    <h3>تصميم دوائر التحيز</h3>
                    <p>كيفية تصميم دوائر تحيز مستقرة للترانزستور</p>
                    <a href="lab-notes.html#biasing" class="btn btn-sm">قراءة الملاحظة</a>
                </div>
            </div>
        </section>

        <section id="newsletter-section" class="container">
            <div class="newsletter-container">
                <div class="newsletter-content">
                    <h2>اشترك في النشرة الإخبارية</h2>
                    <p>احصل على آخر التحديثات والتجارب الجديدة مباشرة إلى بريدك الإلكتروني</p>

                    <form id="newsletter-form" class="newsletter-form">
                        <div class="form-group">
                            <input type="email" id="newsletter-email" placeholder="أدخل بريدك الإلكتروني" required>
                            <button type="submit" class="button">اشتراك <i class="fas fa-paper-plane"></i></button>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" id="newsletter-consent" required>
                            <label for="newsletter-consent">أوافق على تلقي رسائل إلكترونية حول التحديثات والميزات الجديدة</label>
                        </div>
                    </form>
                </div>
                <div class="newsletter-image">
                    <img src="images/newsletter-illustration.png" alt="النشرة الإخبارية" onerror="this.src='images/placeholder_circuit_workbench.svg'">
                </div>
            </div>
        </section>

        <section id="faq-section" class="container">
            <div class="section-header">
                <h2>الأسئلة الشائعة</h2>
            </div>

            <div class="faq-container">
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>ما هو معمل الدوائر الإلكترونية الافتراضي؟ <i class="fas fa-chevron-down"></i></h3>
                    </div>
                    <div class="faq-answer">
                        <p>معمل الدوائر الإلكترونية الافتراضي هو منصة تعليمية تفاعلية مصممة لمساعدة الطلاب والمهتمين بالإلكترونيات على فهم مبادئ عمل الترانزستور والدوائر الإلكترونية من خلال تجارب محاكاة واقعية وتفاعلية.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>هل يمكنني استخدام المعمل الافتراضي بدون خبرة سابقة؟ <i class="fas fa-chevron-down"></i></h3>
                    </div>
                    <div class="faq-answer">
                        <p>نعم، المعمل مصمم ليناسب جميع المستويات بدءًا من المبتدئين. ننصح باتباع مسار التعلم المقترح للحصول على أفضل تجربة تعليمية.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>هل يمكنني حفظ الدوائر التي أصممها؟ <i class="fas fa-chevron-down"></i></h3>
                    </div>
                    <div class="faq-answer">
                        <p>نعم، يمكنك حفظ الدوائر التي تصممها في مساحة العمل وتحميلها لاحقًا لمواصلة العمل عليها أو مشاركتها مع الآخرين.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>هل المعمل الافتراضي متوافق مع الأجهزة المحمولة؟ <i class="fas fa-chevron-down"></i></h3>
                    </div>
                    <div class="faq-answer">
                        <p>نعم، المعمل الافتراضي متوافق مع جميع الأجهزة بما في ذلك الهواتف الذكية والأجهزة اللوحية، مما يتيح لك التعلم في أي وقت ومن أي مكان.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>كيف يمكنني الإبلاغ عن مشكلة أو اقتراح ميزة جديدة؟ <i class="fas fa-chevron-down"></i></h3>
                    </div>
                    <div class="faq-answer">
                        <p>يمكنك التواصل معنا من خلال صفحة الاتصال أو إرسال بريد إلكتروني إلى <EMAIL> وسنكون سعداء بالرد على استفساراتك واقتراحاتك.</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="footer-top">
            <div class="container">
                <div class="footer-columns">
                    <div class="footer-column">
                        <h3>معمل الدوائر الإلكترونية</h3>
                        <p>منصة تعليمية تفاعلية لفهم مبادئ عمل الترانزستور والدوائر الإلكترونية</p>
                        <div class="social-links">
                            <a href="#" title="فيسبوك"><i class="fab fa-facebook"></i></a>
                            <a href="#" title="تويتر"><i class="fab fa-twitter"></i></a>
                            <a href="#" title="يوتيوب"><i class="fab fa-youtube"></i></a>
                            <a href="#" title="لينكد إن"><i class="fab fa-linkedin"></i></a>
                        </div>
                    </div>

                    <div class="footer-column">
                        <h3>روابط سريعة</h3>
                        <ul class="footer-links">
                            <li><a href="index.html">الرئيسية</a></li>
                            <li><a href="simulation.html">المحاكاة</a></li>
                            <li><a href="workbench.html">مساحة العمل</a></li>
                            <li><a href="lab-notes.html">ملاحظات المعمل</a></li>
                            <li><a href="#faq-section">الأسئلة الشائعة</a></li>
                        </ul>
                    </div>

                    <div class="footer-column">
                        <h3>الدعم</h3>
                        <ul class="footer-links">
                            <li><a href="#">دليل المستخدم</a></li>
                            <li><a href="#">الأسئلة الشائعة</a></li>
                            <li><a href="#">اتصل بنا</a></li>
                            <li><a href="#">الإبلاغ عن مشكلة</a></li>
                        </ul>
                    </div>

                    <div class="footer-column">
                        <h3>قانوني</h3>
                        <ul class="footer-links">
                            <li><a href="#">شروط الاستخدام</a></li>
                            <li><a href="#">سياسة الخصوصية</a></li>
                            <li><a href="#">ملفات تعريف الارتباط</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer-bottom">
            <div class="container">
                <div class="author-info">
                    <p class="author-name"><strong>المؤلف:</strong> د. محمد يعقوب إسماعيل</p>
                    <p class="author-contact">
                        <span><i class="far fa-envelope"></i> <EMAIL></span>
                        <span><i class="fas fa-phone-alt"></i> +249912867327 / +966538076790</span>
                    </p>
                </div>
                <p>&copy; 2024 معمل الدوائر الإلكترونية الافتراضي. جميع الحقوق محفوظة.</p>
                <p>تم التطوير بواسطة د. محمد يعقوب إسماعيل</p>
            </div>
        </div>
    </footer>

    <div id="scroll-top" class="scroll-top" title="التمرير لأعلى">
        <i class="fas fa-chevron-up"></i>
    </div>

    <!-- Schematic Modal -->
    <div class="schematic-modal" id="schematic-modal">
        <div class="schematic-modal-content">
            <div class="schematic-modal-header">
                <h3 id="schematic-modal-title">عرض الرسم التخطيطي</h3>
                <button type="button" class="schematic-modal-close">&times;</button>
            </div>
            <div class="schematic-modal-body">
                <div class="schematic-modal-image-container">
                    <img id="schematic-modal-image" src="" alt="رسم تخطيطي" class="schematic-modal-image">
                </div>
                <div id="schematic-modal-description" class="schematic-modal-description"></div>
                <div class="schematic-modal-components">
                    <h4>مكونات الدائرة</h4>
                    <div id="schematic-modal-component-list" class="component-list"></div>
                </div>
            </div>
            <div class="schematic-modal-footer">
                <a id="schematic-modal-details" href="#" class="btn"><i class="fas fa-info-circle"></i> تفاصيل الدائرة</a>
                <a id="schematic-modal-download" href="#" class="btn" download><i class="fas fa-download"></i> تنزيل الرسم</a>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script>
        // Scroll to top functionality
        const scrollTopButton = document.getElementById('scroll-top');

        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                scrollTopButton.classList.add('visible');
            } else {
                scrollTopButton.classList.remove('visible');
            }
        });

        scrollTopButton.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Schematic diagrams functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Schematic data
            const schematicData = {
                'common_emitter_amplifier': {
                    title: 'دائرة مضخم الباعث المشترك',
                    image: 'images/schematics/common_emitter_amplifier.png',
                    fallbackImage: 'images/placeholder_circuit_ce_amplifier.png',
                    description: 'دائرة تضخيم أساسية باستخدام ترانزستور NPN في تكوين الباعث المشترك. تستخدم هذه الدائرة لتضخيم الإشارات الصغيرة وتوفر كسب جهد عالي. يتم توصيل الإشارة إلى قاعدة الترانزستور، ويتم أخذ الخرج من المجمع، بينما يكون الباعث متصلاً بالأرضي من خلال مقاومة.',
                    components: [
                        { name: 'Q1', type: 'ترانزستور NPN', value: '2N2222', description: 'الترانزستور الرئيسي في الدائرة' },
                        { name: 'R1', type: 'مقاومة', value: '10 كيلو أوم', description: 'مقاومة تحيز القاعدة' },
                        { name: 'R2', type: 'مقاومة', value: '2.2 كيلو أوم', description: 'مقاومة تحيز القاعدة' },
                        { name: 'R3', type: 'مقاومة', value: '1 كيلو أوم', description: 'مقاومة المجمع' },
                        { name: 'R4', type: 'مقاومة', value: '220 أوم', description: 'مقاومة الباعث' },
                        { name: 'C1', type: 'مكثف', value: '10 ميكروفاراد', description: 'مكثف إقران الدخل' },
                        { name: 'C2', type: 'مكثف', value: '100 ميكروفاراد', description: 'مكثف إقران الخرج' }
                    ],
                    detailsUrl: 'schematics/common_emitter_amplifier.html',
                    downloadUrl: 'images/schematics/common_emitter_amplifier.png'
                },
                'transistor_switch': {
                    title: 'دائرة مفتاح الترانزستور',
                    image: 'images/schematics/transistor_switch.png',
                    fallbackImage: 'images/placeholder_circuit_transistor_switch.png',
                    description: 'دائرة تستخدم الترانزستور كمفتاح للتحكم في تشغيل وإيقاف حمل كهربائي مثل LED. تعمل هذه الدائرة في منطقتي القطع والتشبع، حيث يكون الترانزستور إما في حالة توصيل كامل أو قطع كامل.',
                    components: [
                        { name: 'Q1', type: 'ترانزستور NPN', value: '2N2222', description: 'الترانزستور المستخدم كمفتاح' },
                        { name: 'R1', type: 'مقاومة', value: '1 كيلو أوم', description: 'مقاومة تحديد تيار القاعدة' },
                        { name: 'R2', type: 'مقاومة', value: '220 أوم', description: 'مقاومة تحديد تيار LED' },
                        { name: 'D1', type: 'LED', value: 'أحمر', description: 'الحمل المراد التحكم به' }
                    ],
                    detailsUrl: 'schematics/transistor_switch.html',
                    downloadUrl: 'images/schematics/transistor_switch.png'
                },
                'astable_multivibrator': {
                    title: 'دائرة المذبذب متعدد الاستقرار',
                    image: 'images/schematics/astable_multivibrator.png',
                    fallbackImage: 'images/placeholder_circuit_workbench.svg',
                    description: 'دائرة مذبذب تستخدم ترانزستورين NPN لإنتاج إشارة مربعة متناوبة دون الحاجة إلى إشارة دخل. تعمل الدائرة على مبدأ الشحن والتفريغ المتبادل للمكثفات، مما يؤدي إلى تناوب حالة التوصيل بين الترانزستورين.',
                    components: [
                        { name: 'Q1', type: 'ترانزستور NPN', value: '2N2222', description: 'الترانزستور الأول' },
                        { name: 'Q2', type: 'ترانزستور NPN', value: '2N2222', description: 'الترانزستور الثاني' },
                        { name: 'R1, R4', type: 'مقاومة', value: '10 كيلو أوم', description: 'مقاومات المجمع' },
                        { name: 'R2, R3', type: 'مقاومة', value: '100 كيلو أوم', description: 'مقاومات القاعدة' },
                        { name: 'C1, C2', type: 'مكثف', value: '10 ميكروفاراد', description: 'مكثفات الاقتران' },
                        { name: 'LED1, LED2', type: 'LED', value: 'أحمر/أخضر', description: 'مؤشرات الخرج' }
                    ],
                    detailsUrl: 'schematics/astable_multivibrator.html',
                    downloadUrl: 'images/schematics/astable_multivibrator.png'
                },
                'voltage_regulator': {
                    title: 'دائرة منظم الجهد بالترانزستور',
                    image: 'images/schematics/voltage_regulator.png',
                    fallbackImage: 'images/placeholder_circuit_workbench.svg',
                    description: 'دائرة بسيطة لتنظيم الجهد باستخدام ترانزستور NPN وديود زينر للحصول على جهد خرج ثابت. يعمل ديود زينر على تثبيت جهد القاعدة، بينما يقوم الترانزستور بتوفير تيار الحمل.',
                    components: [
                        { name: 'Q1', type: 'ترانزستور NPN', value: '2N3055', description: 'ترانزستور القدرة' },
                        { name: 'D1', type: 'ديود زينر', value: '5.6 فولت', description: 'ديود تثبيت الجهد' },
                        { name: 'R1', type: 'مقاومة', value: '1 كيلو أوم', description: 'مقاومة تحديد تيار الزينر' },
                        { name: 'C1', type: 'مكثف', value: '100 ميكروفاراد', description: 'مكثف ترشيح الدخل' },
                        { name: 'C2', type: 'مكثف', value: '100 ميكروفاراد', description: 'مكثف ترشيح الخرج' }
                    ],
                    detailsUrl: 'schematics/voltage_regulator.html',
                    downloadUrl: 'images/schematics/voltage_regulator.png'
                }
            };

            // Get schematic modal elements
            const schematicModal = document.getElementById('schematic-modal');
            const schematicModalTitle = document.getElementById('schematic-modal-title');
            const schematicModalImage = document.getElementById('schematic-modal-image');
            const schematicModalDescription = document.getElementById('schematic-modal-description');
            const schematicModalComponentList = document.getElementById('schematic-modal-component-list');
            const schematicModalDetails = document.getElementById('schematic-modal-details');
            const schematicModalDownload = document.getElementById('schematic-modal-download');
            const schematicModalClose = document.querySelector('.schematic-modal-close');

            // Get all schematic view buttons
            const schematicViewButtons = document.querySelectorAll('.schematic-view-btn');

            // Add click event to schematic view buttons
            schematicViewButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const schematicId = this.dataset.schematic;
                    const schematic = schematicData[schematicId];

                    if (schematic) {
                        // Set modal content
                        schematicModalTitle.textContent = schematic.title;

                        schematicModalImage.src = schematic.image;
                        schematicModalImage.onerror = function() {
                            this.src = schematic.fallbackImage;
                        };

                        schematicModalDescription.textContent = schematic.description;

                        // Clear and populate component list
                        schematicModalComponentList.innerHTML = '';
                        schematic.components.forEach(component => {
                            const componentItem = document.createElement('div');
                            componentItem.className = 'component-item';
                            componentItem.innerHTML = `
                                <div class="component-icon"><i class="fas fa-microchip"></i></div>
                                <div class="component-info">
                                    <strong>${component.name}</strong>: ${component.type} (${component.value})
                                </div>
                            `;
                            schematicModalComponentList.appendChild(componentItem);
                        });

                        // Set links
                        schematicModalDetails.href = schematic.detailsUrl;
                        schematicModalDownload.href = schematic.downloadUrl;
                        schematicModalDownload.download = schematic.title + '.png';

                        // Show modal
                        schematicModal.classList.add('active');

                        // Prevent scrolling on body
                        document.body.style.overflow = 'hidden';
                    }
                });
            });

            // Close modal on close button click
            schematicModalClose.addEventListener('click', function() {
                schematicModal.classList.remove('active');
                document.body.style.overflow = '';
            });

            // Close modal on click outside
            schematicModal.addEventListener('click', function(e) {
                if (e.target === schematicModal) {
                    schematicModal.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });

            // Close modal on ESC key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && schematicModal.classList.contains('active')) {
                    schematicModal.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });

            // Filter schematics by category
            const schematicCategories = document.querySelectorAll('.schematic-category');
            const schematicCards = document.querySelectorAll('.schematic-card');

            schematicCategories.forEach(category => {
                category.addEventListener('click', function() {
                    // Remove active class from all categories
                    schematicCategories.forEach(cat => cat.classList.remove('active'));

                    // Add active class to clicked category
                    this.classList.add('active');

                    // Get selected category
                    const selectedCategory = this.dataset.category;

                    // Filter cards
                    schematicCards.forEach(card => {
                        if (selectedCategory === 'all' || card.dataset.category === selectedCategory) {
                            card.style.display = 'flex';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });

            // Template preview functionality
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'template-preview-modal';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 id="template-modal-title">معاينة القالب</h3>
                        <button type="button" class="modal-close">&times;</button>
                    </div>
                    <div class="modal-body">
                        <img id="template-modal-image" src="" alt="معاينة القالب" class="modal-image">
                        <div id="template-modal-description" class="modal-description"></div>
                        <div class="modal-specs">
                            <div class="spec-item">
                                <div class="spec-label">نوع الترانزستور:</div>
                                <div id="template-modal-transistor-type"></div>
                            </div>
                            <div class="spec-item">
                                <div class="spec-label">عدد المكونات:</div>
                                <div id="template-modal-components"></div>
                            </div>
                            <div class="spec-item">
                                <div class="spec-label">مستوى الصعوبة:</div>
                                <div id="template-modal-difficulty"></div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <a id="template-modal-download" href="#" class="btn" download><i class="fas fa-download"></i> تنزيل</a>
                        <a id="template-modal-edit" href="#" class="btn"><i class="fas fa-edit"></i> تحرير في مساحة العمل</a>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            // Template data
            const templateData = {
                'ce_amplifier': {
                    title: 'مضخم الباعث المشترك',
                    image: 'images/template_ce_amplifier.png',
                    fallbackImage: 'images/placeholder_circuit_ce_amplifier.png',
                    description: 'دائرة مضخم أساسية بترانزستور NPN في تكوين الباعث المشترك مع تحيز مناسب. تستخدم هذه الدائرة لتضخيم الإشارات الصغيرة وتوفر كسب جهد عالي.',
                    transistorType: 'NPN',
                    components: '7 مكونات (ترانزستور، 4 مقاومات، 2 مكثفات)',
                    difficulty: 'متوسط',
                    downloadUrl: 'templates/ce_amplifier.json',
                    editUrl: 'workbench.html?template=ce_amplifier'
                },
                'transistor_switch': {
                    title: 'مفتاح ترانزستور',
                    image: 'images/template_transistor_switch.png',
                    fallbackImage: 'images/placeholder_circuit_transistor_switch.png',
                    description: 'دائرة مفتاح بسيطة باستخدام ترانزستور NPN للتحكم في تشغيل وإيقاف حمل (LED). تعمل هذه الدائرة في منطقتي القطع والتشبع.',
                    transistorType: 'NPN',
                    components: '4 مكونات (ترانزستور، 2 مقاومات، LED)',
                    difficulty: 'مبتدئ',
                    downloadUrl: 'templates/transistor_switch.json',
                    editUrl: 'workbench.html?template=transistor_switch'
                },
                'darlington_pair': {
                    title: 'زوج دارلنجتون',
                    image: 'images/template_darlington_pair.png',
                    fallbackImage: 'images/placeholder_circuit_workbench.svg',
                    description: 'دائرة زوج دارلنجتون لتحقيق كسب تيار عالي باستخدام ترانزستورين NPN متصلين معاً. يستخدم هذا التكوين عندما نحتاج إلى كسب تيار عالي جداً.',
                    transistorType: 'NPN (زوج)',
                    components: '5 مكونات (2 ترانزستور، 3 مقاومات)',
                    difficulty: 'متوسط',
                    downloadUrl: 'templates/darlington_pair.json',
                    editUrl: 'workbench.html?template=darlington_pair'
                },
                'differential_amplifier': {
                    title: 'مضخم تفاضلي',
                    image: 'images/template_differential_amplifier.png',
                    fallbackImage: 'images/placeholder_circuit_workbench.svg',
                    description: 'دائرة مضخم تفاضلي أساسية باستخدام زوج من ترانزستورات NPN لتضخيم الفرق بين إشارتين. يستخدم هذا النوع من المضخمات في الدوائر التناظرية المتقدمة.',
                    transistorType: 'NPN (زوج)',
                    components: '8 مكونات (2 ترانزستور، 5 مقاومات، مصدر تيار ثابت)',
                    difficulty: 'متقدم',
                    downloadUrl: 'templates/differential_amplifier.json',
                    editUrl: 'workbench.html?template=differential_amplifier'
                }
            };

            // Get all preview buttons
            const previewButtons = document.querySelectorAll('.template-preview-btn');
            const modalCloseButton = modal.querySelector('.modal-close');

            // Add click event to preview buttons
            previewButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const templateId = this.dataset.template;
                    const template = templateData[templateId];

                    if (template) {
                        // Set modal content
                        document.getElementById('template-modal-title').textContent = template.title;

                        const modalImage = document.getElementById('template-modal-image');
                        modalImage.src = template.image;
                        modalImage.onerror = function() {
                            this.src = template.fallbackImage;
                        };

                        document.getElementById('template-modal-description').textContent = template.description;
                        document.getElementById('template-modal-transistor-type').textContent = template.transistorType;
                        document.getElementById('template-modal-components').textContent = template.components;
                        document.getElementById('template-modal-difficulty').textContent = template.difficulty;

                        const downloadLink = document.getElementById('template-modal-download');
                        downloadLink.href = template.downloadUrl;
                        downloadLink.download = template.title + '.json';

                        document.getElementById('template-modal-edit').href = template.editUrl;

                        // Show modal
                        modal.classList.add('active');

                        // Prevent scrolling on body
                        document.body.style.overflow = 'hidden';
                    }
                });
            });

            // Close modal on close button click
            modalCloseButton.addEventListener('click', function() {
                modal.classList.remove('active');
                document.body.style.overflow = '';
            });

            // Close modal on click outside
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });

            // Close modal on ESC key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && modal.classList.contains('active')) {
                    modal.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });
        });
    </script>
</body>
</html>