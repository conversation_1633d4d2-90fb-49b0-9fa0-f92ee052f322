@echo off
echo Copying web application files to Electron app directory...

mkdir app\css 2>nul
mkdir app\js 2>nul
mkdir app\images 2>nul
mkdir app\images\components 2>nul
mkdir app\images\lab-notes 2>nul

echo Copying HTML files...
copy ..\*.html app\ 2>nul

echo Copying CSS files...
copy ..\css\*.css app\css\ 2>nul

echo Copying JavaScript files...
copy ..\js\*.js app\js\ 2>nul

echo Copying image files...
copy ..\images\components\*.* app\images\components\ 2>nul
copy ..\images\lab-notes\*.* app\images\lab-notes\ 2>nul

echo Creating renderer.js for Electron integration...
echo // Renderer process integration with Electron > app\js\renderer.js
echo document.addEventListener('DOMContentLoaded', () => { >> app\js\renderer.js
echo   // Listen for IPC messages from the main process >> app\js\renderer.js
echo   window.api.receive('new-circuit', () => { >> app\js\renderer.js
echo     if (typeof resetWorkbench === 'function') { >> app\js\renderer.js
echo       resetWorkbench(); >> app\js\renderer.js
echo     } >> app\js\renderer.js
echo   }); >> app\js\renderer.js
echo. >> app\js\renderer.js
echo   window.api.receive('save-circuit', () => { >> app\js\renderer.js
echo     if (typeof saveCircuit === 'function') { >> app\js\renderer.js
echo       const circuitData = saveCircuit(); >> app\js\renderer.js
echo       window.api.send('save-file-dialog', JSON.stringify(circuitData)); >> app\js\renderer.js
echo     } >> app\js\renderer.js
echo   }); >> app\js\renderer.js
echo. >> app\js\renderer.js
echo   window.api.receive('open-circuit', (filePath) => { >> app\js\renderer.js
echo     if (typeof loadCircuit === 'function') { >> app\js\renderer.js
echo       // Read the file and load the circuit >> app\js\renderer.js
echo       fetch(filePath) >> app\js\renderer.js
echo         .then(response => response.json()) >> app\js\renderer.js
echo         .then(data => loadCircuit(data)) >> app\js\renderer.js
echo         .catch(error => console.error('Error loading circuit:', error)); >> app\js\renderer.js
echo     } >> app\js\renderer.js
echo   }); >> app\js\renderer.js
echo. >> app\js\renderer.js
echo   window.api.receive('export-image', () => { >> app\js\renderer.js
echo     if (typeof exportCircuitImage === 'function') { >> app\js\renderer.js
echo       const imageData = exportCircuitImage(); >> app\js\renderer.js
echo       window.api.send('export-image-dialog', imageData); >> app\js\renderer.js
echo     } >> app\js\renderer.js
echo   }); >> app\js\renderer.js
echo. >> app\js\renderer.js
echo   // Listen for responses from the main process >> app\js\renderer.js
echo   window.api.receive('save-file-response', (response) => { >> app\js\renderer.js
echo     if (response.success) { >> app\js\renderer.js
echo       alert('Circuit saved successfully!'); >> app\js\renderer.js
echo     } else { >> app\js\renderer.js
echo       alert('Error saving circuit: ' + response.message); >> app\js\renderer.js
echo     } >> app\js\renderer.js
echo   }); >> app\js\renderer.js
echo. >> app\js\renderer.js
echo   window.api.receive('export-image-response', (response) => { >> app\js\renderer.js
echo     if (response.success) { >> app\js\renderer.js
echo       alert('Image exported successfully!'); >> app\js\renderer.js
echo     } else { >> app\js\renderer.js
echo       alert('Error exporting image: ' + response.message); >> app\js\renderer.js
echo     } >> app\js\renderer.js
echo   }); >> app\js\renderer.js
echo }); >> app\js\renderer.js

echo Modifying HTML files to include renderer.js...
for %%f in (app\*.html) do (
    echo ^<script src="js/renderer.js"^>^</script^> >> "%%f"
)

echo Done!
pause
