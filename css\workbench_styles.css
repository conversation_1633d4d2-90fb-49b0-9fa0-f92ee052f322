n /* Workbench Specific Styles */

#workbench-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    width: 100%;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

/* Workbench Container Layout */
.workbench-container {
    display: flex;
    width: 100%;
    gap: 20px;
    margin-bottom: 20px;
}

/* Component Library Panel */
#component-library {
    width: 250px;
    background-color: #f0f0f0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.library-header {
    padding: 15px;
    background-color: #e0e0e0;
    border-bottom: 1px solid #ccc;
}

.library-header h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
    text-align: center;
}

.search-container {
    margin-bottom: 10px;
}

#component-search {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
}

.library-filters select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
}

.library-content {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
}

/* Component Categories and Items */
.component-category {
    margin-bottom: 20px;
}

.component-category h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #333;
    font-size: 14px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

.components-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.component-item {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.component-item:hover {
    background-color: #f0f8ff;
    transform: translateY(-2px);
}

.component-icon {
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 5px;
    font-size: 20px;
    color: #666;
}

.component-icon img {
    max-width: 100%;
    max-height: 100%;
}

.component-name {
    font-size: 12px;
    text-align: center;
    color: #333;
}

/* Main Workbench Area */
#workbench-main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Canvas Container */
.canvas-container {
    position: relative;
    margin-bottom: 20px;
}

.canvas-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
}

.toggle-container {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.toggle-container input[type="checkbox"] {
    margin-right: 5px;
}

.toggle-label {
    font-size: 14px;
    color: #333;
}

/* Oscilloscope Panel */
#oscilloscope-panel {
    width: 320px;
    background-color: #f0f0f0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 15px;
    display: flex;
    flex-direction: column;
}

#oscilloscope-panel h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
    text-align: center;
}

#oscilloscope-canvas {
    background-color: #000;
    border-radius: 4px;
    margin-bottom: 15px;
}

.oscilloscope-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.oscilloscope-controls button {
    padding: 8px 15px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.oscilloscope-controls button:hover {
    background-color: #0056b3;
}

.oscilloscope-controls select {
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
}

/* Templates Section */
#templates-section {
    width: 100%;
    margin-top: 20px;
    margin-bottom: 20px;
    background-color: #f0f0f0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 15px;
}

#templates-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 18px;
    text-align: center;
}

.templates-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
}

.template-card {
    width: 200px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.template-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.template-card h4 {
    margin: 0;
    padding: 10px;
    background-color: #007bff;
    color: white;
    font-size: 14px;
    text-align: center;
}

.template-preview {
    height: 120px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
    background-color: #f9f9f9;
}

.template-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.load-template-btn {
    width: 100%;
    padding: 10px;
    background-color: #28a745;
    color: white;
    border: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.load-template-btn:hover {
    background-color: #218838;
}

#toolbar {
    margin-bottom: 20px;
    padding: 10px;
    background-color: #e0e0e0;
    border-radius: 5px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
}

.toolbar-section {
    background-color: #f0f0f0;
    border-radius: 5px;
    padding: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 150px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.toolbar-section h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #333;
    font-size: 14px;
    text-align: center;
    border-bottom: 1px solid #ccc;
    padding-bottom: 5px;
    width: 100%;
}

#toolbar button {
    padding: 8px 15px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-bottom: 5px;
    width: 100%;
    text-align: center;
}

#toolbar button:hover {
    background-color: #0056b3;
}

#toolbar button.active {
    background-color: #004085;
    box-shadow: inset 0 0 5px rgba(0,0,0,0.3);
}

#circuit-canvas {
    border: 1px solid #ccc;
    background-color: #ffffff;
    cursor: crosshair; /* Default cursor for drawing */
    margin-bottom: 20px;
}

#properties-panel {
    width: 100%;
    max-width: 800px;
    padding: 15px;
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 20px;
}

#properties-panel h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #333;
}

#simulation-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin-bottom: 20px;
}

#simulation-controls button {
    padding: 10px 20px;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    min-width: 120px;
}

#simulation-controls button#simulate-button {
    background-color: #28a745;
}

#simulation-controls button#simulate-button:hover {
    background-color: #1e7e34;
}

#simulation-controls button#reset-button {
    background-color: #dc3545;
}

#simulation-controls button#reset-button:hover {
    background-color: #c82333;
}

#simulation-controls button#save-button {
    background-color: #007bff;
}

#simulation-controls button#save-button:hover {
    background-color: #0056b3;
}

#simulation-controls button#load-button {
    background-color: #6c757d;
}

#simulation-controls button#load-button:hover {
    background-color: #5a6268;
}

#simulation-controls button#export-button {
    background-color: #17a2b8;
}

#simulation-controls button#export-button:hover {
    background-color: #138496;
}

/* Simulation Modal Styles */
.simulation-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.simulation-modal-content {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    width: 80%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.close-button {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.close-button:hover {
    color: #000;
}

.simulation-results h3 {
    margin-top: 15px;
    margin-bottom: 10px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.simulation-results ul {
    list-style-type: none;
    padding-left: 20px;
}

.simulation-results li {
    margin-bottom: 5px;
    font-family: monospace;
    font-size: 14px;
}

.simulation-note {
    margin-top: 20px;
    padding: 10px;
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    font-size: 14px;
    color: #666;
}