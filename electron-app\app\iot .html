!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنفوجرافيك: إنترنت الأشياء في الهندسة الطبية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <!-- 
        Chosen Color Palette: Energetic & Playful
        - Coral: #FF6B6B
        - Sunny Yellow: #FFD166
        - Turquoise Green: #06D6A0
        - Cerulean Blue: #118AB2
        - Midnight Blue: #073B4C (Dark text & accents)
    -->
    <!--
        Narrative & Structure Plan:
        1.  Header: Title and brief introduction to IoMT.
        2.  Section 1: Market Overview - IoMT Market Size & Growth (Line Chart).
        3.  Section 2: Core IoMT Architecture (Styled HTML Cards for layers).
        4.  Section 3: Key IoMT Applications & Benefits (Grid of cards with Donut Chart, Bar Chart, Big Numbers).
             - Remote Patient Monitoring (RPM)
             - Smart Medical Devices
             - Connected Hospitals
             - Smart Surgery & Preventive Care
        5.  Section 4: Promising Future Technologies in IoMT (Grid of cards with HTML/CSS diagram, Bar Chart).
             - AI & ML (AIoT Cycle Diagram)
             - Role of 5G (5G Benefits Bar Chart)
        6.  Section 5: Key Challenges & Ethical Considerations (Grid of cards with Radar Chart, HTML/CSS visual, List).
             - Cybersecurity & Privacy
             - Interoperability & Regulation
             - Ethical Considerations
        7.  Conclusion: Summary of IoMT's potential and the path forward.
        8.  Footer.
    -->
    <!--
        Visualization Choices Summary:
        - IoMT Market Growth: Line Chart (Chart.js - Canvas). Goal: Change. Justification: Show market trend over time. No SVG.
        - IoMT Architecture Layers: Styled HTML Cards (Tailwind CSS). Goal: Organize. Justification: Clearly present distinct layers from Table 1. No SVG/Mermaid.
        - RPM Conditions Monitored: Donut Chart (Chart.js - Canvas). Goal: Compare (composition). Justification: Show proportions of RPM use. No SVG.
        - Smart Medical Device Examples: Bar Chart (Chart.js - Canvas). Goal: Compare. Justification: List device types and qualitative benefits. Labels wrapped. No SVG.
        - Connected Hospitals Efficiency: Single Big Number (HTML/CSS). Goal: Inform. Justification: Highlight key benefit. No SVG.
        - Smart Surgery Recovery: Single Big Number (HTML/CSS). Goal: Inform. Justification: Highlight key surgical benefit. No SVG.
        - AIoT Cycle: HTML/CSS Flow Diagram (Tailwind CSS, Unicode arrows). Goal: Organize (process). Justification: Visualize AI-IoT synergy. No SVG/Mermaid.
        - 5G Benefits vs 4G: Bar Chart (Chart.js - Canvas). Goal: Compare. Justification: Show 5G advantages. Labels wrapped. No SVG.
        - Cybersecurity Vulnerabilities: Radar Chart (Chart.js - Canvas). Goal: Compare (multiple dimensions). Justification: Show multiple risk factors. No SVG.
        - Interoperability Visual: HTML/CSS Disconnected/Connected Puzzle Pieces (Tailwind CSS). Goal: Compare/Inform. Justification: Simple metaphor. No SVG.
        - Ethical Points: Styled List (HTML `<ul>` with Tailwind). Goal: Organize. Justification: Clear listing. No SVG.
        - Confirmation: NEITHER Mermaid JS NOR SVG were used anywhere in this output.
    -->
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f0f4f8; /* Light gray-blue background */
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 600px; /* Default max-width */
            margin-left: auto;
            margin-right: auto;
            height: 300px; /* Base height */
            max-height: 400px; /* Max height */
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 350px;
            }
        }
        .section-title {
            font-size: 2rem;
            font-weight: 700;
            color: #073B4C; /* Midnight Blue */
            margin-bottom: 1rem;
            text-align: center;
        }
        .card {
            background-color: white;
            border-radius: 0.75rem; /* 12px */
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            padding: 1.5rem; /* 24px */
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .card-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #118AB2; /* Cerulean Blue */
            margin-bottom: 0.75rem;
        }
        .card-text {
            font-size: 1rem;
            color: #073B4C; /* Midnight Blue */
            line-height: 1.6;
        }
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: #FF6B6B; /* Coral */
        }
        .stat-label {
            font-size: 1rem;
            color: #073B4C; /* Midnight Blue */
        }
        .icon-style {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            color: #FFD166; /* Sunny Yellow */
        }
        .flow-step {
            background-color: #e0f2fe; /* Light blue for flow steps */
            border: 2px solid #118AB2; /* Cerulean Blue border */
            color: #073B4C; /* Midnight Blue text */
            padding: 0.75rem;
            border-radius: 0.5rem;
            text-align: center;
            font-weight: 600;
        }
        .arrow-symbol {
            font-size: 1.5rem;
            color: #118AB2; /* Cerulean Blue */
            margin: 0 0.5rem;
        }
    </style>
</head>
<body class="antialiased">

    <header class="bg-gradient-to-r from-[#118AB2] to-[#06D6A0] text-white py-12 px-4 text-center shadow-lg">
        <div class="container mx-auto">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">إنترنت الأشياء في الهندسة الطبية: اتجاهات الصناعة وأبحاث السوق</h1>
            <p class="text-lg md:text-xl max-w-3xl mx-auto">
                نظرة شاملة على كيفية إحداث إنترنت الأشياء (IoT) لثورة في قطاع الرعاية الصحية، واستكشاف تطبيقاته الحالية، وآفاقه المستقبلية، والتحديات الرئيسية.
            </p>
        </div>
    </header>

    <main class="container mx-auto p-4 md:p-8">

        <section id="market-overview" class="my-12">
            <h2 class="section-title">نظرة عامة على سوق إنترنت الأشياء الطبية (IoMT)</h2>
            <div class="card">
                <h3 class="card-title">حجم سوق إنترنت الأشياء الطبية وآفاق نموه</h3>
                <p class="card-text mb-6">
                    يُتوقع أن يشهد سوق إنترنت الأشياء الطبية (IoMT) نموًا هائلاً في السنوات القادمة، مدفوعًا بالتقدم في تقنيات المراقبة عن بعد، والأجهزة الطبية الذكية، وأتمتة سير العمل في مؤسسات الرعاية الصحية. هذا النمو يعكس الدور المحوري الذي تلعبه إنترنت الأشياء في تحسين كفاءة الرعاية الصحية ونتائج المرضى.
                </p>
                <div class="chart-container h-[300px] md:h-[400px]">
                    <canvas id="iomtMarketGrowthChart"></canvas>
                </div>
                <p class="card-text mt-4 text-sm text-gray-600 text-center">الشكل 1: توقعات نمو سوق إنترنت الأشياء الطبية العالمي (مليار دولار أمريكي) - بيانات توضيحية.</p>
            </div>
        </section>

        <section id="iomt-architecture" class="my-12">
            <h2 class="section-title">البنية المعمارية الأساسية لإنترنت الأشياء الطبية</h2>
            <p class="text-center text-lg text-[#073B4C] mb-8 max-w-2xl mx-auto">
                تتألف البنية المعمارية لإنترنت الأشياء من عدة طبقات متكاملة تعمل معًا لجمع البيانات ونقلها ومعالجتها وتقديمها بشكل آمن وفعال. كل طبقة لها دور حيوي في تمكين الحلول الذكية في القطاع الطبي.
            </p>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="card">
                    <div class="icon-style text-center">📡</div>
                    <h3 class="card-title text-center">طبقة الإدراك (Perception)</h3>
                    <p class="card-text">المسؤولة عن جمع البيانات الخام من العالم المادي عبر أجهزة الاستشعار، والمحركات، وتقنيات تحديد الهوية (مثل RFID، QR codes). هي واجهة التفاعل المباشر مع البيئة أو المريض.</p>
                </div>
                <div class="card">
                    <div class="icon-style text-center">🌐</div>
                    <h3 class="card-title text-center">طبقة الاتصال/النقل (Connectivity)</h3>
                    <p class="card-text">تضمن نقل البيانات المجمعة بكفاءة وأمان بين الأجهزة والمنصات السحابية أو الطرفية. تشمل شبكات Wi-Fi، Bluetooth، 5G، وبروتوكولات الاتصال المتنوعة.</p>
                </div>
                <div class="card">
                    <div class="icon-style text-center">⚙️</div>
                    <h3 class="card-title text-center">طبقة المعالجة/البيانات (Processing)</h3>
                    <p class="card-text">يتم فيها تخزين ومعالجة وتحليل البيانات الضخمة. تشمل قواعد البيانات، منصات التحليل، خوارزميات الذكاء الاصطناعي، والحوسبة الطرفية لمعالجة أسرع بالقرب من المصدر.</p>
                </div>
                <div class="card">
                    <div class="icon-style text-center">📱</div>
                    <h3 class="card-title text-center">طبقة التطبيق (Application)</h3>
                    <p class="card-text">توفر الواجهات التي يتفاعل معها المستخدمون (أطباء، مرضى) للتحكم في الأجهزة، عرض البيانات، والحصول على رؤى. تشمل تطبيقات الويب والجوال ولوحات المعلومات.</p>
                </div>
                <div class="card">
                    <div class="icon-style text-center">⚖️</div>
                    <h3 class="card-title text-center">طبقة العملية (Process)</h3>
                    <p class="card-text">تُعنى بحوكمة وإدارة وتشغيل نظام إنترنت الأشياء بأكمله، بما في ذلك سياسات العمل وسير العمليات لضمان الامتثال والكفاءة.</p>
                </div>
                <div class="card">
                    <div class="icon-style text-center">🛡️</div>
                    <h3 class="card-title text-center">طبقة الأمن (Security)</h3>
                    <p class="card-text">طبقة شاملة ومتداخلة عبر جميع الطبقات الأخرى، ضرورية لحماية البيانات والأنظمة من التهديدات السيبرانية وضمان خصوصية المريض.</p>
                </div>
            </div>
        </section>

        <section id="key-applications" class="my-12">
            <h2 class="section-title">التطبيقات الرئيسية لإنترنت الأشياء في القطاع الطبي وفوائدها</h2>
            <p class="text-center text-lg text-[#073B4C] mb-8 max-w-2xl mx-auto">
                تُحدث إنترنت الأشياء تحولاً جذرياً في تقديم الرعاية الصحية من خلال مجموعة واسعة من التطبيقات المبتكرة التي تُحسن التشخيص والعلاج والمراقبة، مما يؤدي إلى رعاية أكثر تخصيصًا وفعالية.
            </p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="card">
                    <div class="icon-style text-center">🏠</div>
                    <h3 class="card-title">مراقبة المرضى عن بعد (RPM)</h3>
                    <p class="card-text mb-4">تُمكّن مراقبة المرضى عن بعد من تتبع العلامات الحيوية للمرضى بشكل مستمر وهم في منازلهم، مما يقلل الحاجة لزيارات المستشفى ويحسن إدارة الحالات المزمنة مثل السكري وأمراض القلب.</p>
                    <div class="chart-container h-[250px] md:h-[300px]">
                        <canvas id="rpmConditionsChart"></canvas>
                    </div>
                    <p class="card-text mt-2 text-sm text-gray-600 text-center">الشكل 2: توزيع استخدامات مراقبة المرضى عن بعد حسب نوع الحالة (بيانات توضيحية).</p>
                </div>

                <div class="card">
                    <div class="icon-style text-center">🩺</div>
                    <h3 class="card-title">الأجهزة الطبية الذكية</h3>
                    <p class="card-text mb-4">تشمل مضخات الأنسولين الذكية، وأجهزة الاستنشاق المتصلة، وأجهزة تخطيط القلب المنزلية. هذه الأجهزة تسهل التشخيص الدقيق، وتحسن الالتزام بالعلاج، وتمكن من نقل البيانات لاسلكياً للأطباء.</p>
                    <div class="chart-container h-[250px] md:h-[300px]">
                        <canvas id="smartMedicalDevicesChart"></canvas>
                    </div>
                    <p class="card-text mt-2 text-sm text-gray-600 text-center">الشكل 3: أمثلة على الأجهزة الطبية الذكية ومعدلات تبنيها المتوقعة (بيانات توضيحية).</p>
                </div>

                <div class="card">
                    <div class="icon-style text-center">🏥</div>
                    <h3 class="card-title">المستشفيات المتصلة</h3>
                    <p class="card-text mb-4">تستخدم إنترنت الأشياء لتبسيط العمليات مثل تتبع الأصول والمعدات، إدارة المخزون، ومراقبة نظافة الأيدي، مما يعزز الكفاءة التشغيلية ويحسن الاستجابة للطوارئ.</p>
                    <div class="text-center py-6">
                        <div class="stat-number">25%</div>
                        <div class="stat-label">زيادة متوقعة في الكفاءة التشغيلية</div>
                        <p class="card-text mt-2 text-sm text-gray-600">بفضل تتبع الأصول والصيانة التنبؤية (تقدير توضيحي).</p>
                    </div>
                </div>

                <div class="card">
                    <div class="icon-style text-center">👨‍⚕️</div>
                    <h3 class="card-title">الجراحة الذكية والرعاية الوقائية</h3>
                    <p class="card-text mb-4">الأدوات الجراحية المتصلة والروبوتات الجراحية تعزز الدقة وتقلل المخاطر. كما تساهم إنترنت الأشياء في الرعاية الوقائية عبر التنبؤ بالمشكلات الصحية قبل تفاقمها.</p>
                    <div class="text-center py-6">
                        <div class="stat-number">15%</div>
                        <div class="stat-label">تقليل محتمل في مدة التعافي بعد الجراحات الروبوتية</div>
                        <p class="card-text mt-2 text-sm text-gray-600">مقارنة بالجراحات التقليدية في بعض الحالات (تقدير توضيحي).</p>
                    </div>
                </div>
            </div>
        </section>
        
        <section id="future-tech" class="my-12">
            <h2 class="section-title">التقنيات المستقبلية الواعدة في إنترنت الأشياء الطبية</h2>
            <p class="text-center text-lg text-[#073B4C] mb-8 max-w-2xl mx-auto">
                يستمر الابتكار في دفع حدود ما هو ممكن في إنترنت الأشياء الطبية، مع تقنيات مثل الذكاء الاصطناعي وشبكات الجيل الخامس التي تعد بإحداث تحولات أكبر في كيفية تقديم الرعاية الصحية.
            </p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="card">
                    <div class="icon-style text-center">💡</div>
                    <h3 class="card-title">الذكاء الاصطناعي والتعلم الآلي (AIoT)</h3>
                    <p class="card-text mb-4">
                        يُشكل دمج الذكاء الاصطناعي مع إنترنت الأشياء (AIoT) تآزرًا تحويليًا. يمكن لخوارزميات الذكاء الاصطناعي تحليل البيانات الضخمة من أجهزة إنترنت الأشياء لتقديم تشخيصات أدق، وخطط علاج شخصية، وحتى أتمتة بعض الاستجابات الطبية.
                    </p>
                    <div class="space-y-3 p-2">
                        <div class="flow-step">جمع البيانات (أجهزة IoT)</div>
                        <div class="text-center"><span class="arrow-symbol">⬇️</span></div>
                        <div class="flow-step">معالجة البيانات (سحابية/طرفية)</div>
                        <div class="text-center"><span class="arrow-symbol">⬇️</span></div>
                        <div class="flow-step">تحليل الذكاء الاصطناعي (اكتشاف الأنماط والتنبؤ)</div>
                        <div class="text-center"><span class="arrow-symbol">⬇️</span></div>
                        <div class="flow-step">رؤى وقرارات (تشخيص/علاج)</div>
                        <div class="text-center"><span class="arrow-symbol">⬇️</span></div>
                        <div class="flow-step">إجراءات آلية أو مُحسّنة (مشغلات IoT)</div>
                    </div>
                     <p class="card-text mt-2 text-sm text-gray-600 text-center">الشكل 4: دورة عمل ذكاء الأشياء (AIoT) في الرعاية الصحية.</p>
                </div>

                <div class="card">
                     <div class="icon-style text-center">📶</div>
                    <h3 class="card-title">دور شبكات الجيل الخامس (5G)</h3>
                    <p class="card-text mb-4">
                        تُعتبر تقنية الجيل الخامس (5G) عامل تمكين حاسم للجيل القادم من إنترنت الأشياء الطبية. توفر سرعات فائقة، وزمن استجابة منخفض للغاية، وقدرة على دعم عدد هائل من الأجهزة المتصلة، مما يفتح الباب لتطبيقات متقدمة مثل الجراحة عن بعد ونقل البيانات الطبية الضخمة في الوقت الفعلي.
                    </p>
                    <div class="chart-container h-[300px] md:h-[350px]">
                        <canvas id="fiveGBenefitsChart"></canvas>
                    </div>
                    <p class="card-text mt-2 text-sm text-gray-600 text-center">الشكل 5: مقارنة بين قدرات شبكات 4G و 5G الداعمة لإنترنت الأشياء الطبية (بيانات توضيحية).</p>
                </div>
            </div>
        </section>

        <section id="challenges-ethics" class="my-12">
            <h2 class="section-title">التحديات الرئيسية والاعتبارات الأخلاقية</h2>
            <p class="text-center text-lg text-[#073B4C] mb-8 max-w-2xl mx-auto">
                على الرغم من الإمكانيات الهائلة، يواجه تبني إنترنت الأشياء الطبية تحديات تتعلق بالأمن والخصوصية وقابلية التشغيل البيني، بالإضافة إلى اعتبارات أخلاقية جوهرية يجب معالجتها.
            </p>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="card lg:col-span-1">
                    <div class="icon-style text-center">🔒</div>
                    <h3 class="card-title">الأمن السيبراني وخصوصية البيانات</h3>
                    <p class="card-text mb-4">تُعد حماية البيانات الصحية الحساسة من الاختراقات وسوء الاستخدام أولوية قصوى. يجب تأمين الأجهزة والشبكات بشكل قوي وتطبيق معايير تشفير متقدمة.</p>
                    <div class="chart-container h-[280px] md:h-[320px]">
                        <canvas id="cybersecurityChallengesChart"></canvas>
                    </div>
                    <p class="card-text mt-2 text-sm text-gray-600 text-center">الشكل 6: أبرز تحديات الأمن السيبراني في إنترنت الأشياء الطبية (تقييم توضيحي).</p>
                </div>

                <div class="card lg:col-span-1">
                    <div class="icon-style text-center">🧩</div>
                    <h3 class="card-title">قابلية التشغيل البيني والتنظيم</h3>
                    <p class="card-text mb-4">يشكل تحقيق التوافق بين الأجهزة والمنصات المختلفة تحديًا كبيرًا. غياب المعايير الموحدة يعيق تبادل البيانات بسلاسة. كما أن اللوائح التنظيمية المتغيرة تتطلب مواكبة مستمرة.</p>
                    <div class="flex flex-col items-center justify-center h-full mt-4">
                        <div class="flex space-x-1 mb-2">
                            <div class="w-10 h-10 bg-[#FF6B6B] rounded shadow-md animate-pulse"></div>
                            <div class="w-10 h-10 bg-[#FFD166] rounded shadow-md animate-pulse delay-100"></div>
                            <div class="w-10 h-10 bg-[#06D6A0] rounded shadow-md animate-pulse delay-200"></div>
                        </div>
                        <p class="text-sm text-[#073B4C] font-semibold mb-4">أنظمة غير مترابطة (تحدي)</p>
                        <div class="flex space-x-0.5">
                            <div class="w-10 h-10 bg-[#118AB2] rounded-l-md shadow-md"></div>
                            <div class="w-10 h-10 bg-[#118AB2] shadow-md"></div>
                            <div class="w-10 h-10 bg-[#118AB2] rounded-r-md shadow-md"></div>
                        </div>
                        <p class="text-sm text-[#073B4C] font-semibold mt-2">أنظمة مترابطة (الهدف)</p>
                    </div>
                     <p class="card-text mt-4 text-sm text-gray-600 text-center">الشكل 7: تمثيل بصري لتحدي قابلية التشغيل البيني.</p>
                </div>
                
                <div class="card lg:col-span-1">
                    <div class="icon-style text-center">📜</div>
                    <h3 class="card-title">الاعتبارات الأخلاقية</h3>
                    <p class="card-text mb-4">يثير جمع واستخدام البيانات الصحية قضايا أخلاقية مثل الموافقة المستنيرة، التحيز الخوارزمي في الذكاء الاصطناعي، والمساءلة. يجب ضمان العدالة والشفافية.</p>
                    <ul class="list-disc list-inside card-text space-y-1">
                        <li>ضمان موافقة المريض وعلمه بكيفية استخدام بياناته.</li>
                        <li>تجنب التحيز في الخوارزميات التي قد تؤثر على قرارات العلاج.</li>
                        <li>تحديد المسؤولية في حالة حدوث أخطاء ناتجة عن الأنظمة الذكية.</li>
                        <li>حماية خصوصية الفرد وحقه في عدم المشاركة.</li>
                        <li>ضمان إتاحة التقنيات بشكل عادل لمختلف الفئات.</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="conclusion" class="my-12 py-10 bg-gradient-to-r from-[#073B4C] to-[#118AB2] text-white rounded-lg shadow-xl">
            <div class="container mx-auto px-6 text-center">
                <h2 class="text-3xl font-bold mb-6">الخلاصة: مستقبل واعد ومنظم لإنترنت الأشياء الطبية</h2>
                <p class="text-lg leading-relaxed max-w-3xl mx-auto">
                    تقدم إنترنت الأشياء في الهندسة الطبية وعدًا هائلاً بتحسين جودة الرعاية الصحية، وتقليل التكاليف، وزيادة كفاءة الأنظمة الصحية. إن التآزر بين الأجهزة المتصلة، والبيانات الضخمة، والذكاء الاصطناعي يفتح آفاقًا جديدة للطب الشخصي والوقائي. ومع ذلك، يتطلب تحقيق هذه الإمكانات بالكامل معالجة جادة للتحديات المتعلقة بالأمن، والخصوصية، وقابلية التشغيل البيني، ووضع أطر أخلاقية وتنظيمية قوية تضمن استخدام هذه التقنيات بمسؤولية وإنصاف. الاستثمار المستمر في البحث والتطوير، مع التركيز على هذه الجوانب، هو السبيل لثورة حقيقية في تقديم الرعاية الصحية المستقبلية.
                </p>
            </div>
        </section>
    </main>

    <footer class="bg-[#073B4C] text-white text-center py-6 mt-12">
        <p>&copy; 2025 إنفوجرافيك إنترنت الأشياء في الهندسة الطبية. جميع الحقوق محفوظة (محتوى توضيحي).</p>
    </footer>

    <script>
        function processLabel(label, maxLength = 16) {
            if (label.length <= maxLength) {
                return label;
            }
            const words = label.split(' ');
            const lines = [];
            let currentLine = '';
            for (const word of words) {
                if ((currentLine + word).length > maxLength && currentLine.length > 0) {
                    lines.push(currentLine.trim());
                    currentLine = word + ' ';
                } else {
                    currentLine += word + ' ';
                }
            }
            if (currentLine.trim().length > 0) {
                lines.push(currentLine.trim());
            }
            return lines;
        }

        const tooltipTitleCallback = function(tooltipItems) {
            const item = tooltipItems[0];
            let label = item.chart.data.labels[item.dataIndex];
            if (Array.isArray(label)) {
              return label.join(' ');
            } else {
              return label;
            }
        };

        const commonChartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        font: { family: 'Cairo', size: 12 },
                        color: '#073B4C'
                    }
                },
                tooltip: {
                    titleFont: { family: 'Cairo', size: 14 },
                    bodyFont: { family: 'Cairo', size: 12 },
                    backgroundColor: 'rgba(7, 59, 76, 0.9)', // Midnight Blue
                    titleColor: '#FFD166', // Sunny Yellow
                    bodyColor: '#FFFFFF',
                    callbacks: {
                        title: tooltipTitleCallback
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        font: { family: 'Cairo', size: 11 },
                        color: '#073B4C'
                    },
                    grid: { display: false }
                },
                y: {
                    ticks: {
                        font: { family: 'Cairo', size: 11 },
                        color: '#073B4C'
                    },
                    grid: { color: '#e0e0e0' }
                }
            }
        };
        
        // Chart 1: IoMT Market Growth
        const iomtMarketCtx = document.getElementById('iomtMarketGrowthChart').getContext('2d');
        new Chart(iomtMarketCtx, {
            type: 'line',
            data: {
                labels: ['2022', '2023', '2024', '2025', '2026', '2027', '2028'],
                datasets: [{
                    label: 'حجم السوق (مليار دولار)',
                    data: [55, 75, 105, 145, 190, 250, 320], // Illustrative data
                    borderColor: '#FF6B6B', // Coral
                    backgroundColor: 'rgba(255, 107, 107, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: { ...commonChartOptions }
        });

        // Chart 2: RPM Conditions
        const rpmCtx = document.getElementById('rpmConditionsChart').getContext('2d');
        new Chart(rpmCtx, {
            type: 'doughnut',
            data: {
                labels: [processLabel('أمراض القلب والأوعية الدموية'), processLabel('مرض السكري'), processLabel('أمراض الجهاز التنفسي'), processLabel('اضطرابات النوم'), processLabel('حالات أخرى')],
                datasets: [{
                    label: 'استخدامات RPM',
                    data: [35, 25, 20, 10, 10], // Illustrative data
                    backgroundColor: ['#FF6B6B', '#FFD166', '#06D6A0', '#118AB2', '#7c5cc4'],
                    borderColor: '#FFFFFF',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'bottom', labels: { font: { family: 'Cairo', size: 11 }, color: '#073B4C' } },
                    tooltip: { ...commonChartOptions.plugins.tooltip }
                }
            }
        });

        // Chart 3: Smart Medical Devices
        const smartDevicesCtx = document.getElementById('smartMedicalDevicesChart').getContext('2d');
        new Chart(smartDevicesCtx, {
            type: 'bar',
            data: {
                labels: [processLabel('مضخات الأنسولين الذكية'), processLabel('أجهزة قياس الجلوكوز المستمر'), processLabel('أجهزة الاستنشاق المتصلة'), processLabel('أجهزة مراقبة القلب المنزلية')],
                datasets: [{
                    label: 'معدل التبني المتوقع (تقديري)',
                    data: [60, 75, 50, 65], // Illustrative data (0-100 scale)
                    backgroundColor: ['#06D6A0', '#118AB2', '#FFD166', '#FF6B6B'],
                    borderRadius: 5
                }]
            },
            options: { ...commonChartOptions, indexAxis: 'y' }
        });
        
        // Chart 4: 5G Benefits
        const fiveGCtx = document.getElementById('fiveGBenefitsChart').getContext('2d');
        new Chart(fiveGCtx, {
            type: 'bar',
            data: {
                labels: [processLabel('زمن الاستجابة (Latency)'), processLabel('عرض النطاق الترددي (Bandwidth)'), processLabel('كثافة الأجهزة (Device Density)')],
                datasets: [
                    {
                        label: 'شبكة 4G',
                        data: [50, 60, 40], // Illustrative comparative values
                        backgroundColor: '#FFD166', // Sunny Yellow
                        borderRadius: 5
                    },
                    {
                        label: 'شبكة 5G',
                        data: [95, 90, 85], // Illustrative comparative values
                        backgroundColor: '#06D6A0', // Turquoise Green
                        borderRadius: 5
                    }
                ]
            },
            options: { ...commonChartOptions }
        });

        // Chart 5: Cybersecurity Challenges
        const cybersecurityCtx = document.getElementById('cybersecurityChallengesChart').getContext('2d');
        new Chart(cybersecurityCtx, {
            type: 'radar',
            data: {
                labels: [processLabel('أمان الجهاز الطرفي'), processLabel('أمان الشبكة'), processLabel('تشفير البيانات'), processLabel('خصوصية المستخدم'), processLabel('البرامج الضارة'), processLabel('الوصول غير المصرح به')],
                datasets: [{
                    label: 'مستوى التحدي (تقديري)',
                    data: [8, 7, 9, 8, 6, 7], // Illustrative data (scale 1-10)
                    fill: true,
                    backgroundColor: 'rgba(255, 107, 107, 0.2)', // Coral transparent
                    borderColor: '#FF6B6B', // Coral
                    pointBackgroundColor: '#FF6B6B',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: '#FF6B6B'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: { ...commonChartOptions.plugins.tooltip }
                },
                scales: {
                    r: {
                        angleLines: { display: true, color: '#cccccc' },
                        suggestedMin: 0,
                        suggestedMax: 10,
                        pointLabels: { font: { family: 'Cairo', size: 10 }, color: '#073B4C' },
                        grid: { color: '#e0e0e0' },
                        ticks: { display: true, backdropColor: 'transparent', stepSize: 2, font: {size: 9}}
                    }
                }
            }
        });

    </script>
</body>
</html>
