<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transistor Water Valve Analogy</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        #app-container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 700px; /* Increased max-width to better accommodate canvas and controls */
        }

        h1, h2 {
            text-align: center;
            color: #007bff;
        }
        h1 { margin-top: 0;}

        #controls {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }

        #controls div {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            flex-wrap: wrap; /* Allow wrapping for smaller screens */
        }

        #controls label {
            margin-right: 10px;
            font-weight: bold;
            min-width: 150px; /* Provide some space for labels */
        }

        #controls input[type="range"] {
            flex-grow: 1;
            min-width: 150px; /* Ensure slider is usable */
            max-width: 300px;
            cursor: pointer;
            margin-right: 10px; /* Space before the text label */
        }
        
        #controls button {
            padding: 8px 12px;
            margin-right: 5px;
            border: 1px solid #007bff;
            background-color: #fff;
            color: #007bff;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.2s, color 0.2s;
        }
        #controls button:hover {
            background-color: #e7f3ff;
        }

        #controls button.active {
            background-color: #007bff;
            color: #fff;
        }

        #baseCurrentLabel {
            font-style: italic;
            min-width: 120px; /* To prevent layout shifts */
            color: #555;
        }
        #transistorTypeLabel {
            font-weight: bold;
            margin-left: 10px;
            color: #0056b3;
        }

        #visualization {
            position: relative; /* For absolute positioning of labels */
            width: 100%;
            max-width: 500px; /* Canvas width */
            margin: 20px auto; /* Center canvas container and add some margin */
            border: 1px solid #ccc;
            background-color: #e9f5ff; /* Light blue background for water theme */
            overflow: hidden; /* Clip anything drawn outside by mistake */
        }

        #transistorCanvas {
            display: block; /* Remove extra space below canvas */
            width: 100%; /* Make canvas responsive within its container */
            height: auto; /* Maintain aspect ratio based on canvas element's width/height */
        }
        
        .component-label {
            position: absolute;
            background-color: rgba(255, 255, 255, 0.85);
            padding: 3px 6px;
            border-radius: 3px;
            font-size: 0.85em;
            font-weight: bold;
            color: #333;
            border: 1px solid #ccc;
            pointer-events: none; /* So they don't interfere with canvas events if any */
            text-align: center;
        }

        #explanation {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            font-size: 0.95em;
            line-height: 1.6;
        }
        #explanation strong {
            color: #0056b3;
        }
        #explanation ul {
            padding-left: 20px;
        }


        /* Responsive adjustments */
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            #app-container {
                padding: 10px;
            }
            #controls div {
                /* flex-direction: column; // Keep as row but allow wrap */
                align-items: flex-start; /* Align items to start for better vertical stacking if wrapped */
            }
            #controls label {
                margin-bottom: 5px; /* Space when wrapped */
                 min-width: unset; /* Allow label to be shorter */
            }
            #controls input[type="range"] {
                width: calc(100% - 130px); /* Adjust width to fit with label */
            }
            #baseCurrentLabel {
                width: 100%; /* Take full width if wrapped */
                margin-left: 0;
                margin-top: 5px;
                text-align: left;
            }
        }

    </style>
</head>
<body>
    <div id="app-container">
        <h1>Transistor Water Valve Analogy</h1>

        <div id="controls">
            <h2>Controls</h2>
            <div>
                <label>Transistor Type:</label>
                <button id="npnBtn" class="active">NPN</button>
                <button id="pnpBtn">PNP</button>
                <span id="transistorTypeLabel">NPN</span>
            </div>
            <div>
                <label for="baseCurrentSlider">Base "Control Signal":</label>
                <input type="range" id="baseCurrentSlider" min="0" max="100" value="0">
                <span id="baseCurrentLabel">Flow: OFF</span>
            </div>
        </div>

        <div id="visualization">
            <canvas id="transistorCanvas"></canvas>
            <div id="labelPower" class="component-label">Power</div>
            <div id="labelCollector" class="component-label">Collector</div>
            <div id="labelEmitter" class="component-label">Emitter</div>
            <div id="labelBase" class="component-label">Base</div>
            <div id="labelGround" class="component-label">Ground</div>
        </div>

        <div id="explanation">
            <p>This interactive app demonstrates how a transistor can act like a controllable water valve.</p>
            <ul>
                <li>Adjust the <strong>Base "Control Signal"</strong> slider. This is like turning the handle of a valve.</li>
                <li>Observe the "water flow" (representing electrical current) through the main pipe.</li>
                <li><strong>Cutoff State:</strong> When the Base Signal is zero, the valve is closed, and no water flows.</li>
                <li><strong>Active Region:</strong> As you increase the Base Signal, the valve opens, and more water flows.</li>
                <li><strong>Saturation State:</strong> At a certain point, the valve is fully open. Further increasing the Base Signal has no more effect on the water flow.</li>
                <li>Switch between <strong>NPN</strong> and <strong>PNP</strong> transistor types. Notice how the symbol changes (the arrow direction) and how the "Collector" and "Emitter" pipes are conceptually arranged relative to the power source and ground. In this analogy, the base control always works to *increase* flow for simplicity.</li>
            </ul>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('transistorCanvas');
        const ctx = canvas.getContext('2d');

        const baseCurrentSlider = document.getElementById('baseCurrentSlider');
        const baseCurrentLabel = document.getElementById('baseCurrentLabel');
        const npnBtn = document.getElementById('npnBtn');
        const pnpBtn = document.getElementById('pnpBtn');
        const transistorTypeLabelEl = document.getElementById('transistorTypeLabel');

        const labelPowerEl = document.getElementById('labelPower');
        const labelCollectorEl = document.getElementById('labelCollector');
        const labelEmitterEl = document.getElementById('labelEmitter');
        const labelBaseEl = document.getElementById('labelBase');
        const labelGroundEl = document.getElementById('labelGround');

        let transistorType = 'NPN';
        let baseCurrent = 0; 

        const CANVAS_WIDTH = 500;
        const CANVAS_HEIGHT = 300;
        canvas.width = CANVAS_WIDTH;
        canvas.height = CANVAS_HEIGHT;

        const PIPE_THICKNESS = 24; // Renamed from PIPE_WIDTH for clarity
        const TRANSISTOR_CX = CANVAS_WIDTH / 2;
        const TRANSISTOR_CY = CANVAS_HEIGHT / 2;
        const TRANSISTOR_R = 45;
        const VALVE_GATE_WIDTH = 10; // Thickness of valve gate
        const MAX_VALVE_OPENING_GAP = PIPE_THICKNESS * 0.9; 

        const POWER_SOURCE_X = 60;
        const POWER_SOURCE_Y = CANVAS_HEIGHT / 2;
        const GROUND_X = CANVAS_WIDTH - 60;
        const GROUND_Y = CANVAS_HEIGHT / 2;

        const BASE_PIPE_X = TRANSISTOR_CX;
        const BASE_PIPE_Y_START = TRANSISTOR_CY + TRANSISTOR_R + 5;
        const BASE_PIPE_Y_END = TRANSISTOR_CY + TRANSISTOR_R + 30;


        let particles = [];
        const MAX_PARTICLES = 60;
        const PARTICLE_RADIUS = 3.5;
        const PARTICLE_BASE_SPEED = 1.5;

        const SATURATION_BASE_THRESHOLD = 70;
        const GAIN_FACTOR = 1.5; 

        function updateDynamicLabels() {
            labelPowerEl.style.left = (POWER_SOURCE_X - 30) + 'px';
            labelPowerEl.style.top = (POWER_SOURCE_Y - 45) + 'px';

            labelGroundEl.style.left = (GROUND_X - 30) + 'px';
            labelGroundEl.style.top = (GROUND_Y + 20) + 'px';
            
            labelBaseEl.style.left = (BASE_PIPE_X - 20) + 'px';
            labelBaseEl.style.top = (BASE_PIPE_Y_END + 5) + 'px';

            const inputPipeLabelX = TRANSISTOR_CX - TRANSISTOR_R - PIPE_THICKNESS - 15;
            const outputPipeLabelX = TRANSISTOR_CX + TRANSISTOR_R + PIPE_THICKNESS - 15;
            const pipeLabelY = TRANSISTOR_CY - PIPE_THICKNESS - 30;

            if (transistorType === 'NPN') {
                labelCollectorEl.textContent = 'Collector';
                labelEmitterEl.textContent = 'Emitter';
                labelCollectorEl.style.left = inputPipeLabelX + 'px';
                labelCollectorEl.style.top = pipeLabelY + 'px';
                labelEmitterEl.style.left = outputPipeLabelX + 'px';
                labelEmitterEl.style.top = pipeLabelY + 'px';
            } else { 
                labelEmitterEl.textContent = 'Emitter'; 
                labelCollectorEl.textContent = 'Collector'; 
                labelEmitterEl.style.left = inputPipeLabelX + 'px';
                labelEmitterEl.style.top = pipeLabelY + 'px';
                labelCollectorEl.style.left = outputPipeLabelX + 'px';
                labelCollectorEl.style.top = pipeLabelY + 'px';
            }
        }

        function calculateCollectorCurrentVisual() {
            if (baseCurrent === 0) return 0;
            let flow = baseCurrent * GAIN_FACTOR;
            if (baseCurrent >= SATURATION_BASE_THRESHOLD) {
                flow = SATURATION_BASE_THRESHOLD * GAIN_FACTOR; 
            }
            return Math.min(flow, 100); // Max visual flow percentage
        }
        
        function updateBaseCurrentSliderLabel() {
            const visualFlow = calculateCollectorCurrentVisual();
            if (baseCurrent == 0) {
                baseCurrentLabel.textContent = "Flow: OFF";
            } else if (baseCurrent >= SATURATION_BASE_THRESHOLD && visualFlow >= 99) {
                baseCurrentLabel.textContent = "Flow: SATURATED";
            } else if (baseCurrent < SATURATION_BASE_THRESHOLD / 3) {
                baseCurrentLabel.textContent = "Flow: LOW";
            } else if (baseCurrent < (SATURATION_BASE_THRESHOLD * 2) / 3) {
                baseCurrentLabel.textContent = "Flow: MEDIUM";
            } else {
                baseCurrentLabel.textContent = "Flow: HIGH";
            }
        }

        function drawPowerSourceVisual() {
            ctx.fillStyle = '#f0ad4e';
            ctx.fillRect(POWER_SOURCE_X - 20, POWER_SOURCE_Y - 30, 40, 60);
            ctx.strokeStyle = '#c88f2a';
            ctx.lineWidth = 2;
            ctx.strokeRect(POWER_SOURCE_X - 20, POWER_SOURCE_Y - 30, 40, 60);
        }

        function drawGroundVisual() {
            ctx.strokeStyle = '#555';
            ctx.lineWidth = 3;
            for (let i = 0; i < 3; i++) {
                ctx.beginPath();
                ctx.moveTo(GROUND_X - (15 - i*5), GROUND_Y + 10 + i*6);
                ctx.lineTo(GROUND_X + (15 - i*5), GROUND_Y + 10 + i*6);
                ctx.stroke();
            }
        }
        
        function drawMainPipes() {
            ctx.strokeStyle = '#9db2bf'; 
            ctx.lineWidth = PIPE_THICKNESS;
            ctx.lineCap = 'butt'; // Use butt for cleaner connections to transistor

            // Input pipe
            ctx.beginPath();
            ctx.moveTo(POWER_SOURCE_X, POWER_SOURCE_Y);
            ctx.lineTo(TRANSISTOR_CX - TRANSISTOR_R, TRANSISTOR_CY);
            ctx.stroke();

            // Output pipe
            ctx.beginPath();
            ctx.moveTo(TRANSISTOR_CX + TRANSISTOR_R, TRANSISTOR_CY);
            ctx.lineTo(GROUND_X, GROUND_Y);
            ctx.stroke();
            
            // Base pipe
            ctx.lineWidth = PIPE_THICKNESS / 2;
            ctx.beginPath();
            ctx.moveTo(BASE_PIPE_X, BASE_PIPE_Y_START);
            ctx.lineTo(BASE_PIPE_X, BASE_PIPE_Y_END);
            ctx.stroke();
        }

        function drawTransistorSymbol() {
            ctx.fillStyle = '#d1e7f3'; 
            ctx.strokeStyle = '#4a7c9b'; 
            ctx.lineWidth = 3;

            ctx.beginPath();
            ctx.arc(TRANSISTOR_CX, TRANSISTOR_CY, TRANSISTOR_R, 0, Math.PI * 2);
            ctx.fill();
            ctx.stroke();

            // Internal base plate
            const plateY = TRANSISTOR_CY;
            const plateLeftX = TRANSISTOR_CX - TRANSISTOR_R * 0.5;
            const plateRightX = TRANSISTOR_CX + TRANSISTOR_R * 0.5;
            ctx.beginPath();
            ctx.moveTo(plateLeftX, plateY);
            ctx.lineTo(plateRightX, plateY);
            ctx.lineWidth = 3;
            ctx.strokeStyle = '#31708f';
            ctx.stroke();

            // Connections to plate
            const cExtX = plateLeftX - TRANSISTOR_R * 0.3;
            const cExtY = plateY - TRANSISTOR_R * 0.4;
            const eExtX = plateRightX + TRANSISTOR_R * 0.3;
            const eExtY = plateY + TRANSISTOR_R * 0.4;
            const baseConX = TRANSISTOR_CX;
            const baseConY = BASE_PIPE_Y_START - 2; // Connect to where base pipe meets transistor

            ctx.lineWidth = 2.5;
            ctx.strokeStyle = '#333';

            // Base connection line
            ctx.beginPath();
            ctx.moveTo(baseConX, baseConY); // From external base pipe
            ctx.lineTo(TRANSISTOR_CX, plateY); // To center of plate
            ctx.stroke();

            let emitterLineP1, emitterLineP2, collectorLineP1, collectorLineP2;
            let arrowPoint, arrowAngle;

            if (transistorType === 'NPN') {
                collectorLineP1 = { x: plateLeftX, y: plateY };
                collectorLineP2 = { x: cExtX, y: cExtY };
                emitterLineP1 = { x: plateRightX, y: plateY };
                emitterLineP2 = { x: eExtX, y: eExtY };
                arrowPoint = emitterLineP2;
                arrowAngle = Math.atan2(emitterLineP2.y - emitterLineP1.y, emitterLineP2.x - emitterLineP1.x);
            } else { // PNP
                emitterLineP1 = { x: plateLeftX, y: plateY }; // Emitter on left for PNP symbol
                emitterLineP2 = { x: cExtX, y: cExtY };
                collectorLineP1 = { x: plateRightX, y: plateY };
                collectorLineP2 = { x: eExtX, y: eExtY };
                arrowPoint = emitterLineP2; // Arrow at end of emitter line
                arrowAngle = Math.atan2(emitterLineP1.y - emitterLineP2.y, emitterLineP1.x - emitterLineP2.x); // Pointing towards plate
            }

            // Draw Collector line
            ctx.beginPath();
            ctx.moveTo(collectorLineP1.x, collectorLineP1.y);
            ctx.lineTo(collectorLineP2.x, collectorLineP2.y);
            ctx.stroke();
            
            // Draw Emitter line
            ctx.beginPath();
            ctx.moveTo(emitterLineP1.x, emitterLineP1.y);
            ctx.lineTo(emitterLineP2.x, emitterLineP2.y);
            ctx.stroke();
            
            // Draw Arrow on Emitter line
            drawArrowSymbol(arrowPoint.x, arrowPoint.y, arrowAngle, 10);
        }
        
        function drawArrowSymbol(x, y, angle, size) {
            ctx.save();
            ctx.translate(x, y);
            ctx.rotate(angle);
            ctx.beginPath();
            ctx.moveTo(0, 0); // Tip of arrow is at (x,y)
            ctx.lineTo(-size, -size / 2.5);
            ctx.lineTo(-size, size / 2.5);
            ctx.closePath();
            ctx.fillStyle = '#333';
            ctx.fill();
            ctx.restore();
        }

        function drawValveVisual(collectorCurrentVisual) {
            const flowProportion = collectorCurrentVisual / 100; 
            const currentOpeningGap = flowProportion * MAX_VALVE_OPENING_GAP;

            // Valve gates are vertical, moving horizontally, OR horizontal moving vertically.
            // For horizontal pipe, horizontal gates moving vertically is more intuitive for "valve".
            const gateHeight = (PIPE_THICKNESS - currentOpeningGap) / 2;

            ctx.fillStyle = '#7a92a1'; 

            // Upper gate
            if (gateHeight > 0) { // Only draw if there's a gate
                ctx.fillRect(TRANSISTOR_CX - VALVE_GATE_WIDTH / 2, TRANSISTOR_CY - PIPE_THICKNESS / 2, VALVE_GATE_WIDTH, gateHeight);
            }
            // Lower gate
            if (gateHeight > 0) {
                ctx.fillRect(TRANSISTOR_CX - VALVE_GATE_WIDTH / 2, TRANSISTOR_CY + PIPE_THICKNESS / 2 - gateHeight, VALVE_GATE_WIDTH, gateHeight);
            }
        }

        function manageWaterParticles(collectorCurrentVisual) {
            const flowProportion = collectorCurrentVisual / 100;

            if (flowProportion > 0.01 && particles.length < MAX_PARTICLES) {
                const numNewParticles = Math.min(MAX_PARTICLES - particles.length, Math.ceil(flowProportion * 2));
                for(let i=0; i < numNewParticles ; i++) {
                    particles.push({
                        x: POWER_SOURCE_X + PIPE_THICKNESS/2, 
                        y: TRANSISTOR_CY + (Math.random() - 0.5) * (PIPE_THICKNESS * 0.7),
                        vx: PARTICLE_BASE_SPEED + flowProportion * PARTICLE_BASE_SPEED * 1.5, 
                        vy: (Math.random() - 0.5) * 0.3 
                    });
                }
            }

            ctx.fillStyle = '#3498db'; 
            for (let i = particles.length - 1; i >= 0; i--) {
                let p = particles[i];
                p.x += p.vx;
                p.y += p.vy;

                const currentOpeningGap = flowProportion * MAX_VALVE_OPENING_GAP;
                const gateTopEdge = TRANSISTOR_CY - currentOpeningGap / 2;
                const gateBottomEdge = TRANSISTOR_CY + currentOpeningGap / 2;

                if (p.x > TRANSISTOR_CX - VALVE_GATE_WIDTH / 2 && p.x < TRANSISTOR_CX + VALVE_GATE_WIDTH / 2) { 
                    if (p.y < gateTopEdge || p.y > gateBottomEdge) { 
                       if (flowProportion > 0.01) { 
                           p.x -= p.vx * 1.5; 
                           p.vx *= -0.2; 
                       } else { 
                           particles.splice(i, 1);
                           continue;
                       }
                    }
                }
                
                if (p.y < TRANSISTOR_CY - PIPE_THICKNESS/2 + PARTICLE_RADIUS || 
                    p.y > TRANSISTOR_CY + PIPE_THICKNESS/2 - PARTICLE_RADIUS) {
                    p.vy *= -0.8; 
                    p.y = Math.max(TRANSISTOR_CY - PIPE_THICKNESS/2 + PARTICLE_RADIUS, Math.min(p.y, TRANSISTOR_CY + PIPE_THICKNESS/2 - PARTICLE_RADIUS));
                }

                if (p.x > GROUND_X + PIPE_THICKNESS/2) {
                    particles.splice(i, 1);
                    continue;
                }
                
                ctx.beginPath();
                ctx.arc(p.x, p.y, PARTICLE_RADIUS, 0, Math.PI * 2);
                ctx.fill();
            }
        }
        
        function mainDrawLoop() {
            ctx.clearRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
            
            const collectorCurrentVisual = calculateCollectorCurrentVisual();

            drawMainPipes(); 
            drawPowerSourceVisual();
            drawGroundVisual();
            
            drawTransistorSymbol(); 
            drawValveVisual(collectorCurrentVisual); 

            manageWaterParticles(collectorCurrentVisual);
            
            // No need to call updateDynamicLabels here, it's called on type change / init.

            requestAnimationFrame(mainDrawLoop);
        }

        baseCurrentSlider.addEventListener('input', (e) => {
            baseCurrent = parseInt(e.target.value);
            updateBaseCurrentSliderLabel();
        });

        npnBtn.addEventListener('click', () => {
            if (transistorType === 'NPN') return;
            transistorType = 'NPN';
            npnBtn.classList.add('active');
            pnpBtn.classList.remove('active');
            transistorTypeLabelEl.textContent = 'NPN';
            particles = []; 
            updateDynamicLabels(); 
        });

        pnpBtn.addEventListener('click', () => {
            if (transistorType === 'PNP') return;
            transistorType = 'PNP';
            pnpBtn.classList.add('active');
            npnBtn.classList.remove('active');
            transistorTypeLabelEl.textContent = 'PNP';
            particles = []; 
            updateDynamicLabels(); 
        });
        
        updateBaseCurrentSliderLabel();
        updateDynamicLabels();
        transistorTypeLabelEl.textContent = 'NPN'; // Initial state
        mainDrawLoop();

    </script>
</body>
</html>
