# Electronic Circuits Lab - Windows Application

This package contains the Electronic Circuits Lab application, a virtual simulation workbench for electronic components library and drawing.

## System Requirements

- Windows 7 or later (64-bit)
- 4 GB RAM (minimum)
- 500 MB free disk space
- 1280x720 screen resolution (minimum)

## Installation Instructions

### Option 1: Using the Pre-built Installer

If you have received a pre-built installer (`.exe` file):

1. Double-click the installer file to start the installation process.
2. Follow the on-screen instructions to complete the installation.
3. Launch the application from the Start Menu or desktop shortcut.

### Option 2: Building the Application from Source

If you need to build the application from source:

1. **Install Node.js**:
   - Run `download-nodejs.bat` to download the Node.js installer.
   - Follow the installation instructions.
   - Restart your computer after installation.

2. **Build the Application**:
   - Run `build-exe.bat` to build the application.
   - This process may take several minutes.
   - Once completed, the installer will be available in the `electron-app\dist` directory.

3. **Run the Application**:
   - Run `run-app.bat` to launch the installer.
   - Follow the installation instructions.
   - Launch the application from the Start Menu or desktop shortcut.

## Features

- Interactive circuit drawing and editing
- Comprehensive electronic component library
- Circuit simulation capabilities
- Save and load circuit designs
- Export circuits as images
- Lab notes with common transistor circuit experiments

## Troubleshooting

If you encounter any issues:

1. **Application doesn't start**:
   - Make sure your computer meets the system requirements.
   - Try reinstalling the application.

2. **Build process fails**:
   - Make sure Node.js is properly installed.
   - Check your internet connection.
   - Try running the build script again.

3. **Graphics issues**:
   - Update your graphics drivers to the latest version.

## Support

If you need assistance, please contact:

- Email: <EMAIL>
- Website: www.example.com/support
