<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transistor Basics Explorer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 20px auto;
            padding: 15px;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            border-radius: 8px;
        }
        nav {
            display: flex;
            justify-content: center;
            margin-bottom: 0; /* Remove margin for seamless connection with content */
            background-color: #333;
            border-radius: 5px 5px 0 0; /* Rounded corners only at the top */
        }
        nav button {
            background-color: #333;
            color: white;
            border: none;
            padding: 12px 20px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
            flex-grow: 1;
            border-right: 1px solid #444; /* Separator */
        }
        nav button:last-child {
            border-right: none;
        }
        nav button:hover {
            background-color: #555;
        }
        nav button.active {
            background-color: #007bff;
        }
        .tab-content {
            display: none;
            padding: 20px; /* Increased padding */
            border: 1px solid #ddd;
            border-top: none; /* Tabs provide top border */
            border-radius: 0 0 5px 5px;
            background-color: #fff; /* Ensure content area has white background */
        }
        .tab-content.active {
            display: block;
        }
        h1, h2, h3, h4 {
            color: #0056b3;
        }
        h1 { text-align: center; margin-bottom: 20px; }
        h2 { margin-top: 0; border-bottom: 1px solid #eee; padding-bottom: 10px; }

        /* Interactive Circuit Section */
        .circuit-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .schematic-area {
            flex: 2; /* Give more space to schematic */
            min-width: 300px;
        }
        .schematic-area svg {
            max-width: 100%;
            height: auto;
            border:1px solid #ccc; 
            margin: 10px auto; 
            display: block; 
            background: #f9f9f9;
        }
        .controls-area {
            flex: 1;
            min-width: 280px; /* Slightly wider controls */
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
        }
        .control-group {
            margin-bottom: 15px;
        }
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .control-group input[type="range"] {
            width: 100%;
            cursor: pointer;
        }
        .value-display {
            font-weight: bold;
            color: #007bff;
        }
        .output-values p {
            margin: 8px 0;
            font-size: 1.05em; /* Slightly larger text */
        }
        .output-values strong {
            color: #28a745; /* Green for values */
            min-width: 80px; /* Align values */
            display: inline-block;
        }
        .circuit-params {
            font-size: 0.9em;
            color: #555;
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            margin-top: 10px;
        }


        /* Operating Regions Section */
        .regions-container {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
        }
        .region-box {
            flex: 1;
            min-width: 200px;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 5px;
            text-align: center;
            transition: all 0.3s ease-in-out;
        }
        .region-box h3 { margin-top: 0; }
        .region-box.highlighted-region {
            border-color: #007bff;
            background-color: #e7f3ff;
            box-shadow: 0 0 10px rgba(0,123,255,0.4);
            transform: translateY(-3px);
        }
        #current-region-description {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #ced4da;
            border-radius: 4px;
            text-align: center;
        }
        #current-region-description strong {
            font-size: 1.1em;
            color: #0056b3;
        }
        .graph-container {
            margin-top: 25px;
            text-align: center;
        }
        .graph-container canvas {
            border: 1px solid #aaa;
            background-color: #fff;
            max-width: 100%;
            height: auto; /* Maintain aspect ratio */
            display: block; /* Center canvas */
            margin: 0 auto;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            nav {
                flex-direction: column;
                border-radius: 5px; /* Full radius if stacked */
            }
            nav button {
                border-radius: 0;
                border-bottom: 1px solid #444;
            }
            nav button:last-child {
                border-bottom: none;
                border-radius: 0 0 5px 5px; /* Bottom radius for last button */
            }
            nav button:first-child {
                border-radius: 5px 5px 0 0; /* Top radius for first button */
            }
            .circuit-container {
                flex-direction: column;
            }
        }
        @media (max-width: 480px) {
            .regions-container {
                flex-direction: column;
            }
            .region-box {
                min-width: 100%;
            }
            .container {
                margin: 10px;
                padding: 10px;
            }
            h1 { font-size: 1.5em; }
            nav button { padding: 10px 15px; font-size: 1em; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Transistor Basics Explorer</h1>
        <nav>
            <button id="tab-btn-intro" onclick="showTab('intro')">Introduction</button>
            <button id="tab-btn-circuit" onclick="showTab('circuit')">Interactive Circuit</button>
            <button id="tab-btn-regions" onclick="showTab('regions')">Operating Regions</button>
        </nav>

        <div id="intro" class="tab-content">
            <h2>Welcome to the Transistor Basics Explorer!</h2>
            <p>Transistors are fundamental semiconductor devices that act as the building blocks for most modern electronic circuits. They typically have three terminals: the <strong>Base (B)</strong>, the <strong>Collector (C)</strong>, and the <strong>Emitter (E)</strong>.</p>
            <h3>Basic Function</h3>
            <p>A transistor primarily performs two key functions:</p>
            <ul>
                <li><strong>Switching:</strong> It can act as an electronically controlled switch. A small current at the base can turn a much larger current between the collector and emitter ON or OFF. This is the foundation of digital logic and computers.</li>
                <li><strong>Amplification:</strong> It can take a small, varying input signal (e.g., at the base) and produce a much stronger, but similarly varying, output signal (e.g., at the collector). This is vital for audio amplifiers, radio transmitters, and many other analog applications.</li>
            </ul>
            <p>This interactive application focuses on the NPN Bipolar Junction Transistor (BJT). You'll explore how the base current (Ib) controls the collector current (Ic) and how this relationship defines the transistor's operating state.</p>
            <p>Use the tabs above to learn more:</p>
            <ul>
                <li><strong>Interactive Circuit:</strong> Experiment with a virtual NPN transistor circuit. Adjust the base resistor and observe the changes in currents and voltages.</li>
                <li><strong>Operating Regions:</strong> Understand the three distinct modes of transistor operation – Cut-off, Active, and Saturation – and see how your circuit adjustments affect them.</li>
            </ul>
        </div>

        <div id="circuit" class="tab-content">
            <h2>Interactive NPN Transistor Circuit</h2>
            <div class="circuit-container">
                <div class="schematic-area">
                    <h3>Circuit Diagram (NPN BJT)</h3>
                    <svg width="320" height="240" viewBox="0 0 320 240">
                        <!-- VCC -->
                        <text x="155" y="20" font-size="12">VCC (+9V)</text>
                        <line x1="160" y1="25" x2="160" y2="40" stroke="black" stroke-width="1.5"/>

                        <!-- Rc -->
                        <rect x="145" y="40" width="30" height="15" stroke="black" fill="white" stroke-width="1.5"/>
                        <text x="180" y="48" font-size="10">Rc</text>
                        <text x="180" y="60" font-size="10">(<tspan id="rc_value_text_svg">0.47</tspan>kΩ)</text>
                        <line x1="160" y1="55" x2="160" y2="75" stroke="black" stroke-width="1.5"/>
                        <circle cx="160" cy="75" r="3" fill="blue"/>
                        <text x="168" y="83" font-size="10" fill="blue">Vc: <tspan id="vc_diagram_val">?</tspan></text>

                        <!-- Transistor NPN (Simplified symbol) -->
                        <line x1="160" y1="75" x2="160" y2="95" stroke="black" stroke-width="1.5"/> <!-- Collector lead -->
                        <text x="165" y="93" font-size="10">C</text>
                        <rect x="140" y="95" width="40" height="5" stroke="black" fill="lightgray" stroke-width="1.5"/> <!-- Base bar -->
                        <line x1="100" y1="97.5" x2="140" y2="97.5" stroke="black" stroke-width="1.5"/> <!-- Base lead -->
                        <text x="85" y="100" font-size="10">B</text>
                        <line x1="160" y1="100" x2="160" y2="120" stroke="black" stroke-width="1.5"/> <!-- Emitter lead -->
                        <text x="165" y="118" font-size="10">E</text>
                        <!-- Emitter arrow for NPN -->
                        <polygon points="160,115 165,120 155,120" fill="black"/>

                        <!-- Ground -->
                        <line x1="160" y1="120" x2="160" y2="130" stroke="black" stroke-width="1.5"/>
                        <line x1="145" y1="130" x2="175" y2="130" stroke="black" stroke-width="1.5"/>
                        <line x1="150" y1="135" x2="170" y2="135" stroke="black" stroke-width="1.5"/>
                        <line x1="155" y1="140" x2="165" y2="140" stroke="black" stroke-width="1.5"/>
                        <text x="150" y="155" font-size="10">GND</text>

                        <!-- Base Resistor Rb -->
                        <line x1="40" y1="25" x2="40" y2="90" stroke="black" stroke-width="1.5"/> <!-- VCC line for base -->
                        <text x="5" y="20" font-size="10">VCC for Base</text>
                        <line x1="40" y1="90" x2="55" y2="90" stroke="black" stroke-width="1.5"/>
                        <rect x="55" y="82.5" width="30" height="15" stroke="black" fill="white" stroke-width="1.5"/>
                        <text x="52" y="78" font-size="10">Rb (Var)</text>
                        <line x1="85" y1="90" x2="100" y2="97.5" stroke="black" stroke-width="1.5"/> <!-- Rb to Base -->
                        <!-- Potentiometer symbol -->
                        <line x1="70" y1="82.5" x2="70" y2="75" stroke="black" stroke-width="1.5"/>
                        <polygon points="70,75 67,79 73,79" fill="black" transform="rotate(180 70 75)"/>
                    </svg>
                </div>
                <div class="controls-area">
                    <h3>Controls & Readings</h3>
                    <div class="control-group">
                        <label for="rbSlider">Base Resistor (Rb): <span id="rbValue" class="value-display">100.0 kΩ</span></label>
                        <input type="range" id="rbSlider" min="1000" max="200000" step="100" value="100000">
                    </div>
                    <div class="output-values">
                        <p>Base Current (Ib): <strong id="ibVal">0.0 µA</strong></p>
                        <p>Collector Current (Ic): <strong id="icVal">0.00 mA</strong></p>
                        <p>Collector Voltage (Vc): <strong id="vcVal">9.00 V</strong></p>
                    </div>
                    <div class="circuit-params">
                        <h4>Circuit Parameters:</h4>
                        <p><small>VCC = 9V, Collector Resistor (Rc) = 470Ω, Transistor β (Beta/hFE) = 100, Vbe(on) ≈ 0.7V, Vce(sat) ≈ 0.2V. Emitter is grounded.</small></p>
                    </div>
                </div>
            </div>
        </div>

        <div id="regions" class="tab-content">
            <h2>Operating Regions of a Transistor</h2>
            <p>A transistor's behavior changes drastically depending on the currents and voltages applied. These behaviors are categorized into three main operating regions:</p>
            <div class="regions-container">
                <div id="region-cutoff" class="region-box">
                    <h3>Cut-off</h3>
                    <p id="desc-cutoff">The transistor is <strong>OFF</strong>. No significant current flows from collector to emitter (Ic ≈ 0). It acts like an open switch. This happens when the base current (Ib) is too low (or zero) to turn the transistor on (i.e., Vbe < Vbe_on).</p>
                </div>
                <div id="region-active" class="region-box">
                    <h3>Active</h3>
                    <p id="desc-active">The transistor acts as an <strong>amplifier</strong>. The collector current (Ic) is proportional to the base current (Ib), following the relation Ic = β × Ib (where β is the current gain). The collector-emitter voltage (Vce) is between Vce(sat) and Vcc.</p>
                </div>
                <div id="region-saturation" class="region-box">
                    <h3>Saturation</h3>
                    <p id="desc-saturation">The transistor is fully <strong>ON</strong>. Collector current (Ic) is at its maximum, limited by the collector resistor (Rc) and power supply, not by base current. It acts like a closed switch, with a small, nearly constant voltage drop (Vce ≈ Vce(sat)) across collector and emitter.</p>
                </div>
            </div>
            <div id="current-region-description">
                <p id="current-region-description-text">Adjust the Base Resistor in the "Interactive Circuit" tab to see the region change.</p>
            </div>
            <div class="graph-container">
                <h4>Ic vs. Vce Characteristic (Conceptual)</h4>
                <canvas id="regionGraph" width="380" height="280"></canvas> <!-- Slightly larger canvas -->
                <p><small>The red dot shows the current operating point (Vce, Ic). The gray lines conceptually represent the boundaries between regions.</small></p>
            </div>
        </div>
    </div>

    <script>
        // --- Constants ---
        const VCC = 9.0;        // Volts
        const RC_VAL = 470;     // Ohms (0.47 kΩ)
        const VBE_ON = 0.7;     // Volts
        const BETA = 100;       // Current Gain (hFE)
        const VCE_SAT = 0.2;    // Volts (Collector-Emitter Saturation Voltage)
        const RB_MIN = 1000;    // 1 kOhm
        const RB_MAX = 200000;  // 200 kOhm

        // --- DOM Elements ---
        let rbSlider, rbValueDisplay;
        let ibDisplay, icDisplay, vcDisplay;
        let regionCutoffEl, regionActiveEl, regionSaturationEl;
        let currentRegionTextEl;
        let vcDiagramDisplay;
        let rcValueTextSvg;

        let tabButtons = {};
        let tabContents = {};
        
        // --- Tab Switching Logic ---
        function showTab(tabName) {
            Object.keys(tabContents).forEach(key => {
                tabContents[key].classList.remove('active');
                tabButtons['tab-btn-' + key].classList.remove('active');
            });
            tabContents[tabName].classList.add('active');
            tabButtons['tab-btn-' + tabName].classList.add('active');
            // Redraw graph if switching to regions tab and it's initialized
            if (tabName === 'regions' && typeof drawRegionGraph === 'function') {
                 updateCircuit(); // Re-calculate and redraw
            }
        }

        // --- Circuit Calculation and Update Logic ---
        function updateCircuit() {
            if (!rbSlider) return; // Elements not ready yet

            let rb = parseFloat(rbSlider.value);
            rbValueDisplay.textContent = (rb / 1000).toFixed(1) + " kΩ";

            let ib = 0, ic = 0, vc = VCC; // vc is Vce as Emitter is grounded
            let region = "Cut-off";

            let voltageAcrossRb = VCC - VBE_ON;
            if (voltageAcrossRb > 0) {
                ib = voltageAcrossRb / rb;
            } else {
                ib = 0; 
            }
            
            if (ib < 1e-7) { // Threshold for "zero" base current (0.1 µA)
                ib = 0;
                ic = 0;
                vc = VCC;
                region = "Cut-off";
            } else {
                let ic_potential_active = BETA * ib;
                let vc_if_active = VCC - (ic_potential_active * RC_VAL);

                if (vc_if_active < VCE_SAT) {
                    region = "Saturation";
                    vc = VCE_SAT;
                    ic = (VCC - VCE_SAT) / RC_VAL;
                } else {
                    region = "Active";
                    ic = ic_potential_active;
                    vc = vc_if_active;
                }
            }

            ibDisplay.textContent = (ib * 1e6).toFixed(1) + " µA";
            icDisplay.textContent = (ic * 1e3).toFixed(2) + " mA";
            vcDisplay.textContent = vc.toFixed(2) + " V";
            
            if (vcDiagramDisplay) vcDiagramDisplay.textContent = vc.toFixed(2) + "V";
            if (rcValueTextSvg) rcValueTextSvg.textContent = (RC_VAL/1000).toFixed(2);

            updateOperatingRegionVisual(region, ib, ic, vc);
        }

        // --- Operating Region Visual Update ---
        function updateOperatingRegionVisual(region, ib_val, ic_val, vce_val) {
            if (!regionCutoffEl) return; // Elements not ready

            regionCutoffEl.classList.remove('highlighted-region');
            regionActiveEl.classList.remove('highlighted-region');
            regionSaturationEl.classList.remove('highlighted-region');

            let descriptionHtml = "";
            let currentRegionSimpleText = "";
            if (region === "Cut-off") {
                regionCutoffEl.classList.add('highlighted-region');
                descriptionHtml = document.getElementById('desc-cutoff').innerHTML;
                currentRegionSimpleText = "Cut-off";
            } else if (region === "Active") {
                regionActiveEl.classList.add('highlighted-region');
                descriptionHtml = document.getElementById('desc-active').innerHTML;
                currentRegionSimpleText = "Active";
            } else if (region === "Saturation") {
                regionSaturationEl.classList.add('highlighted-region');
                descriptionHtml = document.getElementById('desc-saturation').innerHTML;
                currentRegionSimpleText = "Saturation";
            }
            currentRegionTextEl.innerHTML = `<strong>Current Region: ${currentRegionSimpleText}</strong><br>${descriptionHtml}`;
            
            if (document.getElementById('regions').classList.contains('active')) {
                drawRegionGraph(ic_val, vce_val);
            }
        }

        function drawRegionGraph(current_ic, current_vce) {
            const canvas = document.getElementById('regionGraph');
            if (!canvas.getContext) return;
            const ctx = canvas.getContext('2d');
            
            const graphWidth = canvas.width;
            const graphHeight = canvas.height;
            const p = 40; // Padding

            ctx.clearRect(0, 0, graphWidth, graphHeight);
            ctx.fillStyle = "black";
            ctx.strokeStyle = "black";
            ctx.lineWidth = 1;
            ctx.font = "11px Arial";

            // Y-axis (Ic)
            ctx.beginPath();
            ctx.moveTo(p, p / 2); ctx.lineTo(p, graphHeight - p); ctx.stroke();
            ctx.save(); ctx.translate(p/2 - 5, graphHeight/2); ctx.rotate(-Math.PI/2);
            ctx.textAlign="center"; ctx.fillText("Ic (mA)", 0, 0); ctx.restore();

            // X-axis (Vce)
            ctx.beginPath();
            ctx.moveTo(p, graphHeight - p); ctx.lineTo(graphWidth - p / 2, graphHeight - p); ctx.stroke();
            ctx.fillText("Vce (V)", graphWidth / 2 -15, graphHeight - p / 2 + 5);
            
            // Max values for scaling graph
            const max_ic_amps = ((VCC - VCE_SAT) / RC_VAL) * 1.1; 
            const max_vce_volts = VCC * 1.1;

            // X-axis ticks and labels
            for(let i = 0; i <= VCC; i += Math.ceil(VCC/5)) {
                let x = p + (i / max_vce_volts) * (graphWidth - 1.5 * p);
                ctx.fillText(i.toString(), x - (i < 10 ? 3:7) , graphHeight - p + 15);
                ctx.beginPath(); ctx.moveTo(x, graphHeight - p -3); ctx.lineTo(x, graphHeight - p + 3); ctx.stroke();
            }

            // Y-axis ticks and labels
            const max_ic_mA_plot = max_ic_amps * 1000;
            for(let i = 0; i <= max_ic_mA_plot; i += Math.ceil(max_ic_mA_plot/5)) {
                let y = (graphHeight - p) - (i / max_ic_mA_plot) * (graphHeight - 1.5 * p);
                ctx.fillText(i.toFixed(0), p - 25, y + 3);
                 ctx.beginPath(); ctx.moveTo(p-3, y); ctx.lineTo(p+3, y); ctx.stroke();
            }

            // Conceptual region lines
            ctx.strokeStyle = "#aaa"; ctx.lineWidth = 1.5;
            let sat_x_coord = p + (VCE_SAT / max_vce_volts) * (graphWidth - 1.5 * p);
            ctx.beginPath(); ctx.moveTo(sat_x_coord, p / 2); ctx.lineTo(sat_x_coord, graphHeight - p); ctx.stroke();
            ctx.fillStyle = "#777"; ctx.fillText("Saturation", sat_x_coord - 15, p - 5);
            ctx.fillText("Cut-off", p + 5, graphHeight - p - 5);
            ctx.fillText("Active", graphWidth / 2 - 20, graphHeight / 2);
            ctx.strokeStyle = "black"; ctx.lineWidth = 1; // Reset

            // Plot the operating point
            let pointX = p + (current_vce / max_vce_volts) * (graphWidth - 1.5 * p);
            let pointY = (graphHeight - p) - (current_ic / max_ic_amps) * (graphHeight - 1.5 * p);
            
            pointX = Math.max(p, Math.min(graphWidth - p / 2, pointX));
            pointY = Math.max(p / 2, Math.min(graphHeight - p, pointY));
            
            ctx.beginPath(); ctx.fillStyle = "red";
            ctx.arc(pointX, pointY, 6, 0, 2 * Math.PI); ctx.fill();
            ctx.fillStyle = "black";
        }


        // --- Initial Setup ---
        window.onload = () => {
            tabButtons = {
                'tab-btn-intro': document.getElementById('tab-btn-intro'),
                'tab-btn-circuit': document.getElementById('tab-btn-circuit'),
                'tab-btn-regions': document.getElementById('tab-btn-regions')
            };
            tabContents = {
                'intro': document.getElementById('intro'),
                'circuit': document.getElementById('circuit'),
                'regions': document.getElementById('regions')
            };

            rbSlider = document.getElementById('rbSlider');
            rbValueDisplay = document.getElementById('rbValue');
            ibDisplay = document.getElementById('ibVal');
            icDisplay = document.getElementById('icVal');
            vcDisplay = document.getElementById('vcVal');
            vcDiagramDisplay = document.getElementById('vc_diagram_val');
            rcValueTextSvg = document.getElementById('rc_value_text_svg');

            regionCutoffEl = document.getElementById('region-cutoff');
            regionActiveEl = document.getElementById('region-active');
            regionSaturationEl = document.getElementById('region-saturation');
            currentRegionTextEl = document.getElementById('current-region-description-text');
            
            rbSlider.min = RB_MIN;
            rbSlider.max = RB_MAX;
            rbSlider.step = 100;
            rbSlider.value = RB_MAX / 2; 
            rbSlider.oninput = updateCircuit;

            showTab('intro'); 
            updateCircuit();  
        };
    </script>
</body>
</html>
