<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transistor Mode Explorer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: auto;
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        header h1 {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }

        .main-content {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .schematic-area {
            flex: 1;
            min-width: 300px; /* Ensures schematic has enough space */
        }

        .controls-calculations {
            flex: 1;
            min-width: 300px; /* Ensures controls have enough space */
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .controls, .calculations, .equations-display, .mode-explanation-box {
            background-color: #e9e9e9;
            padding: 15px;
            border-radius: 5px;
        }

        h2 {
            margin-top: 0;
            color: #555;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input[type="range"], input[type="number"] {
            width: calc(100% - 20px); /* Adjust for padding/border if any */
            padding: 8px;
            margin-bottom: 10px;
            border-radius: 4px;
            border: 1px solid #ccc;
            box-sizing: border-box;
        }
        
        input[type="number"] {
            width: 100px; /* More appropriate for number inputs */
            margin-right: 5px;
        }

        .control-group {
            margin-bottom: 15px;
        }
        .control-group label {
            display: inline-block; /* For side-by-side label and input */
            margin-right: 10px;
            min-width: 180px;
        }


        #circuitSchematic {
            width: 100%;
            max-width: 450px; /* Max width for the SVG */
            height: auto; /* Maintain aspect ratio */
            border: 1px solid #ccc;
            border-radius: 4px;
            display: block; /* Center if margin auto is used on parent */
            margin-left: auto;
            margin-right: auto;
        }

        #transistorSymbolBody {
            transition: fill 0.3s ease;
        }

        #equationsText {
            white-space: pre-wrap;
            background-color: #fff;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9em;
        }

        #transistorMode {
            font-weight: bold;
        }
        
        .mode-explanation-box p strong {
             display: block; margin-bottom: 5px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            input[type="number"] {
                width: calc(100% - 10px); /* Full width on mobile */
            }
            .control-group label {
                min-width: 120px; /* Adjust label width */
            }
        }

    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>NPN Transistor Common-Emitter Circuit Explorer</h1>
        </header>

        <div class="main-content">
            <div class="schematic-area">
                <h2>Circuit Schematic</h2>
                <svg id="circuitSchematic" viewBox="0 0 400 300" preserveAspectRatio="xMidYMid meet">
                    <!-- VCC -->
                    <circle cx="100" cy="30" r="15" stroke="black" stroke-width="1" fill="#fff" />
                    <text x="93" y="35" font-size="10">VCC</text>
                    <line x1="100" y1="45" x2="100" y2="70" stroke="black" stroke-width="1" />
                    
                    <!-- RC -->
                    <rect x="85" y="70" width="30" height="15" stroke="black" stroke-width="1" fill="#fff"/>
                    <text x="120" y="80" font-size="10">RC</text>
                    <line x1="100" y1="85" x2="100" y2="110" stroke="black" stroke-width="1" />
                    
                    <!-- Collector Line -->
                    <line x1="100" y1="110" x2="150" y2="110" stroke="black" stroke-width="1" />
                    
                    <!-- Transistor -->
                    <circle id="transistorSymbolBody" cx="170" cy="150" r="20" stroke="black" stroke-width="1" fill="#eee" />
                    <line x1="150" y1="110" x2="170" y2="130" stroke="black" stroke-width="1" /> <!-- C -->
                    <line x1="170" y1="170" x2="170" y2="200" stroke="black" stroke-width="1" /> <!-- E -->
                    <polygon points="170,200 165,190 175,190" stroke="black" stroke-width="1" fill="black" /> <!-- Emitter Arrow -->
                    <line x1="130" y1="150" x2="150" y2="150" stroke="black" stroke-width="1" /> <!-- B -->
                    <text x="175" y="125" font-size="10">C</text>
                    <text x="175" y="185" font-size="10">E</text>
                    <text x="135" y="145" font-size="10">B</text>
                    <text x="180" y="155" font-size="10" fill="black">NPN</text>

                    <!-- Emitter to Ground -->
                    <line x1="170" y1="200" x2="170" y2="230" stroke="black" stroke-width="1" />
                    <line x1="155" y1="230" x2="185" y2="230" stroke="black" stroke-width="1" />
                    <line x1="160" y1="235" x2="180" y2="235" stroke="black" stroke-width="1" />
                    <line x1="165" y1="240" x2="175" y2="240" stroke="black" stroke-width="1" />
                    <text x="175" y="235" font-size="10">GND</text>

                    <!-- RB -->
                    <line x1="130" y1="150" x2="70" y2="150" stroke="black" stroke-width="1" />
                    <rect x="40" y="142.5" width="30" height="15" stroke="black" stroke-width="1" fill="#fff"/>
                    <text x="10" y="155" font-size="10">RB</text>
                    <line x1="40" y1="150" x2="20" y2="150" stroke="black" stroke-width="1" />

                    <!-- Vin -->
                    <circle cx="20" cy="170" r="15" stroke="black" stroke-width="1" fill="#fff" />
                    <text x="13" y="175" font-size="10">Vin</text>
                    <line x1="20" y1="155" x2="20" y2="150" stroke="black" stroke-width="1" /> <!-- Vin to RB line -->
                    <line x1="20" y1="185" x2="20" y2="230" stroke="black" stroke-width="1" /> <!-- Vin to Ground line -->
                    <line x1="5" y1="230" x2="35" y2="230" stroke="black" stroke-width="1" /> <!-- Vin Ground bar -->
                    <text x="25" y="220" font-size="10">GND</text>
                    
                    <!-- VCE Label -->
                    <polyline points="150,100 140,100 140,210 150,210" stroke="blue" stroke-width="1" fill="none" />
                    <text x="115" y="155" font-size="10" fill="blue">VCE</text>
                    <text x="150" y="95" font-size="10" fill="blue">+</text>
                    <text x="150" y="220" font-size="10" fill="blue">-</text>
                </svg>
                
                <div class="mode-explanation-box">
                    <p><strong>Mode Explanation:</strong> <span id="explanationText"></span></p>
                </div>
            </div>

            <div class="controls-calculations">
                <div class="controls">
                    <h2>Controls</h2>
                    <div class="control-group">
                        <label for="vinSlider">Vin: <span id="vinValue">0.00</span> V</label>
                        <input type="range" id="vinSlider" min="0" max="5" step="0.01" value="0">
                    </div>
                    <div class="control-group">
                        <label for="vccInput">VCC (Supply):</label>
                        <input type="number" id="vccInput" min="0.1" step="0.1" value="10"> V
                    </div>
                    <div class="control-group">
                        <label for="rcInput">RC (Collector Res):</label>
                        <input type="number" id="rcInput" min="1" step="10" value="1000"> &Omega;
                    </div>
                    <div class="control-group">
                        <label for="rbInput">RB (Base Res):</label>
                        <input type="number" id="rbInput" min="1" step="100" value="10000"> &Omega;
                    </div>
                </div>

                <div class="calculations">
                    <h2>Calculated Values</h2>
                    <p>Base Current (IB): <span id="ibValue">0.000</span> mA</p>
                    <p>Collector Current (IC): <span id="icValue">0.000</span> mA</p>
                    <p>Collector-Emitter Voltage (VCE): <span id="vceValue">0.00</span> V</p>
                    <p>Transistor Mode: <strong id="transistorMode">Cutoff</strong></p>
                </div>

                <div class="equations-display">
                    <h2>Equations Used (Simplified)</h2>
                    <pre id="equationsText">
Constants (typical):
VBE_on (Base-Emitter Turn-on) ≈ 0.7V
VCE_sat (Collector-Emitter Saturation) ≈ 0.2V
β (Beta / hFE - DC Current Gain) ≈ 100

Base Current (IB):
If Vin < VBE_on: IB ≈ 0
If Vin ≥ VBE_on: IB = (Vin - VBE_on) / RB

Collector Current (IC):
In Cutoff: IC ≈ 0
In Active Mode: IC = β * IB
In Saturation: IC ≈ (VCC - VCE_sat) / RC (limited by external circuit)

Collector-Emitter Voltage (VCE):
In Cutoff: VCE ≈ VCC
In Active Mode: VCE = VCC - (IC * RC)
In Saturation: VCE ≈ VCE_sat
</pre>
                </div>
            </div>
        </div>
    </div>

    <script>
        const vinSlider = document.getElementById('vinSlider');
        const vinValueDisplay = document.getElementById('vinValue');
        const vccInput = document.getElementById('vccInput');
        const rcInput = document.getElementById('rcInput');
        const rbInput = document.getElementById('rbInput');

        const ibValueDisplay = document.getElementById('ibValue');
        const icValueDisplay = document.getElementById('icValue');
        const vceValueDisplay = document.getElementById('vceValue');
        const transistorModeDisplay = document.getElementById('transistorMode');
        const transistorSymbolBody = document.getElementById('transistorSymbolBody');
        const explanationTextDisplay = document.getElementById('explanationText');

        // Transistor parameters
        const VBE_on = 0.7; // Volts
        const VCE_sat = 0.2; // Volts
        const Beta = 100;    // DC current gain (hFE)

        const modeColors = {
            Cutoff: "#cccccc", // Gray
            Active: "#90ee90", // Light Green
            Saturation: "#ff7f7f" // Light Coral/Red
        };
        
        const modeExplanations = {
            Cutoff: "In cutoff mode, Vin is too low to turn on the base-emitter junction (Vin < VBE_on). Thus, IB is approximately 0, which means very little to no current flows from collector to emitter (IC ≈ 0). The transistor acts like an open switch, and VCE is approximately equal to VCC.",
            Active: "In active mode, the base-emitter junction is forward-biased (Vin > VBE_on), and the collector-base junction is reverse-biased. The collector current (IC) is proportionally controlled by the base current (IB), following IC = β * IB. The transistor acts as a current amplifier. VCE is between VCE_sat and VCC.",
            Saturation: "In saturation mode, both the base-emitter and base-collector junctions are effectively forward-biased. An increase in IB no longer results in a proportional increase in IC. IC is limited by the external circuit (RC and VCC), approximately IC = (VCC - VCE_sat) / RC. The transistor acts like a closed switch, with VCE close to VCE_sat (typically ~0.2V)."
        };

        function updateCircuit() {
            const Vin = parseFloat(vinSlider.value);
            const VCC = parseFloat(vccInput.value);
            const RC = parseFloat(rcInput.value);
            const RB = parseFloat(rbInput.value);

            vinValueDisplay.textContent = Vin.toFixed(2);

            let IB = 0, IC = 0, VCE = VCC;
            let mode = "Cutoff";

            if (isNaN(VCC) || isNaN(RC) || isNaN(RB) || RC <= 0 || RB <= 0 || VCC < 0) {
                // Handle invalid inputs for VCC, RC, RB by showing error or default state
                ibValueDisplay.textContent = "Err";
                icValueDisplay.textContent = "Err";
                vceValueDisplay.textContent = "Err";
                transistorModeDisplay.textContent = "Error";
                explanationTextDisplay.textContent = "Invalid VCC, RC, or RB values. Please ensure they are positive numbers and resistors are greater than 0.";
                transistorSymbolBody.style.fill = "#000000"; // Black for error
                return;
            }
            
            if (Vin < VBE_on) {
                mode = "Cutoff";
                IB = 0;
                IC = 0;
                VCE = VCC;
            } else {
                // Potential for Active or Saturation
                IB = (Vin - VBE_on) / RB;
                if (IB < 0) IB = 0; // Should not happen if Vin >= VBE_on

                let IC_active = Beta * IB;
                let VCE_test = VCC - (IC_active * RC);

                if (VCE_test < VCE_sat) {
                    mode = "Saturation";
                    VCE = VCE_sat;
                    IC = (VCC - VCE_sat) / RC;
                    if (IC < 0) IC = 0; // Handles VCC < VCE_sat case
                } else {
                    mode = "Active";
                    IC = IC_active;
                    VCE = VCE_test;
                }
            }
            
            // Clamp VCE just in case, though logic should handle it.
            VCE = Math.max(VCE_sat, VCE); // VCE shouldn't be below VCE_sat (unless in cutoff where it can be VCC)
            if (mode === "Cutoff") {
                VCE = VCC; // In cutoff, VCE is VCC
            } else {
                 VCE = Math.min(VCC, VCE); // VCE shouldn't exceed VCC
            }
            if (VCE < 0 && mode !== "Cutoff") VCE = VCE_sat; // Final check if VCE went negative

            // Ensure IC is not negative
            IC = Math.max(0, IC);

            // Display values
            // Currents in mA (Amps * 1000)
            ibValueDisplay.textContent = (IB * 1000).toFixed(3);
            icValueDisplay.textContent = (IC * 1000).toFixed(3);
            vceValueDisplay.textContent = VCE.toFixed(2);
            transistorModeDisplay.textContent = mode;
            
            // Update transistor symbol color and explanation
            transistorSymbolBody.style.fill = modeColors[mode];
            explanationTextDisplay.textContent = modeExplanations[mode];
        }

        vinSlider.addEventListener('input', updateCircuit);
        vccInput.addEventListener('input', updateCircuit);
        rcInput.addEventListener('input', updateCircuit);
        rbInput.addEventListener('input', updateCircuit);

        // Initial calculation on page load
        updateCircuit();
    </script>
</body>
</html>
