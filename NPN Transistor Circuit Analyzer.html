<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NPN Transistor Circuit Simulator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .app-container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 700px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-top: 0;
        }
        h2 {
            margin-top: 0;
            color: #555;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }

        .schematic-container {
            border: 1px solid #ccc;
            padding: 15px;
            border-radius: 5px;
        }

        .schematic-display {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            padding: 10px 0;
            border: 1px solid #ddd;
            background: #f9f9f9;
            border-radius: 4px;
            font-size: 0.9em; 
            width: 100%; 
            max-width: 340px; 
            margin: 0 auto; 
            box-sizing: border-box;
        }
        .component {
            border: 1px solid black;
            padding: 4px 8px;
            text-align: center;
            background-color: #e9e9e9;
            border-radius: 3px;
            font-size: 0.9em; 
        }
        .wire {
            background-color: black;
        }
        .wire-v { 
            width: 2px;
            margin: 0 auto; 
        }
        .wire-h { 
            height: 2px;
        }
        
        .transistor-symbol {
            width: 50px; 
            height: 50px;
            border: 2px solid black;
            border-radius: 50%; 
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #fff;
            font-size: 0.8em; 
            font-weight: bold;
        }
        .transistor-symbol::before { /* Collector line */
            content: '';
            position: absolute;
            top: -14px; 
            left: 50%;
            transform: translateX(-50%);
            width: 2px;
            height: 15px; 
            background-color: black;
        }
        .transistor-symbol::after { /* Emitter line */
            content: '';
            position: absolute;
            bottom: -14px; 
            left: 50%;
            transform: translateX(-50%);
            width: 2px;
            height: 15px;
            background-color: black;
        }
        .emitter-arrow {
            position: absolute;
            bottom: -14px; 
            left: 50%;
            transform: translateX(-50%) translateY(100%) rotate(45deg); 
            width: 0;
            height: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 7px solid black; 
        }
        .base-connection { 
            position: absolute;
            left: -14px; 
            top: 50%;
            transform: translateY(-50%);
            width: 15px; 
            height: 2px;
            background-color: black;
        }

        .controls-container, .outputs-container, .equations-container {
            border: 1px solid #eee;
            padding: 15px;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .controls-container label {
            margin-right: 10px;
            vertical-align: middle;
        }
        .controls-container input[type="range"] {
            width: 60%;
            max-width: 300px;
            vertical-align: middle;
        }
        #vinValue {
            font-weight: bold;
            min-width: 55px; 
            display: inline-block;
            text-align: right;
        }

        .outputs-container p {
            margin: 8px 0;
            font-size: 1.0em;
        }
        .outputs-container span {
            font-weight: bold;
        }
        #vceValue, #voutValue { color: #007bff; }
        #transistorMode {
            padding: 3px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        .mode-cutoff { background-color: #ffdddd; color: #c00; }
        .mode-active { background-color: #ddffdd; color: #080; }
        .mode-saturation { background-color: #ddddff; color: #00c; }

        .equations-container textarea {
            width: 100%;
            box-sizing: border-box;
            min-height: 200px; /* Increased height */
            background-color: #eef;
            border: 1px solid #ccd;
            font-family: monospace;
            font-size: 0.9em;
            padding: 10px;
            resize: vertical;
            white-space: pre; /* Preserve formatting */
        }

        /* Responsive adjustments */
        @media (max-width: 600px) {
            body { padding: 10px; }
            .app-container { padding: 15px; }
            h1 { font-size: 1.4em; }
            .controls-container input[type="range"] {
                width: 50%;
            }
        }
         @media (max-width: 400px) {
            .controls-container label { display: block; margin-bottom: 5px;}
            .controls-container input[type="range"] { width: 100%; max-width:none; }
            #vinValue { text-align: left; margin-left: 0px;} /* Align with slider */
            .schematic-display { font-size: 0.8em; } 
            .component { padding: 3px 5px; }
            .transistor-symbol { width: 40px; height: 40px; }
            .transistor-symbol::before { top: -11px; height: 12px; }
            .transistor-symbol::after { bottom: -11px; height: 12px; }
            .emitter-arrow { bottom: -11px; border-left-width:4px; border-right-width:4px; border-top-width:6px;}
            .base-connection { left: -11px; width: 12px; }
        }
    </style>
</head>
<body>
    <h1>NPN Transistor Circuit Analyzer</h1>

    <div class="app-container">
        <div class="schematic-container">
            <h2>Circuit Diagram</h2>
            <div class="schematic-display">
                <!-- Row 1: VCC -->
                <div style="text-align: center;">VCC (+5V)</div>
                <div class="wire wire-v" style="height: 15px;"></div>
                
                <!-- Row 2: RC -->
                <div class="component" style="margin: 0 auto;">RC (1kΩ)</div>
                
                <!-- Row 3: Collector Node (Vout) and connection to Transistor Collector -->
                <div style="display: flex; flex-direction: column; align-items: center; position: relative;">
                    <div class="wire wire-v" style="height: 15px; margin-top: -1px;"></div>
                    <div style="position: absolute; left: calc(50% + 8px); top: -2px; color: blue; font-weight: bold; font-size: 0.9em;" id="schematic-vout-label-text">Vout</div>
                </div>

                <!-- Row 4: Transistor and Base Circuit -->
                <div style="display: flex; justify-content: center; align-items: center; width: 100%; margin-top: -2px;"> 
                    <div style="display: flex; align-items: center; margin-right: 0px;">
                        <div style="font-weight: bold; margin-right: 3px; font-size: 0.9em;">Vin</div>
                        <div class="wire wire-h" style="width: 10px;"></div>
                        <div class="component" style="margin-right: 3px;">RB (10kΩ)</div>
                        <div class="wire wire-h" style="width: 10px;"></div>
                    </div>
                    
                    <div class="transistor-symbol" style="margin: 0;"> 
                        NPN
                        <div class="base-connection"></div>
                        <div class="emitter-arrow"></div>
                        <div id="schematic-vce-label-text" 
                             style="position: absolute; font-weight: bold; font-size:0.9em; color: green;
                                    left: 100%; top: 50%; 
                                    transform: translateY(-50%) translateX(5px);
                                    white-space: nowrap;">VCE</div>
                    </div>
                </div>
                
                <!-- Row 5: Emitter to Ground -->
                <div class="wire wire-v" style="height: 15px; margin-top: -2px;"></div>
                <div style="text-align: center; font-weight: bold;">GND</div>
            </div>
        </div>

        <div class="controls-container">
            <h2>Controls</h2>
            <label for="vinSlider">Input Voltage (Vin):</label>
            <input type="range" id="vinSlider" min="0" max="5" value="0" step="0.01">
            <span id="vinValue">0.00 V</span>
        </div>

        <div class="outputs-container">
            <h2>Calculated Values</h2>
            <p>Collector-Emitter Voltage (VCE): <span id="vceValue">5.00 V</span></p>
            <p>Output Voltage (Vout at Collector): <span id="voutValue">5.00 V</span></p>
            <p>Transistor Mode: <span id="transistorMode" class="mode-cutoff">Cutoff</span></p>
        </div>

        <div class="equations-container">
            <h2>Relevant Equations</h2>
            <textarea id="equationsText" readonly>
CIRCUIT PARAMETERS:
  VCC = 5.0V
  RC = 1 kΩ (1000 Ohms)
  RB = 10 kΩ (10000 Ohms)
  Transistor Beta (β) = 100
  Base-Emitter ON Voltage (V_BE_ON) = 0.7V
  Collector-Emitter Saturation Voltage (VCE_SAT) = 0.2V

TRANSISTOR MODES & CALCULATIONS:

1. CUTOFF MODE:
   Condition: Vin ≤ V_BE_ON
   Base Current (IB) = 0 A
   Collector Current (IC) = 0 A
   VCE = VCC

2. ACTIVE MODE:
   Condition: Vin > V_BE_ON and calculated VCE > VCE_SAT
   IB = (Vin - V_BE_ON) / RB
   IC = β * IB  (Collector Current)
   VCE = VCC - (IC * RC) (Collector-Emitter Voltage)

3. SATURATION MODE:
   Condition: Vin > V_BE_ON and calculated VCE ≤ VCE_SAT
   (This means β * IB would try to make IC so large that VCE drops below VCE_SAT)
   VCE = VCE_SAT (typically 0.2V)
   IC = (VCC - VCE_SAT) / RC (Actual Collector Current in Saturation)
   Note: In saturation, the actual IC is less than β * IB.

KEY EQUATIONS (as per prompt):
  IC = β * IB       (Applies in Active Mode)
  VCE = VCC - (RC * IC) (General voltage drop across RC and Transistor)
  IC(sat) = VCC / RC  (Approximation for max saturation current if VCE_SAT=0,
                       or current when VCE is very small. More precisely,
                       IC(sat) for VCE=VCE_SAT is (VCC - VCE_SAT)/RC )
            </textarea>
        </div>
    </div>

    <script>
        // --- Constants ---
        const VCC = 5.0;        // Volts
        const RC_VAL = 1000;    // Ohms (1 kOhm)
        const RB_VAL = 10000;   // Ohms (10 kOhm)
        const BETA = 100;
        const V_BE_ON = 0.7;  // Volts
        const VCE_SAT = 0.2;  // Volts

        // --- DOM Elements ---
        const vinSlider = document.getElementById('vinSlider');
        const vinValueDisplay = document.getElementById('vinValue');
        const vceValueDisplay = document.getElementById('vceValue');
        const voutValueDisplay = document.getElementById('voutValue');
        const transistorModeDisplay = document.getElementById('transistorMode');
        
        vinSlider.addEventListener('input', updateCircuit);

        vinSlider.value = "0.0"; 
        updateCircuit(); 

        function updateCircuit() {
            const vin = parseFloat(vinSlider.value);
            vinValueDisplay.textContent = vin.toFixed(2) + ' V';

            let ib = 0;
            let ic = 0;
            let vce = VCC; 
            let mode = "Cutoff";
            let modeClass = "mode-cutoff";

            if (vin <= V_BE_ON) {
                mode = "Cutoff";
                modeClass = "mode-cutoff";
                ib = 0;
                ic = 0;
                vce = VCC;
            } else {
                ib = (vin - V_BE_ON) / RB_VAL;
                if (ib < 0) ib = 0; 

                let ic_active_check = BETA * ib;
                let vce_active_check = VCC - (ic_active_check * RC_VAL);

                if (vce_active_check <= VCE_SAT) { 
                    mode = "Saturation";
                    modeClass = "mode-saturation";
                    vce = VCE_SAT;
                    ic = (VCC - VCE_SAT) / RC_VAL; 
                    if (ic < 0) ic = 0; 
                } else {
                    mode = "Active";
                    modeClass = "mode-active";
                    ic = ic_active_check;
                    vce = vce_active_check;
                }
            }
            
            let vout = vce; // Vout is voltage at collector, which is VCE as Emitter is grounded.

            vceValueDisplay.textContent = vce.toFixed(2) + ' V';
            voutValueDisplay.textContent = vout.toFixed(2) + ' V';
            transistorModeDisplay.textContent = mode;
            transistorModeDisplay.className = modeClass; 
        }
    </script>
</body>
</html>
