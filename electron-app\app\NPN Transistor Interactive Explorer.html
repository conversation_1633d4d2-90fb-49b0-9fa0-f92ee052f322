<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مستكشف الترانزستور NPN التفاعلي</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .interactive-diagram {
            border: 1px solid #ccc;
            padding: 20px;
            margin-bottom: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .transistor-symbol {
            width: 150px; /* مثال لحجم رسم الترانزستور */
            height: auto;
            margin-bottom: 15px;
        }
        .controls label, .outputs p {
            margin: 5px 0;
        }
        .controls input[type="range"] {
            width: 80%;
            margin-bottom: 10px;
        }
        .outputs {
            border-top: 1px dashed #eee;
            padding-top: 15px;
            width: 80%;
            text-align: right;
        }
    </style>
</head>
<body>
    <header>
        <h1>مستكشف الترانزستور NPN التفاعلي الشامل</h1>
    </header>

    <nav>
        <ul>
            <li><a href="index.html">الرئيسية</a></li>
            <li><a href="simulation.html">بدء المحاكاة</a></li>
            <li><a href="NPN Transistor Behavior Explorer.html">سلوك NPN</a></li>
            <li><a href="NPN BJT Operating Modes Explorer.html">أوضاع تشغيل NPN</a></li>
        </ul>
    </nav>

    <main>
        <section id="explorer-intro">
            <h2>مقدمة للمستكشف التفاعلي</h2>
            <p>هذه التجربة تجمع بين مفاهيم سلوك الترانزستور NPN وأوضاع تشغيله في واجهة واحدة. يمكنك التحكم في معلمات الدائرة المختلفة ورؤية التأثير المباشر على تيارات وجهود الترانزستور، بالإضافة إلى تحديد منطقة عمله.</p>
        </section>

        <section id="circuit-diagram-workbench-interactive-npn" style="padding: 20px; margin-bottom: 20px; border: 1px solid #007bff; background-color: #f0f8ff; border-radius: 8px;">
            <h2>رسم الدائرة التفاعلية ومنطقة العمل</h2>
            <div class="circuit-diagram-container" style="text-align: center; margin-bottom: 15px;">
                <p><strong>رسم الدائرة لهذه التجربة:</strong></p>
                <!-- سيتم استبدال هذا برسم SVG أو صورة للدائرة لاحقًا -->
                <img src="images/placeholder_circuit_npn_interactive.png" alt="رسم دائرة المستكشف التفاعلي NPN" style="max-width: 450px; border: 1px solid #ccc; padding: 10px; margin-bottom: 10px;">
            </div>
            <div class="workbench-container" style="border: 1px solid #0056b3; padding: 15px; margin-top: 15px; background-color: #e7f3fe;">
                <p><strong>منطقة العمل (Workbench):</strong></p>
                <p><em>(هنا يمكنك بناء وتعديل الدائرة المستخدمة في المحاكاة أدناه)</em></p>
                <div>
                    <label for="component-select-interactive-npn">اختر مكونًا:</label>
                    <select id="component-select-interactive-npn">
                        <option value="resistor">مقاومة</option>
                        <option value="transistor_npn">ترانزستور NPN</option>
                        <option value="voltage_source_dc">مصدر جهد مستمر</option>
                        <option value="ground">أرضي</option>
                    </select>
                    <button id="addComponentButtonNpnInteractive">إضافة المكون</button>
                </div>
                <div id="interactive-circuit-drawing-area" style="width: 100%; height: 250px; background-color: #e9ecef; margin-top:10px; border:1px dashed #adb5bd; display:flex; flex-direction: column; align-items:center; justify-content:center;">
                    <p>مساحة رسم الدائرة التفاعلية (قيد التطوير)</p>
                </div>
            </div>
        </section>

        <section class="interactive-diagram">
            <h2>الرسم التفاعلي لدائرة الترانزستور NPN</h2>
            
            <!-- يمكن إضافة رسم SVG تفاعلي هنا لاحقاً -->
            <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/7/70/NPN_BJT_symbol.svg/100px-NPN_BJT_symbol.svg.png" alt="رمز الترانزستور NPN" class="transistor-symbol">
            
            <div class="controls">
                <h3>عناصر التحكم بالدائرة:</h3>
                <div>
                    <label for="vcc-supply">جهد المصدر (V<sub>CC</sub>): <span id="vcc-val">10</span> V</label>
                    <input type="range" id="vcc-supply" min="1" max="20" step="1" value="10">
                </div>
                <div>
                    <label for="rb-resistor">مقاومة القاعدة (R<sub>B</sub>): <span id="rb-val">10</span> kΩ</label>
                    <input type="range" id="rb-resistor" min="1" max="100" step="1" value="10">
                </div>
                <div>
                    <label for="rc-resistor">مقاومة المجمع (R<sub>C</sub>): <span id="rc-val">1</span> kΩ</label>
                    <input type="range" id="rc-resistor" min="0.1" max="10" step="0.1" value="1">
                </div>
                <div>
                    <label for="vin-base">جهد الدخل للقاعدة (V<sub>in</sub>): <span id="vin-val">0.7</span> V</label>
                    <input type="range" id="vin-base" min="0" max="5" step="0.05" value="0.7">
                </div>
            </div>

            <div class="outputs">
                <h3>مخرجات المحاكاة:</h3>
                <p>جهد القاعدة-الباعث (V<sub>BE</sub>): <span id="vbe-out">--</span> V</p>
                <p>تيار القاعدة (I<sub>B</sub>): <span id="ib-out">--</span> µA</p>
                <p>تيار المجمع (I<sub>C</sub>): <span id="ic-out">--</span> mA</p>
                <p>جهد المجمع-الباعث (V<sub>CE</sub>): <span id="vce-out">--</span> V</p>
                <p><strong>منطقة العمل: <span id="op-region-out">--</span></strong></p>
            </div>
        </section>

        <section id="theory-recap">
            <h2>ملخص نظري سريع</h2>
            <p>تذكر أن سلوك الترانزستور NPN يعتمد على حالة الانحياز لوصلتي القاعدة-الباعث (BEJ) والمجمع-القاعدة (CBJ).</p>
            <ul>
                <li><strong>القطع:</strong> BEJ و CBJ منحازتان عكسياً (أو BEJ غير موصلة كفاية).</li>
                <li><strong>الفعال:</strong> BEJ منحازة أمامياً، CBJ منحازة عكسياً. (I<sub>C</sub> = β * I<sub>B</sub>)</li>
                <li><strong>التشبع:</strong> BEJ و CBJ منحازتان أمامياً. (V<sub>CE</sub> ≈ V<sub>CE,sat</sub>)</li>
            </ul>
            <p>المعلمات مثل β (كسب التيار)، V<sub>BE,on</sub> (جهد تشغيل القاعدة-الباعث)، و V<sub>CE,sat</sub> (جهد تشبع المجمع-الباعث) هي خصائص للترانزستور نفسه.</p>
        </section>

        <section id="experiment-procedure-interactive">
            <h2>الخطوات الأساسية لإجراء التجربة</h2>
            <ol>
                <li><strong>فهم الهدف من التجربة:</strong> استكشاف كيف تتغير تيارات وجهود الترانزستور NPN ومنطقة عمله بتغيير معلمات الدائرة (V<sub>CC</sub>, R<sub>B</sub>, R<sub>C</sub>, V<sub>in</sub>).</li>
                <li><strong>التعرف على واجهة المحاكاة:</strong> فهم دور كل منزلق تحكم وكيف يؤثر على قيم المخرجات (V<sub>BE</sub>, I<sub>B</sub>, I<sub>C</sub>, V<sub>CE</sub>) ومنطقة العمل.</li>
                <li><strong>تأثير جهد الدخل (V<sub>in</sub>):</strong>
                    <ul>
                        <li>ثبت قيم V<sub>CC</sub>, R<sub>B</sub>, R<sub>C</sub> (مثلاً: V<sub>CC</sub>=10V, R<sub>B</sub>=10kΩ, R<sub>C</sub>=1kΩ).
                        <li>ابدأ بـ V<sub>in</sub> = 0V ولاحظ أن الترانزستور في وضع القطع.
                        <li>زد V<sub>in</sub> تدريجياً. لاحظ متى يبدأ الترانزستور في التوصيل (عادة عندما V<sub>in</sub> > 0.7V).
                        <li>استمر في زيادة V<sub>in</sub> ولاحظ كيف ينتقل الترانزستور من القطع إلى الفعال ثم إلى التشبع.
                        <li>سجل ملاحظاتك وقيم V<sub>in</sub> التي تحدث عندها هذه الانتقالات.</li>
                    </ul>
                </li>
                <li><strong>تأثير مقاومة القاعدة (R<sub>B</sub>):</strong>
                    <ul>
                        <li>ثبت V<sub>CC</sub>, R<sub>C</sub>, V<sub>in</sub> (اختر V<sub>in</sub> بحيث يكون الترانزستور في الوضع الفعال، مثلاً V<sub>in</sub>=1.5V).
                        <li>غيّر قيمة R<sub>B</sub> (مثلاً من 1kΩ إلى 50kΩ). لاحظ كيف يؤثر ذلك على I<sub>B</sub> وبالتالي على I<sub>C</sub> و V<sub>CE</sub> ومنطقة العمل.
                        <li>هل زيادة R<sub>B</sub> تدفع الترانزستور نحو القطع؟ وهل تقليلها يدفعه نحو التشبع؟ سجل ملاحظاتك.</li>
                    </ul>
                </li>
                <li><strong>تأثير مقاومة المجمع (R<sub>C</sub>):</strong>
                    <ul>
                        <li>ثبت V<sub>CC</sub>, R<sub>B</sub>, V<sub>in</sub> (اختر القيم بحيث يكون الترانزستور في الوضع الفعال).
                        <li>غيّر قيمة R<sub>C</sub> (مثلاً من 0.5kΩ إلى 5kΩ). لاحظ كيف يؤثر ذلك على V<sub>CE</sub> و I<sub>C</sub> (إذا كان في التشبع) ومنطقة العمل.
                        <li>هل زيادة R<sub>C</sub> تدفع الترانزستور نحو التشبع (لتيار معين)؟ سجل ملاحظاتك.</li>
                    </ul>
                </li>
                <li><strong>تأثير جهد المصدر (V<sub>CC</sub>):</strong>
                    <ul>
                        <li>ثبت R<sub>B</sub>, R<sub>C</sub>, V<sub>in</sub> (اختر القيم بحيث يكون الترانزستور في الوضع الفعال أو التشبع).
                        <li>غيّر قيمة V<sub>CC</sub>. لاحظ كيف يؤثر ذلك على V<sub>CE</sub> وعلى الحد الأقصى لـ I<sub>C</sub> في التشبع.
                        <li>سجل ملاحظاتك.</li>
                    </ul>
                </li>
                <li><strong>تسجيل البيانات:</strong> قم بتعبئة الجداول المقترحة أدناه بتغيير معلمة واحدة في كل مرة مع تثبيت البقية.</li>
            </ol>
        </section>

        <section id="report-writing-interactive">
            <h2>كتابة تقرير التجربة</h2>
            <p>يهدف التقرير إلى تلخيص فهمك لكيفية تأثير معلمات الدائرة المختلفة على سلوك الترانزستور NPN. يجب أن يتضمن التقرير العناصر التالية:</p>
            <ul>
                <li><strong>مقدمة:</strong> وصف موجز للترانزستور NPN وأهمية فهم سلوكه في الدوائر.</li>
                <li><strong>الأدوات المستخدمة:</strong> ذكر أن التجربة تمت باستخدام "مستكشف الترانزستور NPN التفاعلي".</li>
                <li><strong>الخطوات المتبعة:</strong> تلخيص للخطوات التي قمت بها عند تغيير كل معلمة.</li>
                <li><strong>النتائج والملاحظات:</strong> عرض البيانات التي جمعتها في جداول، مع ملاحظاتك حول تأثير كل معلمة.</li>
                <li><strong>تحليل النتائج:</strong> شرح كيف تتوافق النتائج مع مبادئ عمل الترانزستور. ناقش كيف يؤدي تغيير كل معلمة إلى انتقال الترانزستور بين أوضاع التشغيل المختلفة.</li>
                <li><strong>الاستنتاج:</strong> تلخيص لما تعلمته عن التحكم في سلوك الترانزستور.</li>
            </ul>

            <h3>جداول مقترحة لتسجيل النتائج:</h3>
            <h4>جدول 1: تأثير تغيير V<sub>in</sub> (مع ثبات V<sub>CC</sub>, R<sub>B</sub>, R<sub>C</sub>)</h4>
            <table>
                <thead>
                    <tr>
                        <th>V<sub>in</sub> (V)</th>
                        <th>V<sub>BE</sub> (V)</th>
                        <th>I<sub>B</sub> (µA)</th>
                        <th>I<sub>C</sub> (mA)</th>
                        <th>V<sub>CE</sub> (V)</th>
                        <th>منطقة العمل</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>0.2</td><td></td><td></td><td></td><td></td><td></td><td></td>
                    </tr>
                    <tr>
                        <td>0.7</td><td></td><td></td><td></td><td></td><td></td><td></td>
                    </tr>
                    <tr>
                        <td>1.5</td><td></td><td></td><td></td><td></td><td></td><td></td>
                    </tr>
                    <tr>
                        <td>3.0</td><td></td><td></td><td></td><td></td><td></td><td></td>
                    </tr>
                    <!-- أضف المزيد من الصفوف حسب الحاجة -->
                </tbody>
            </table>

            <h4>جدول 2: تأثير تغيير R<sub>B</sub> (مع ثبات V<sub>CC</sub>, R<sub>C</sub>, V<sub>in</sub>)</h4>
            <table>
                <thead>
                    <tr>
                        <th>R<sub>B</sub> (kΩ)</th>
                        <th>V<sub>BE</sub> (V)</th>
                        <th>I<sub>B</sub> (µA)</th>
                        <th>I<sub>C</sub> (mA)</th>
                        <th>V<sub>CE</sub> (V)</th>
                        <th>منطقة العمل</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td><td></td><td></td><td></td><td></td><td></td><td></td>
                    </tr>
                    <tr>
                        <td>10</td><td></td><td></td><td></td><td></td><td></td><td></td>
                    </tr>
                    <tr>
                        <td>50</td><td></td><td></td><td></td><td></td><td></td><td></td>
                    </tr>
                    <!-- أضف المزيد من الصفوف حسب الحاجة -->
                </tbody>
            </table>
            
            <h4>جدول 3: تأثير تغيير R<sub>C</sub> (مع ثبات V<sub>CC</sub>, R<sub>B</sub>, V<sub>in</sub>)</h4>
            <table>
                <thead>
                    <tr>
                        <th>R<sub>C</sub> (kΩ)</th>
                        <th>V<sub>BE</sub> (V)</th>
                        <th>I<sub>B</sub> (µA)</th>
                        <th>I<sub>C</sub> (mA)</th>
                        <th>V<sub>CE</sub> (V)</th>
                        <th>منطقة العمل</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>0.5</td><td></td><td></td><td></td><td></td><td></td><td></td>
                    </tr>
                    <tr>
                        <td>1</td><td></td><td></td><td></td><td></td><td></td><td></td>
                    </tr>
                    <tr>
                        <td>5</td><td></td><td></td><td></td><td></td><td></td><td></td>
                    </tr>
                    <!-- أضف المزيد من الصفوف حسب الحاجة -->
                </tbody>
            </table>
        </section>

    </main>

    <footer>
        <p>&copy; 2024 معمل الترانزستور الافتراضي. جميع الحقوق محفوظة.</p>
    </footer>

    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const vccSlider = document.getElementById('vcc-supply');
            const rbSlider = document.getElementById('rb-resistor');
            const rcSlider = document.getElementById('rc-resistor');
            const vinSlider = document.getElementById('vin-base');

            const vccValSpan = document.getElementById('vcc-val');
            const rbValSpan = document.getElementById('rb-val');
            const rcValSpan = document.getElementById('rc-val');
            const vinValSpan = document.getElementById('vin-val');

            const vbeOutSpan = document.getElementById('vbe-out');
            const ibOutSpan = document.getElementById('ib-out');
            const icOutSpan = document.getElementById('ic-out');
            const vceOutSpan = document.getElementById('vce-out');
            const opRegionOutSpan = document.getElementById('op-region-out');

            // خصائص الترانزستور (قيم نموذجية)
            const BETA = 100;       // كسب التيار (hFE)
            const VBE_ON = 0.7;   // جهد تشغيل القاعدة-الباعث (فولت)
            const VCE_SAT = 0.2;  // جهد تشبع المجمع-الباعث (فولت)

            function simulateNPNCircuit() {
                let vcc = parseFloat(vccSlider.value);
                let rb_kohm = parseFloat(rbSlider.value);
                let rc_kohm = parseFloat(rcSlider.value);
                let vin = parseFloat(vinSlider.value);

                vccValSpan.textContent = vcc.toFixed(1);
                rbValSpan.textContent = rb_kohm.toFixed(1);
                rcValSpan.textContent = rc_kohm.toFixed(1);
                vinValSpan.textContent = vin.toFixed(2);

                let vbe = 0;
                let ib_uA = 0;
                let ic_mA = 0;
                let vce = vcc; // في البداية، إذا كان الترانزستور مقطوعًا
                let region = 'القطع (Cutoff)';

                if (vin > VBE_ON) {
                    vbe = VBE_ON; // نفترض أن VBE ثابت عند التشغيل
                    // حساب تيار القاعدة
                    // Vin = IB * RB + VBE
                    ib_uA = ((vin - VBE_ON) / rb_kohm) * 1000; // IB بالمايكرو أمبير
                    ib_uA = Math.max(0, ib_uA); // لا يمكن أن يكون التيار سالبًا

                    if (ib_uA > 0) {
                        // حساب تيار المجمع في المنطقة الفعالة
                        let ic_active_mA = (BETA * ib_uA) / 1000; // IC بالمللي أمبير

                        // حساب VCE إذا كان في المنطقة الفعالة
                        // VCC - IC * RC = VCE
                        let vce_active = vcc - (ic_active_mA * rc_kohm);

                        if (vce_active <= VCE_SAT) {
                            region = 'التشبع (Saturation)';
                            vce = VCE_SAT;
                            // في التشبع، IC يحدده VCC, RC, VCE_SAT
                            ic_mA = (vcc - VCE_SAT) / rc_kohm;
                            ic_mA = Math.max(0, ic_mA);
                            // يجب أن يكون تيار القاعدة كافياً لإدخال الترانزستور في التشبع
                            // Ib_sat_min = Ic_sat / BETA
                            // إذا كان Ib الفعلي أقل من Ib_sat_min، فإنه لن يكون في التشبع الكامل
                            // هذا النموذج يبسط الأمر بافتراض أنه إذا كان VCE_active <= VCE_SAT، فهو تشبع.
                        } else {
                            region = 'الفعال (Active)';
                            vce = vce_active;
                            ic_mA = ic_active_mA;
                        }
                    } else {
                        // يبقى في القطع إذا كان تيار القاعدة صفرًا حتى لو Vin > VBE_ON (نظريًا)
                        // عمليًا، إذا Vin > VBE_ON، سيكون هناك تيار قاعدة صغير جدًا على الأقل
                        // لكن حسابنا لـ ib_uA يغطي هذا
                        ic_mA = 0;
                        vce = vcc;
                        region = 'القطع (Cutoff)';
                    }
                } else {
                    // Vin <= VBE_ON
                    vbe = vin; // VBE يتبع Vin حتى يصل إلى VBE_ON
                    ib_uA = 0;
                    ic_mA = 0;
                    vce = vcc;
                    region = 'القطع (Cutoff)';
                }
                
                // ضمان أن VCE لا يمكن أن يكون أكبر من VCC أو أقل من 0 (فيزيائياً)
                vce = Math.max(0, Math.min(vcc, vce));
                // ضمان أن IC لا يمكن أن يكون سالباً
                ic_mA = Math.max(0, ic_mA);

                vbeOutSpan.textContent = vbe.toFixed(2);
                ibOutSpan.textContent = ib_uA.toFixed(2);
                icOutSpan.textContent = ic_mA.toFixed(2);
                vceOutSpan.textContent = vce.toFixed(2);
                opRegionOutSpan.textContent = region;
            }

            [vccSlider, rbSlider, rcSlider, vinSlider].forEach(slider => {
                slider.addEventListener('input', simulateNPNCircuit);
            });

            // Initial simulation run
            simulateNPNCircuit();
        });
    </script>
<script src="js/main.js" defer></script>
<script>
document.addEventListener('DOMContentLoaded', function () {
    // ... existing script for sliders and outputs ...

    const addButtonNpnInteractive = document.getElementById('addComponentButtonNpnInteractive');
    const componentSelectNpnInteractive = document.getElementById('component-select-interactive-npn');
    // Assuming the drawing area in this file will also use the generic ID for now
    // const interactiveAreaNpnInteractive = document.getElementById('interactive-circuit-drawing-area'); 

    if (addButtonNpnInteractive && componentSelectNpnInteractive) {
        addButtonNpnInteractive.addEventListener('click', function() {
            const selectedComponent = componentSelectNpnInteractive.value;
            if (typeof addComponent === 'function') {
                addComponent(selectedComponent);
            } else {
                console.error('addComponent function is not defined. Make sure js/main.js is loaded.');
            }
        });
    }
    
    // Initialize sliders and update logic from the original script if it was here
    // For example, if there was a setupSimulation() function:
    // if (typeof setupSimulation === 'function') { setupSimulation(); }
    // Or directly attach event listeners if they were here.
});
</script>
</body>
</html>
